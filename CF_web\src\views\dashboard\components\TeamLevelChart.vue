<template>
  <div class="team-level-card">
    <div class="card-header">
      <div class="header-left">
        <div class="team-icon">
          <img :src="UsersIconSvg" alt="团队绩效图标" />
        </div>
        <span class="header-title">团队绩效</span>
      </div>
      <div class="header-right">
        <div class="period-buttons">
          <button class="period-btn active">本月</button>
        </div>
      </div>
    </div>

    <div class="chart-content">
      <el-skeleton :loading="loading" :rows="4" animated>
        <template #template>
          <div v-for="i in 4" :key="i" class="level-item skeleton">
            <el-skeleton-item variant="text" style="width: 60px;" />
            <div class="item-content">
              <el-skeleton-item variant="rect" style="width: 100%; height: 20px; border-radius: 10px;" />
            </div>
            <el-skeleton-item variant="text" style="width: 40px;" />
          </div>
        </template>

        <template #default>
          <div class="level-list">
            <div v-for="(item, index) in levelData" :key="index" class="level-item">
              <div class="level-name">{{ item.level }}</div>
              <div class="level-bar-container">
                <div class="level-bar">
                  <div
                    class="bar-fill"
                    :style="{
                      width: item.percentage + '%',
                      backgroundColor: item.color
                    }"
                  ></div>
                </div>
                <div class="level-value">{{ item.count }}</div>
              </div>
            </div>
          </div>
        </template>
      </el-skeleton>
    </div>
  </div>
</template>

<script setup lang="ts">
import UsersIconSvg from "@/assets/icons/users.svg";

interface TeamLevelItem {
  level: string;
  count: number;
  percentage: number;
  color: string;
}

interface Props {
  levelData: TeamLevelItem[];
  loading?: boolean;
}

const props = defineProps<Props>();
</script>

<style lang="scss" scoped>
.team-level-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(15px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  padding: 20px;
  height: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  .header-left {
    display: flex;
    align-items: center;
    gap: 8px;

    .team-icon {
      display: flex;
      align-items: center;
      justify-content: center;

      img {
        width: 32px;
        height: 32px;
        filter: brightness(0) invert(1);
      }
    }

    .header-title {
      font-size: 16px;
      font-weight: 600;
      color: rgba(255, 255, 255, 0.95);
    }
  }

  .header-right {
    .period-buttons {
      display: flex;
      gap: 0;

      .period-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 8px 16px;
        border: none;
        background: rgba(255, 255, 255, 0.15);
        color: rgba(255, 255, 255, 0.8);
        font-size: 13px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
        border-radius: 6px;
        margin: 0 2px;

        &:hover {
          background: rgba(255, 255, 255, 0.2);
          color: rgba(255, 255, 255, 0.95);
        }

        &.active {
          padding: 10px 20px;
          font-size: 14px;
          font-weight: 600;
          color: white;
          border-radius: 8px;
          margin: 0;
          background: #fbbf24;
        }
      }
    }
  }
}

.chart-content {
  .level-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .level-item {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }

    &.skeleton {
      .item-content {
        flex: 1;
      }
    }

    .level-name {
      font-size: 16px;
      font-weight: 600;
      color: rgba(255, 255, 255, 0.95);
      min-width: 100px;
      text-align: left;
    }

    .level-bar-container {
      flex: 1;
      display: flex;
      align-items: center;
      gap: 16px;

      .level-bar {
        flex: 1;
        height: 24px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 12px;
        overflow: hidden;
        position: relative;

        .bar-fill {
          height: 100%;
          border-radius: 12px;
          transition: width 0.3s ease;
          position: relative;
        }
      }

      .level-value {
        font-size: 16px;
        font-weight: 700;
        color: rgba(255, 255, 255, 0.95);
        min-width: 60px;
        text-align: right;
      }
    }
  }
}
</style>
