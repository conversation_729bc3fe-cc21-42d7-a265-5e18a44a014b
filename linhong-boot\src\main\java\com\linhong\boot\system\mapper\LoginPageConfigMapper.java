package com.linhong.boot.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.linhong.boot.system.model.entity.LoginPageConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 登录页配置 访问层
 *
 * <AUTHOR>
 */
@Mapper
public interface LoginPageConfigMapper extends BaseMapper<LoginPageConfig> {

    /**
     * 根据配置键查询配置
     *
     * @param configKey 配置键
     * @return 配置信息
     */
    @Select("SELECT * FROM login_page_config WHERE config_key = #{configKey} AND status = 1")
    LoginPageConfig selectByConfigKey(@Param("configKey") String configKey);

    /**
     * 查询所有启用的配置
     *
     * @return 配置列表
     */
    @Select("SELECT * FROM login_page_config WHERE status = 1 ORDER BY sort_order")
    List<LoginPageConfig> selectAllActive();

    /**
     * 根据配置键更新配置值
     *
     * @param configKey   配置键
     * @param configValue 配置值
     * @param updateBy    更新人
     * @return 更新行数
     */
    int updateValueByKey(@Param("configKey") String configKey,
                        @Param("configValue") String configValue,
                        @Param("updateBy") String updateBy);
}
