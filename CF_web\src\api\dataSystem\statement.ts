import request from "@/utils/request";

const statementAPI = {
  /**
   * 上月还款排行榜
   * **/
  reportLastMonthRank(queryParams?: any) {
    return request<any>({
      url: "/api/v1/report/lastMonthRank",
      method: "get",
      params: queryParams,
    });
  },
  /**
   * 本月还款排行榜
   * **/
  reportNowMonthRank(queryParams?: any) {
    return request<any>({
      url: "/api/v1/report/NowMonthRank",
      method: "get",
      params: queryParams,
    });
  },

};
export default statementAPI;
