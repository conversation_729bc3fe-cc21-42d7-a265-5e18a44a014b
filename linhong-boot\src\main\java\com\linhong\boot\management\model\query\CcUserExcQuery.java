package com.linhong.boot.management.model.query;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.linhong.boot.common.annotation.ExcelComment;
import com.linhong.boot.management.model.entity.CcUser;

import lombok.Data;

/**
 * 案件查询对象
 */
@Data
public class CcUserExcQuery {
	
	/** 是否退案 0:否 1:是 */
	private String isTuiAn;
	
	private String fileName;

	@ExcelComment("失联查询结果")
	private String lostContactQueryResult;

	@ExcelComment("催收人或者组织")
	private String userOrDept;

	@ExcelComment("客户姓名")
	private String customerName;

	@ExcelComment("批次号")
	private List<String> batchNumber;

	@ExcelComment("客户索引号")
	private String customerIndexNumber;

	@ExcelComment("案件类型")
	private String caseType;

	@ExcelComment("委案机构")
	private String caseAgency;

	@ExcelComment("跟进状态")
	private String followUpStatus;

	@ExcelComment("委案开始日期")
	private String[] entrustStartDate;

	@ExcelComment("委案结束日期")
	private String[] entrustEndDate;

	@ExcelComment("委托金额段")
	private String[] entrustTotalAmount;

	@ExcelComment("身份证")
	private String cardholderIdNumber;

	@ExcelComment("持卡人代码")
	private String cardholderCode;

	@ExcelComment("委托时月龄分档")
	private String entrustAgeBracket;

	@ExcelComment("委托时佣金分档")
	private String entrustCommissionBracket;

	@ExcelComment("外包序号")
	private String outsourceSerialNumber;

	@ExcelComment("是否标记换单")
	private String isMarkChange;

	private List<Long> ids;
	
    /**
     * 分配功能员工id集
     */
    @JsonProperty("uIds")
    private List<Long> uIds;
    /**
     * 分配功能 需要更改的案件数量
     */
    @JsonProperty("cIdNum")
    private int cIdNum;
    
    private List<CcUser> userList;
    
    private String orderBy;
    
    private String ascOrDesc;
    
    private List<String> followUpStatusList;
    
    private String isLock;

}