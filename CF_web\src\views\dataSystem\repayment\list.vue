<template>
  <div class="app-container">
    <!-- 筛选条件卡片 -->
    <el-card shadow="never" class="repayment-filter-card">
      <div class="filter-header">
        <div class="filter-title">
          <el-icon class="filter-icon">
            <Filter />
          </el-icon>
          <span class="title-text">还款筛选条件</span>
        </div>
        <el-divider class="filter-divider" />
      </div>
      <eleForm
        id="eleForm"
        ref="eleFormRef"
        :formValue="screenValue"
        :formItemList="eleFormAllList"
        :formBox="{
          spanItem: 5,
          formContent: 24,
          operate: 24,
          operateItem: 24,
          operateGrid: 'end',
        }"
        label-position="left"
        label-width="80px"
        :operateOpen="true"
        defineTxt="搜索"
        cancelTxt="重置"
        @handleSubmit="handleQuery"
        @handleCancel="handleReset"
      />
    </el-card>

    <!-- 中间数据统计区域 -->
    <div class="data-stats-section">
      <div class="stats-content">
        <div class="tips flex-start">
          <el-icon size="16px" color="#ff0000">
            <Warning />
          </el-icon>
          <span class="ml-2">
            还款总户数：
            <text class="data-value">{{ total }}</text>
            ；
          </span>
          <span>
            还款总金额：
            <text class="data-value">{{ sumEntrustTotalAmount }}</text>
            ；
          </span>
          <span>
            勾选户数：
            <text class="data-value">{{ ids.length }} 户</text>
            ；
          </span>
          <span>
            勾选金额：
            <text class="data-value">{{ checkTotal }}</text>
            ；
          </span>
        </div>

        <div class="action-buttons-right">
          <el-button
            v-hasPerm="['sys:repayment:delete']"
            class="delete-button"
            :disabled="ids.length === 0"
            @click="deleteAllocation"
          >
            <el-icon><Delete /></el-icon>
            删除
          </el-button>
        </div>
      </div>
    </div>

    <!-- 表格区域 -->
    <el-card shadow="never" class="table-card">
      <el-table
        ref="dataTableRef"
        v-loading="loading"
        :data="roleList"
        highlight-current-row
        border
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column
          v-for="item in listFields"
          :key="item.prop"
          :fixed="item.fixed || false"
          :label="item.label"
          :prop="item.prop"
          :width="item.width || ''"
          min-width="120"
          sortable
          align="center"
          header-align="center"
        >
          <template v-if="item.prop == 'caseType'" #default="scope">
            {{ caseTypeName(scope.row.caseType) || "——" }}
          </template>
          <template v-if="item.prop == 'customerIndexNumber'" #default="scope">
            <el-button
              type="primary"
              size="small"
              link
              @click="handleOpenDialog(scope.row.ccId)"
            >
              {{ scope.row.customerIndexNumber || "——" }}
            </el-button>
          </template>
        </el-table-column>
        <el-table-column
          fixed="right"
          label="操作"
          width="140"
          align="center"
          header-align="center"
        >
          <template #default="scope">
            <div class="action-buttons">
              <div class="action-btn view-btn" @click="handleOpenDialog(scope.row.ccId)">
                <el-icon><View /></el-icon>
              </div>
              <div
                v-hasPerm="['sys:repayment:delete']"
                class="action-btn delete-btn"
                @click="deleteConfirm(scope.row.id)"
              >
                <el-icon><Delete /></el-icon>
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-wrapper">
        <pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          :page-sizes="[10, 20, 50, 100, 200, 500]"
          @change="handleQuery"
          @pagination="handleQuery"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: "repaymentList",
  inheritAttrs: false,
});
import eleForm from "@/components/EleComponents/ele-form.vue";
import { screenList, listFields, repaymentBatchNumberInstance } from "./fieldsSys";
import { ElMessage, ElMessageBox, ElButton, ElLoading } from "element-plus";
import { Filter, Warning, View, Delete } from "@element-plus/icons-vue";
import RoleAPI, { RolePageVO, RoleForm, RolePageQuery } from "@/api/system/role";
import dataSystemAPI from "@/api/dataSystem/pool";
import { useUserStore } from "@/store/modules/user";
import { removeEmptyValues } from "@/utils/commit";
import { useRouter } from "vue-router";
import { nextTick } from "vue";

import { $add } from "@/utils/calculate";
const router = useRouter();

const eleFormRef = ref(null);
const eleFormAllList = ref(screenList);
const loading = ref(false);

// 为还款管理页面解构独立的批次号实例
const {
  getDynamicBatchNumber: repaymentGetDynamicBatchNumber,
  clearBatchNumberCache: repaymentClearBatchNumberCache,
} = repaymentBatchNumberInstance;
const total = ref(0);
//筛选字段默认值
const screenValue: any = ref({
  operatorId: [],
});
const userStore = useUserStore();
const queryParams = reactive<RolePageQuery>({
  pageNum: 1,
  pageSize: 10,
});

// 表格数据
const roleList = ref<[]>();

const dataTableRef = ref(null);

// 选中的角色
const ids = ref([]);
//选中的总环境金额;
const checkTotal = ref(0);
//总还款金额
const sumEntrustTotalAmount = ref(0);
watch(ids, (val) => {
  let arr = dataTableRef.value.getSelectionRows();
  //计算勾选总金额;
  checkTotal.value = arr.reduce((sum, e) => $add(sum, e.repaymentAmount), 0);
});
// 详情
const handleOpenDialog = (id?: any) => {
  let search = removeEmptyValues(eleFormRef.value.getFormVlaue());
  if (search["operatorId"])
    search["userOrDept"] = search["operatorId"][search["operatorId"].length - 1] || "";
  delete search.operatorId;
  router.push({
    path: "/dataSystem/pool/poolDetails",
    query: {
      id,
      pagesType: 3,
      search: JSON.stringify({
        ...search,
        ...queryParams,
      }),
    },
  });
};
// 批量删除
const deleteAllocation = () => {
  let list = ids.value;
  if (!list.length) {
    return ElMessage({
      message: "请选择需要删除的数据",
      type: "warning",
    });
  }
  ElMessageBox.confirm("永久删除,不可恢复", "提示", {
    confirmButtonText: "确认",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    deleteRepayment(list);
  });
};
//删除
const deleteConfirm = (id: any) => {
  ElMessageBox.confirm("永久删除,不可恢复", "提示", {
    confirmButtonText: "确认",
    cancelButtonText: "取消",
    type: "warning",
    draggable: true,
  }).then(() => {
    deleteRepayment([id]);
  });
};
// 删除请求
const deleteRepayment = (ids: any) => {
  loading.value = true;
  dataSystemAPI
    .casepoolDeleteRepayment({
      ids,
    })
    .then((e) => {
      ElMessage({
        message: "删除成功",
        type: "success",
      });
      loading.value = false;
      handleQuery();
    });
};
// 查询
function handleQuery() {
  loading.value = true;
  let obj = {};
  sumEntrustTotalAmount.value = 0;
  let search = eleFormRef.value.getFormVlaue();
  for (let key in search) {
    if (search[key]) {
      if (key == "operatorId") search[key] = search[key][search[key].length - 1] || "";
      obj[key] = search[key];
    }
  }
  dataSystemAPI
    .casepoolRepaymentPage({
      ...queryParams,
      ...obj,
    })
    .then((data: any) => {
      roleList.value = data.list;
      if (data.list && data.list.length > 0) {
        sumEntrustTotalAmount.value = data.list[0].sumRepaymentAmount || 0;
      }
      total.value = data.total;
    })
    .finally(() => {
      loading.value = false;
    });
}

// 重置筛选条件
function handleReset() {
  // 重置筛选条件到初始值
  const { userId, deptId, roles } = userStore.userInfo;
  if (deptId && roles[0] != "YG") {
    screenValue.value = { operatorId: [deptId, userId] };
  } else {
    screenValue.value = { operatorId: [userId] };
  }

  // 使用nextTick确保DOM更新完成后再执行
  nextTick(() => {
    // 延迟执行，确保表单重置完成后再重新获取批次号和查询
    setTimeout(() => {
      console.log("还款管理重置：开始重新获取批次号");
      // 重新获取批次号选项
      repaymentGetDynamicBatchNumber({}, 'repayment').then(() => {
        console.log("还款管理重置：批次号获取成功");
      }).catch((error) => {
        console.warn("重置时批次号更新失败:", error);
      });

      // 重新查询
      handleQuery();
    }, 300);
  });
}

// 案件类型
const caseTypeName = computed(() => {
  return (type: any) => {
    const { options } = eleFormAllList.value.find((e: any) => e.label == "案件类型");
    let name = false;
    if (options && options.length > 0) {
      name = options.find((t: any) => t.value == type)?.label || "-";
    }
    return name;
  };
});

// 行复选框选中
function handleSelectionChange(selection: any) {
  ids.value = selection.map((item: any) => item.id);
}
// eleFormAllList.value = [...screenList];
eleFormAllList.value[0].field = "operatorId";

/**
 * 获取表格内容可用高度
 * **/
const contentHeader = ref(0);

// 获取表格高度
const getTableHeight = () => {
  const appMainH = document.querySelectorAll(".app-main")[0]?.offsetHeight;
  const searchHeight = document.querySelectorAll("#eleForm")[0]?.offsetHeight;
  const statsHeight = document.querySelectorAll(".data-stats-section")[0]?.offsetHeight;
  console.log(appMainH, searchHeight, statsHeight);
  contentHeader.value = appMainH - searchHeight - statsHeight - 130;
};

onUpdated(async () => {});
onMounted(async () => {
  const { userId, deptId, roles } = userStore.userInfo;
  if (deptId && roles[0] != "YG") {
    screenValue.value["operatorId"] = [deptId, userId];
  } else {
    screenValue.value["operatorId"] = [userId];
  }

  // 初始化还款管理页面的批次号选项，使用还款接口，传递operatorId参数
  // 按照handleQuery的逻辑处理operatorId数组
  const operatorIdArray = screenValue.value.operatorId;
  const repaymentFilterParams = {
    operatorId: operatorIdArray[operatorIdArray.length - 1] || ""
  };
  await repaymentGetDynamicBatchNumber(repaymentFilterParams, 'repayment');

  console.log(screenValue);
  setTimeout(() => {
    handleQuery();
    getTableHeight();
  }, 0);
});
</script>
<style lang="scss" scoped>
.flex-between {
  display: flex;
  justify-content: space-between;
}

/* 还款管理筛选条件样式 - 与作业清单保持一致 */
.repayment-filter-card {
  margin-bottom: 6px;
}

.repayment-filter-card .el-card__body {
  padding: 20px;
}

.filter-header {
  margin-bottom: 16px;
}

.filter-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 12px;
}

.filter-icon {
  margin-right: 8px;
  color: #409eff;
  font-size: 18px;
}

.title-text {
  color: #2c3e50;
}

.filter-divider {
  margin: 0;
  border-color: #e4e7ed;
}

/* 客户索引号链接样式 */
.customer-index-link {
  font-weight: 500;
  text-decoration: underline;
}

.customer-index-link:hover {
  color: #409eff;
}

/* 还款管理筛选条件样式 - 强制label和输入框左右排列 */
.repayment-filter-card :deep(.el-form-item) {
  display: flex !important;
  flex-direction: row !important;
  align-items: center !important;
  margin-bottom: 15px !important;
}

.repayment-filter-card :deep(.el-form-item__label) {
  width: 80px !important;
  min-width: 80px !important;
  max-width: 80px !important;
  text-align: left !important;
  font-size: 12px !important;
  font-weight: 600 !important;
  color: #2c3e50 !important;
  white-space: nowrap !important;
  margin-right: 8px !important;
  margin-bottom: 0 !important;
  padding: 0 !important;
  line-height: 32px !important;
  position: relative !important;
}

/* 为没有星号的标签添加统一的左边距，确保对齐 */
.repayment-filter-card :deep(.el-form-item__label):not(.is-required) {
  padding-left: 11px !important; /* 11px = 星号宽度 + 间距，确保与有星号的标签对齐 */
}

/* 确保有星号的标签正常显示 */
.repayment-filter-card :deep(.el-form-item__label.is-required) {
  padding-left: 0 !important;
}

.repayment-filter-card :deep(.el-form-item__content) {
  flex: 1 !important;
  max-width: none !important;
  margin-left: 0 !important;
  width: auto !important;
}

/* 使用与作业清单一致的简洁样式 */
.repayment-filter-card :deep(.el-input__wrapper) {
  height: 32px !important;
  font-size: 12px !important;
  border: 1px solid #d1d5db !important;
  border-radius: 4px !important;
  background-color: #ffffff !important;
}

.repayment-filter-card :deep(.el-select .el-input__wrapper) {
  height: 32px !important;
  font-size: 12px !important;
  border: 1px solid #d1d5db !important;
  border-radius: 4px !important;
  background-color: #ffffff !important;
}

.repayment-filter-card :deep(.el-date-editor .el-input__wrapper) {
  height: 32px !important;
  font-size: 12px !important;
  border: 1px solid #d1d5db !important;
  border-radius: 4px !important;
  background-color: #ffffff !important;
}

.repayment-filter-card :deep(.el-cascader .el-input__wrapper) {
  height: 32px !important;
  font-size: 12px !important;
  border: 1px solid #d1d5db !important;
  border-radius: 4px !important;
  background-color: #ffffff !important;
}

/* 操作按钮样式 - 与作业清单保持一致 */
.repayment-filter-card :deep(.operate-box) {
  display: flex !important;
  gap: 12px !important;
  align-items: center !important;
  justify-content: flex-end !important;
  width: 100% !important;
  padding: 0 !important;
}

.repayment-filter-card :deep(.operate-box .el-button) {
  height: 32px !important;
  font-size: 12px !important;
  padding: 8px 20px !important;
  border-radius: 4px !important;
  min-width: 70px !important;
  font-weight: 500 !important;
}

.repayment-filter-card :deep(.operate-box .el-button--primary) {
  background-color: #409eff !important;
  border-color: #409eff !important;
  color: #ffffff !important;
}

.repayment-filter-card :deep(.operate-box .el-button--default) {
  background-color: #ffffff !important;
  border-color: #dcdfe6 !important;
  color: #606266 !important;
}

/* 客户索引号字段宽度调整 */
.repayment-filter-card :deep(.el-form-item):nth-child(4) {
  flex: 0 0 calc(25% + 50px) !important; /* 客户索引号字段比其他字段宽50px */
  max-width: calc(25% + 50px) !important;
}

.repayment-filter-card :deep(.el-form-item):nth-child(4) .el-input__wrapper {
  min-width: 200px !important; /* 客户索引号输入框最小宽度200px */
  width: 100% !important;
}

/* 响应式调整 - 不同分辨率下的宽度优化 */
@media (max-width: 1600px) {
  .repayment-filter-card :deep(.el-form-item):nth-child(4) {
    flex: 0 0 calc(25% + 40px) !important;
    max-width: calc(25% + 40px) !important;
  }

  .repayment-filter-card :deep(.el-form-item):nth-child(4) .el-input__wrapper {
    min-width: 180px !important;
  }
}

@media (max-width: 1366px) {
  .repayment-filter-card :deep(.el-form-item):nth-child(4) {
    flex: 0 0 calc(25% + 30px) !important;
    max-width: calc(25% + 30px) !important;
  }

  .repayment-filter-card :deep(.el-form-item):nth-child(4) .el-input__wrapper {
    min-width: 160px !important;
  }
}

@media (max-width: 1200px) {
  .repayment-filter-card :deep(.el-form-item):nth-child(4) {
    flex: 0 0 calc(25% + 20px) !important;
    max-width: calc(25% + 20px) !important;
  }

  .repayment-filter-card :deep(.el-form-item):nth-child(4) .el-input__wrapper {
    min-width: 140px !important;
  }
}

.flex-center {
  display: flex;
  justify-content: center;
}

.flex-start {
  display: flex;
  align-items: center;
}

.tips {
  span {
    margin-right: 2px;
  }

  .data-value {
    font-weight: bold;
    color: #000;
  }
}

// 筛选条件卡片样式
.filter-card {
  margin-bottom: 12px;
  border-radius: 12px;
  background: #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
  max-height: 200px;
  overflow: hidden;

  :deep(.el-card__body) {
    padding: 12px;
    max-height: 160px;
    overflow-y: auto;
  }
}

// 选项卡样式
.filter-tabs {
  :deep(.el-tabs__header) {
    margin: 0;
    background-color: #f5f7fb;
    padding: 0 20px;

    .el-tabs__nav-wrap {
      &::after {
        display: none;
      }
    }

    .el-tabs__item {
      color: #666;
      font-weight: 500;

      &.is-active {
        color: #409eff;
        font-weight: bold;
      }
    }
  }

  :deep(.el-tabs__content) {
    padding: 20px;
  }
}

// 选项卡标签样式
.tab-label {
  display: flex;
  align-items: center;
  gap: 8px;

  .filter-icon {
    font-size: 16px;
    color: #4a90e2;
  }

  .filter-text {
    font-weight: 600;
    color: #4a90e2;
    font-size: 14px;
  }
}

// 数据统计区域样式
.data-stats-section {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f9fbfd;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .stats-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .action-buttons-right {
    display: flex;
    gap: 12px;
  }

  .delete-button {
    background: linear-gradient(135deg, #ff4757 0%, #ff3742 100%);
    border: none;
    border-radius: 6px;
    color: white;
    padding: 10px 20px;
    font-weight: 600;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.2s ease;
    box-shadow: 0 2px 8px rgba(255, 71, 87, 0.3);
    min-width: 100px;
    justify-content: center;

    &:hover:not(:disabled) {
      background: linear-gradient(135deg, #ff3742 0%, #ff2d3a 100%);
      transform: translateY(-2px);
      box-shadow: 0 6px 16px rgba(255, 71, 87, 0.4);
    }

    &:active:not(:disabled) {
      transform: translateY(0);
      box-shadow: 0 2px 8px rgba(255, 71, 87, 0.3);
    }

    &:disabled {
      background: linear-gradient(135deg, #e4e7ed 0%, #d3d4d6 100%);
      color: #a8abb2;
      cursor: not-allowed;
      transform: none;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .el-icon {
      font-size: 16px;
    }
  }
}

// 表格卡片样式
.table-card {
  border-radius: 12px;
  background: #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  :deep(.el-card__body) {
    padding: 12px;
  }
}

/* 表格容器样式 - 移除固定高度，让表格自适应内容 */

// 分页组件样式
.pagination-container {
  margin-top: 8px;
  padding: 4px 0;
  flex-shrink: 0;
  display: flex;
  justify-content: center;
}

// 操作按钮样式
.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
  align-items: center;
}

.action-btn {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;

  .el-icon {
    font-size: 16px;
  }

  &:hover {
    transform: scale(1.1);
  }
}

.view-btn {
  background-color: #e3f2fd;
  color: #1976d2;

  &:hover {
    background-color: #bbdefb;
  }
}

.delete-btn {
  background-color: #ffebee;
  color: #d32f2f;

  &:hover {
    background-color: #ffcdd2;
  }
}

// 表单圆角样式
.search-bar {
  :deep(.el-input__wrapper) {
    border-radius: 8px;
  }

  :deep(.el-select .el-input__wrapper) {
    border-radius: 8px;
  }

  :deep(.el-cascader .el-input__wrapper) {
    border-radius: 8px;
  }

  :deep(.el-date-editor.el-input) {
    .el-input__wrapper {
      border-radius: 8px;
    }
  }

  :deep(.el-button) {
    border-radius: 8px;
  }
}

// 表格样式
:deep(.el-table) {
  // 表头样式 - 修改为浅灰色
  .el-table__header-wrapper {
    .el-table__header {
      th {
        background-color: #f5f7fb !important;
        color: #333 !important;
        font-weight: bold;

        .cell {
          color: #333 !important;
        }
      }
    }
  }

  // 表格数据居中已通过 align="center" 实现
}
</style>

<style scoped>
/* 还款管理页面专用紧凑样式 */
.data-stats-section {
  margin-bottom: 4px !important;
  padding: 8px 20px !important;
}

.filter-card {
  margin-bottom: 6px !important;
}
</style>
