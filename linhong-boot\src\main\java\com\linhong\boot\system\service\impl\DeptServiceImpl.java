package com.linhong.boot.system.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.linhong.boot.common.constant.SystemConstants;
import com.linhong.boot.common.enums.RoleEnum;
import com.linhong.boot.common.enums.StatusEnum;
import com.linhong.boot.common.model.Option;
import com.linhong.boot.common.model.OptionOfDept;
import com.linhong.boot.core.security.util.SecurityUtils;
import com.linhong.boot.system.converter.DeptConverter;
import com.linhong.boot.system.mapper.DeptMapper;
import com.linhong.boot.system.model.entity.Dept;
import com.linhong.boot.system.model.entity.Role;
import com.linhong.boot.system.model.entity.User;
import com.linhong.boot.system.model.form.DeptForm;
import com.linhong.boot.system.model.query.DeptQuery;
import com.linhong.boot.system.model.vo.DeptVO;
import com.linhong.boot.system.service.DeptService;
import com.linhong.boot.system.service.RoleService;
import com.linhong.boot.system.service.UserService;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 部门 业务实现类
 *
 */
@Service
@RequiredArgsConstructor
public class DeptServiceImpl extends ServiceImpl<DeptMapper, Dept> implements DeptService {

	private final DeptConverter deptConverter;

	private final UserService userService;
	
	private final RoleService roleService;

	/**
	 * 获取部门列表
	 */
	@Override
	public List<DeptVO> getDeptList(DeptQuery queryParams) {
		// 查询参数
		String keywords = queryParams.getKeywords();
		Integer status = queryParams.getStatus();
		// 查询数据
		List<Dept> deptList = this.list(new LambdaQueryWrapper<Dept>().like(StrUtil.isNotBlank(keywords), Dept::getName, keywords)
						.eq(status != null, Dept::getStatus, status).orderByAsc(Dept::getSort));

		if (CollectionUtil.isEmpty(deptList)) {
			return Collections.EMPTY_LIST;
		}

		// 获取所有部门ID
		Set<String> deptIds = deptList.stream().map(Dept::getId).collect(Collectors.toSet());
		// 获取父节点ID
		Set<String> parentIds = deptList.stream().map(Dept::getParentId).collect(Collectors.toSet());
		// 获取根节点ID（递归的起点），即父节点ID中不包含在部门ID中的节点，注意这里不能拿顶级部门 O 作为根节点，因为部门筛选的时候 O 会被过滤掉
		List<String> rootIds = CollectionUtil.subtractToList(parentIds, deptIds);
		List<DeptVO> a = rootIds.stream().flatMap(rootId -> recurDeptList(rootId, deptList).stream()).toList();
		// 递归生成部门树形列表
		return a;
	}

	/**
	 * 递归生成部门树形列表
	 *
	 * @param parentId 父ID
	 * @param deptList 部门列表
	 * @return 部门树形列表
	 */
	public List<DeptVO> recurDeptList(String parentId, List<Dept> deptList) {
		return deptList.stream().filter(dept -> dept.getParentId().equals(parentId)).map(dept -> {
			DeptVO deptVO = deptConverter.toVo(dept);
			List<DeptVO> children = recurDeptList(dept.getId(), deptList);
			deptVO.setChildren(children);
			return deptVO;
		}).toList();
	}

	/**
	 * 部门下拉选项
	 *
	 * @return 部门下拉List集合
	 */
	@Override
	public List<Option<String>> listDeptOptions() {

		List<Dept> deptList = this.list(new LambdaQueryWrapper<Dept>().eq(Dept::getStatus, StatusEnum.ENABLE.getValue())
				.select(Dept::getId, Dept::getParentId, Dept::getName).orderByAsc(Dept::getSort));
		if (CollectionUtil.isEmpty(deptList)) {
			return Collections.EMPTY_LIST;
		}

		Set<String> deptIds = deptList.stream().map(Dept::getId).collect(Collectors.toSet());

		Set<String> parentIds = deptList.stream().map(Dept::getParentId).collect(Collectors.toSet());

		List<String> rootIds = CollectionUtil.subtractToList(parentIds, deptIds);

		// 递归生成部门树形列表
		return rootIds.stream().flatMap(rootId -> recurDeptTreeOptions(rootId, deptList).stream()).toList();
	}

	@Override
	public List<OptionOfDept<String>> recurDeptTreeOptions() {
		List<Dept> deptList = this.list(new LambdaQueryWrapper<Dept>().eq(Dept::getStatus, StatusEnum.ENABLE.getValue())
				.select(Dept::getId, Dept::getParentId, Dept::getName).orderByAsc(Dept::getSort));
		if (CollectionUtil.isEmpty(deptList)) {
			return Collections.EMPTY_LIST;
		}
		// 使用一个Map来缓存已经处理过的部门ID和对应的OptionOfDept<Long>对象，避免重复计算
		Map<String, OptionOfDept<String>> cache = new HashMap<>();
		/* 如果deptId是空的, 默认为root权限用户, 查询最上级部门id */
		String deptId = SecurityUtils.getDeptId() == null ? this.baseMapper.selectMogamiDeptId(): SecurityUtils.getDeptId();
		List<OptionOfDept<String>> options = new ArrayList<>();
		
		/*添加当前登录人*/
		OptionOfDept<String> LastOption = new OptionOfDept<>();
		List<User> nu = new ArrayList<>();
		User nowUser = userService.getById(SecurityUtils.getUserId());
		Role r= roleService.selectByUserId(nowUser.getId().toString());	
		nu.add(nowUser);
		if(SecurityUtils.getDeptId()!=null) {
			if(!r.getCode().equals(RoleEnum.YG.getValue())) {
				if(deptList.get(0).getId().equals(deptId)) {
					List<User> users = userService.getUserList(deptId);
					nu.addAll(users);
				}
				// 递归函数，用于构建部门树并添加用户
				List<OptionOfDept<String>> childrenOptions = buildDeptTree(deptId, deptList, cache);
				LastOption.setChildren(childrenOptions);
				LastOption.setValue(SecurityUtils.getDeptId());
				Dept d = this.getById(SecurityUtils.getDeptId());
				LastOption.setLabel(d.getName());
			}
		}else {
			LastOption.setValue("-1");
			LastOption.setLabel("终极权限");
		}
		Collection<User> uniqueUserList = nu.stream().collect(Collectors.toMap(User::getId, Function.identity(), 
                        (existing, replacement) -> existing)).values();
		LastOption.setUsers(uniqueUserList.stream().collect(Collectors.toList()));
		options.add(LastOption);
		return options;
	}

	/**
	 * 新增部门
	 *
	 * @param formData 部门表单
	 * @return 部门ID
	 */
	@Override
	public String saveDept(DeptForm formData) {
		UUID uuid = UUID.randomUUID();
		String idStr = uuid.toString().replace("-", "");
		// 校验部门名称是否存在
		String code = formData.getCode();
		long count = this.count(new LambdaQueryWrapper<Dept>().eq(Dept::getCode, code));
		Assert.isTrue(count == 0, "部门编号已存在");

		// form->entity
		Dept entity = deptConverter.toEntity(formData);

		// 生成部门路径(tree_path)，格式：父节点tree_path + , + 父节点ID，用于删除部门时级联删除子部门
		String treePath = generateDeptTreePath(formData.getParentId());
		entity.setTreePath(treePath);

		entity.setCreateBy(SecurityUtils.getUserId());
		// 保存部门并返回部门ID
		entity.setId(idStr);
		boolean result = this.save(entity);
		Assert.isTrue(result, "部门保存失败");

		return entity.getId();
	}

	/**
	 * 获取部门表单
	 *
	 * @param deptId 部门ID
	 * @return 部门表单对象
	 */
	@Override
	public DeptForm getDeptForm(String deptId) {
		Dept entity = this.getById(deptId);
		return deptConverter.toForm(entity);
	}

	/**
	 * 更新部门
	 *
	 * @param deptId   部门ID
	 * @param formData 部门表单
	 * @return 部门ID
	 */
	@Override
	public String updateDept(String deptId, DeptForm formData) {
		// 校验部门名称/部门编号是否存在
		String code = formData.getCode();
		long count = this.count(new LambdaQueryWrapper<Dept>().ne(Dept::getId, deptId).eq(Dept::getCode, code));
		Assert.isTrue(count == 0, "部门编号已存在");

		// form->entity
		Dept entity = deptConverter.toEntity(formData);
		entity.setId(deptId);

		// 生成部门路径(tree_path)，格式：父节点tree_path + , + 父节点ID，用于删除部门时级联删除子部门
		String treePath = generateDeptTreePath(formData.getParentId());
		entity.setTreePath(treePath);

		// 保存部门并返回部门ID
		boolean result = this.updateById(entity);
		Assert.isTrue(result, "部门更新失败");

		return entity.getId();
	}

	/**
	 * 递归生成部门表格层级列表
	 *
	 * @param parentId 父ID
	 * @param deptList 部门列表
	 * @return 部门表格层级列表
	 */
	public static List<Option<String>> recurDeptTreeOptions(String parentId, List<Dept> deptList) {
		return CollectionUtil.emptyIfNull(deptList).stream().filter(dept -> dept.getParentId().equals(parentId))
				.map(dept -> {
					Option<String> option = new Option<>(dept.getId(), dept.getName());
					List<Option<String>> children = recurDeptTreeOptions(dept.getId(), deptList);
					if (CollectionUtil.isNotEmpty(children)) {
						option.setChildren(children);
					}
					return option;
				}).collect(Collectors.toList());
	}
	
	
	public List<OptionOfDept<String>> buildDeptTree(String parentId, List<Dept> deptList,Map<String, OptionOfDept<String>> cache) {
		if (cache.containsKey(parentId)) {
			// 如果缓存中已经存在这个部门ID的OptionOfDept对象，则直接返回它（包括其子部门和用户）
			return Collections.singletonList(cache.get(parentId));
		}

		List<Dept> parentDepts = deptList.stream()
				.filter(dept -> dept.getParentId().equals(parentId))
				.collect(Collectors.toList());

		// 构建这些部门的OptionOfDept对象，并递归获取子部门
		List<OptionOfDept<String>> options = parentDepts.stream().map(dept -> {
			OptionOfDept<String> option = new OptionOfDept<>(dept.getId(), dept.getName());
			// 获取属于这个部门的用户列表，并添加到OptionOfDept对象中
			List<User> users = userService.getUserList(dept.getId());
			option.setUsers(users);
			// 递归获取子部门，并添加到OptionOfDept对象中
			List<OptionOfDept<String>> children = buildDeptTree(dept.getId(), deptList, cache);
			if (CollectionUtil.isNotEmpty(children)) {
				option.setChildren(children);
			}
			cache.put(dept.getId(), option);
			return option;
		}).collect(Collectors.toList());
		
		return options;
	}

	/**
	 * 删除部门
	 *
	 * @param ids 部门ID，多个以英文逗号,拼接字符串
	 * @return 是否删除成功
	 */
	@Override
	public boolean deleteByIds(String ids) {
		// 删除部门及子部门
		if (StrUtil.isNotBlank(ids)) {
			String[] menuIds = ids.split(",");
			for (String deptId : menuIds) {
				this.remove(new LambdaUpdateWrapper<Dept>().eq(Dept::getId, deptId).or()
						.apply("CONCAT (',',tree_path,',') LIKE CONCAT('%,',{0},',%')", deptId));
			}
		}
		return true;
	}

	/**
	 * 部门路径生成
	 *
	 * @param parentId 父ID
	 * @return 父节点路径以英文逗号(, )分割，eg: 1,2,3
	 */
	private String generateDeptTreePath(String parentId) {
		String treePath = null;
		if (SystemConstants.ROOT_NODE_ID.toString().equals(parentId)) {
			treePath = parentId;
		} else {
			Dept parent = this.getById(parentId);
			if (parent != null) {
				treePath = parent.getTreePath() + "," + parent.getId();
			}
		}
		return treePath;
	}
}
