<template>
  <div class="breadcrumb-container">
    <!-- 菜单折叠按钮 -->
    <Hamburger :is-active="isSidebarOpened" @toggle-click="toggleSideBar" />

    <!-- 面包屑导航 -->
    <el-breadcrumb class="flex-y-center">
      <transition-group enter-active-class="animate__animated animate__fadeInRight">
        <el-breadcrumb-item v-for="(item, index) in breadcrumbs" :key="item.path">
          <span
            v-if="item.redirect === 'noredirect' || index === breadcrumbs.length - 1"
            class="breadcrumb-text"
          >
            {{ translateRouteTitle(item.meta.title) }}
          </span>
          <a v-else @click.prevent="handleLink(item)" class="breadcrumb-link">
            {{ translateRouteTitle(item.meta.title) }}
          </a>
        </el-breadcrumb-item>
      </transition-group>
    </el-breadcrumb>
  </div>
</template>

<script setup lang="ts">
import { RouteLocationMatched } from "vue-router";
import { compile } from "path-to-regexp";
import router from "@/router";
import { translateRouteTitle } from "@/utils/i18n";
import { useAppStore } from "@/store";
import Hamburger from "@/components/Hamburger/index.vue";

const appStore = useAppStore();

const currentRoute = useRoute();
const pathCompile = (path: string) => {
  const { params } = currentRoute;
  const toPath = compile(path);
  return toPath(params);
};

const breadcrumbs = ref<Array<RouteLocationMatched>>([]);

// 侧边栏是否打开
const isSidebarOpened = computed(() => appStore.sidebar.opened);

// 展开/收缩菜单
function toggleSideBar() {
  appStore.toggleSidebar();
}

function getBreadcrumb() {
  let matched = currentRoute.matched.filter((item) => item.meta && item.meta.title);
  const first = matched[0];
  if (!isDashboard(first)) {
    matched = [{ path: "/dashboard", meta: { title: "dashboard" } } as any].concat(matched);
  }
  breadcrumbs.value = matched.filter((item) => {
    return item.meta && item.meta.title && item.meta.breadcrumb !== false;
  });
}

function isDashboard(route: RouteLocationMatched) {
  const name = route && route.name;
  if (!name) {
    return false;
  }
  return name.toString().trim().toLocaleLowerCase() === "Dashboard".toLocaleLowerCase();
}

function handleLink(item: any) {
  const { redirect, path } = item;
  if (redirect) {
    router.push(redirect).catch((err) => {
      console.warn(err);
    });
    return;
  }
  router.push(pathCompile(path)).catch((err) => {
    console.warn(err);
  });
}

watch(
  () => currentRoute.path,
  (path) => {
    if (path.startsWith("/redirect/")) {
      return;
    }
    getBreadcrumb();
  }
);

onBeforeMount(() => {
  getBreadcrumb();
});
</script>

<style lang="scss" scoped>
.breadcrumb-container {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 0 16px;
  height: 100%;
  flex: 1;
}

// 面包屑文本样式（白色主题）
.breadcrumb-text {
  color: #ffffff !important;
  font-size: 14px;
  font-weight: 400;
}

.breadcrumb-link {
  color: #ffffff !important;
  text-decoration: none;
  font-size: 14px;
  font-weight: 400;

  &:hover {
    color: #e6f7ff !important;
    text-decoration: underline;
  }
}

// 覆盖 element-plus 的样式
.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
  color: #ffffff !important;
}

// 面包屑分隔符样式
:deep(.el-breadcrumb__separator) {
  color: #ffffff !important;
  font-weight: 400;
}

// 面包屑项样式
:deep(.el-breadcrumb__item) {
  .el-breadcrumb__inner {
    color: #ffffff !important;
    font-weight: 400;

    &:hover {
      color: #e6f7ff !important;
      text-decoration: underline;
    }
  }

  &:last-child .el-breadcrumb__inner {
    color: #ffffff !important;
    cursor: default;
    font-weight: 500; /* 当前页面稍微加粗 */

    &:hover {
      color: #ffffff !important;
      text-decoration: none;
    }
  }
}
</style>
