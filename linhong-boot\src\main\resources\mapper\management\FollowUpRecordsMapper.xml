<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.linhong.boot.management.mapper.FollowUpRecordsMapper">
  <resultMap id="BaseResultMap" type="com.linhong.boot.management.model.entity.FollowUpRecords">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Nov 26 16:39:14 HKT 2024.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="called_number" jdbcType="VARCHAR" property="calledNumber" />
    <result column="caller_number" jdbcType="VARCHAR" property="callerNumber" />
    <result column="collection_result" jdbcType="CHAR" property="collectionResult" />
    <result column="missing_contact_result" jdbcType="CHAR" property="missingContactResult" />
    <result column="repayment_amount" jdbcType="DECIMAL" property="repaymentAmount" />
    <result column="repayment_date" jdbcType="DATE" property="repaymentDate" />
    <result column="is_marked_for_change" jdbcType="BIT" property="isMarkedForChange" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="operation_id" jdbcType="BIGINT" property="operationId" />
    <result column="operation_name" jdbcType="VARCHAR" property="operationName" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.linhong.boot.management.model.entity.FollowUpRecordsWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Nov 26 16:39:14 HKT 2024.
    -->
    <result column="task_prediction" jdbcType="LONGVARCHAR" property="taskPrediction" />
    <result column="collection_notes" jdbcType="LONGVARCHAR" property="collectionNotes" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Nov 26 16:39:14 HKT 2024.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Nov 26 16:39:14 HKT 2024.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Nov 26 16:39:14 HKT 2024.
    -->
    id, user_id, called_number, caller_number, collection_result, missing_contact_result, 
    repayment_amount, repayment_date, is_marked_for_change, created_at, updated_at, operation_id, 
    operation_name
  </sql>
  <sql id="Blob_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Nov 26 16:39:14 HKT 2024.
    -->
    task_prediction, collection_notes
  </sql>
  <select id="TaskUnFollowDay" resultType="com.linhong.boot.management.model.entity.FollowUpRecords">
  	select user_id AS userId, TIMESTAMPDIFF(HOUR, t.maxtime, NOW()) AS unfollowedHour from (
		SELECT user_id , max(created_at) maxtime
		FROM `follow_up_records` GROUP BY user_id
	) t     
  </select>
  
  
  <select id="selectByExampleWithBLOBs" parameterType="com.linhong.boot.management.model.entity.FollowUpRecordsExample" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Nov 26 16:39:14 HKT 2024.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from follow_up_records
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.linhong.boot.management.model.entity.FollowUpRecordsExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Nov 26 16:39:14 HKT 2024.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from follow_up_records
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Nov 26 16:39:14 HKT 2024.
    -->
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from follow_up_records
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Nov 26 16:39:14 HKT 2024.
    -->
    delete from follow_up_records
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.linhong.boot.management.model.entity.FollowUpRecordsExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Nov 26 16:39:14 HKT 2024.
    -->
    delete from follow_up_records
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  
  <insert id="insert" parameterType="com.linhong.boot.management.model.entity.FollowUpRecords">
    
    insert into follow_up_records (id, user_id, called_number, 
      caller_number, collection_result, missing_contact_result, 
      repayment_amount, repayment_date, is_marked_for_change, 
      operation_id, 
      operation_name, task_prediction, collection_notes
      )
    values (#{id,jdbcType=BIGINT}, #{userId,jdbcType=BIGINT}, #{calledNumber,jdbcType=VARCHAR}, 
      #{callerNumber,jdbcType=VARCHAR}, #{collectionResult,jdbcType=CHAR}, #{missingContactResult,jdbcType=CHAR}, 
      #{repaymentAmount,jdbcType=DECIMAL}, #{repaymentDate,jdbcType=DATE}, #{isMarkedForChange,jdbcType=BIT}, 
      #{operationId,jdbcType=BIGINT}, 
      #{operationName,jdbcType=VARCHAR}, #{taskPrediction,jdbcType=LONGVARCHAR}, #{collectionNotes,jdbcType=LONGVARCHAR}
      )
  </insert> 
  
  <insert id="insertSelective" parameterType="com.linhong.boot.management.model.entity.FollowUpRecords">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Nov 26 16:39:14 HKT 2024.
    -->
    insert into follow_up_records
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="calledNumber != null">
        called_number,
      </if>
      <if test="callerNumber != null">
        caller_number,
      </if>
      <if test="collectionResult != null">
        collection_result,
      </if>
      <if test="missingContactResult != null">
        missing_contact_result,
      </if>
      <if test="repaymentAmount != null">
        repayment_amount,
      </if>
      <if test="repaymentDate != null">
        repayment_date,
      </if>
      <if test="isMarkedForChange != null">
        is_marked_for_change,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
      <if test="operationId != null">
        operation_id,
      </if>
      <if test="operationName != null">
        operation_name,
      </if>
      <if test="taskPrediction != null">
        task_prediction,
      </if>
      <if test="collectionNotes != null">
        collection_notes,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="calledNumber != null">
        #{calledNumber,jdbcType=VARCHAR},
      </if>
      <if test="callerNumber != null">
        #{callerNumber,jdbcType=VARCHAR},
      </if>
      <if test="collectionResult != null">
        #{collectionResult,jdbcType=CHAR},
      </if>
      <if test="missingContactResult != null">
        #{missingContactResult,jdbcType=CHAR},
      </if>
      <if test="repaymentAmount != null">
        #{repaymentAmount,jdbcType=DECIMAL},
      </if>
      <if test="repaymentDate != null">
        #{repaymentDate,jdbcType=DATE},
      </if>
      <if test="isMarkedForChange != null">
        #{isMarkedForChange,jdbcType=BIT},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="operationId != null">
        #{operationId,jdbcType=BIGINT},
      </if>
      <if test="operationName != null">
        #{operationName,jdbcType=VARCHAR},
      </if>
      <if test="taskPrediction != null">
        #{taskPrediction,jdbcType=LONGVARCHAR},
      </if>
      <if test="collectionNotes != null">
        #{collectionNotes,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.linhong.boot.management.model.entity.FollowUpRecordsExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Nov 26 16:39:14 HKT 2024.
    -->
    select count(*) from follow_up_records
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Nov 26 16:39:14 HKT 2024.
    -->
    update follow_up_records
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.userId != null">
        user_id = #{row.userId,jdbcType=BIGINT},
      </if>
      <if test="row.calledNumber != null">
        called_number = #{row.calledNumber,jdbcType=VARCHAR},
      </if>
      <if test="row.callerNumber != null">
        caller_number = #{row.callerNumber,jdbcType=VARCHAR},
      </if>
      <if test="row.collectionResult != null">
        collection_result = #{row.collectionResult,jdbcType=CHAR},
      </if>
      <if test="row.missingContactResult != null">
        missing_contact_result = #{row.missingContactResult,jdbcType=CHAR},
      </if>
      <if test="row.repaymentAmount != null">
        repayment_amount = #{row.repaymentAmount,jdbcType=DECIMAL},
      </if>
      <if test="row.repaymentDate != null">
        repayment_date = #{row.repaymentDate,jdbcType=DATE},
      </if>
      <if test="row.isMarkedForChange != null">
        is_marked_for_change = #{row.isMarkedForChange,jdbcType=BIT},
      </if>
      <if test="row.createdAt != null">
        created_at = #{row.createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updatedAt != null">
        updated_at = #{row.updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="row.operationId != null">
        operation_id = #{row.operationId,jdbcType=BIGINT},
      </if>
      <if test="row.operationName != null">
        operation_name = #{row.operationName,jdbcType=VARCHAR},
      </if>
      <if test="row.taskPrediction != null">
        task_prediction = #{row.taskPrediction,jdbcType=LONGVARCHAR},
      </if>
      <if test="row.collectionNotes != null">
        collection_notes = #{row.collectionNotes,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Nov 26 16:39:14 HKT 2024.
    -->
    update follow_up_records
    set id = #{row.id,jdbcType=BIGINT},
      user_id = #{row.userId,jdbcType=BIGINT},
      called_number = #{row.calledNumber,jdbcType=VARCHAR},
      caller_number = #{row.callerNumber,jdbcType=VARCHAR},
      collection_result = #{row.collectionResult,jdbcType=CHAR},
      missing_contact_result = #{row.missingContactResult,jdbcType=CHAR},
      repayment_amount = #{row.repaymentAmount,jdbcType=DECIMAL},
      repayment_date = #{row.repaymentDate,jdbcType=DATE},
      is_marked_for_change = #{row.isMarkedForChange,jdbcType=BIT},
      created_at = #{row.createdAt,jdbcType=TIMESTAMP},
      updated_at = #{row.updatedAt,jdbcType=TIMESTAMP},
      operation_id = #{row.operationId,jdbcType=BIGINT},
      operation_name = #{row.operationName,jdbcType=VARCHAR},
      task_prediction = #{row.taskPrediction,jdbcType=LONGVARCHAR},
      collection_notes = #{row.collectionNotes,jdbcType=LONGVARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Nov 26 16:39:14 HKT 2024.
    -->
    update follow_up_records
    set id = #{row.id,jdbcType=BIGINT},
      user_id = #{row.userId,jdbcType=BIGINT},
      called_number = #{row.calledNumber,jdbcType=VARCHAR},
      caller_number = #{row.callerNumber,jdbcType=VARCHAR},
      collection_result = #{row.collectionResult,jdbcType=CHAR},
      missing_contact_result = #{row.missingContactResult,jdbcType=CHAR},
      repayment_amount = #{row.repaymentAmount,jdbcType=DECIMAL},
      repayment_date = #{row.repaymentDate,jdbcType=DATE},
      is_marked_for_change = #{row.isMarkedForChange,jdbcType=BIT},
      created_at = #{row.createdAt,jdbcType=TIMESTAMP},
      updated_at = #{row.updatedAt,jdbcType=TIMESTAMP},
      operation_id = #{row.operationId,jdbcType=BIGINT},
      operation_name = #{row.operationName,jdbcType=VARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.linhong.boot.management.model.entity.FollowUpRecordsWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Nov 26 16:39:14 HKT 2024.
    -->
    update follow_up_records
    <set>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="calledNumber != null">
        called_number = #{calledNumber,jdbcType=VARCHAR},
      </if>
      <if test="callerNumber != null">
        caller_number = #{callerNumber,jdbcType=VARCHAR},
      </if>
      <if test="collectionResult != null">
        collection_result = #{collectionResult,jdbcType=CHAR},
      </if>
      <if test="missingContactResult != null">
        missing_contact_result = #{missingContactResult,jdbcType=CHAR},
      </if>
      <if test="repaymentAmount != null">
        repayment_amount = #{repaymentAmount,jdbcType=DECIMAL},
      </if>
      <if test="repaymentDate != null">
        repayment_date = #{repaymentDate,jdbcType=DATE},
      </if>
      <if test="isMarkedForChange != null">
        is_marked_for_change = #{isMarkedForChange,jdbcType=BIT},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="operationId != null">
        operation_id = #{operationId,jdbcType=BIGINT},
      </if>
      <if test="operationName != null">
        operation_name = #{operationName,jdbcType=VARCHAR},
      </if>
      <if test="taskPrediction != null">
        task_prediction = #{taskPrediction,jdbcType=LONGVARCHAR},
      </if>
      <if test="collectionNotes != null">
        collection_notes = #{collectionNotes,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.linhong.boot.management.model.entity.FollowUpRecordsWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Nov 26 16:39:14 HKT 2024.
    -->
    update follow_up_records
    set user_id = #{userId,jdbcType=BIGINT},
      called_number = #{calledNumber,jdbcType=VARCHAR},
      caller_number = #{callerNumber,jdbcType=VARCHAR},
      collection_result = #{collectionResult,jdbcType=CHAR},
      missing_contact_result = #{missingContactResult,jdbcType=CHAR},
      repayment_amount = #{repaymentAmount,jdbcType=DECIMAL},
      repayment_date = #{repaymentDate,jdbcType=DATE},
      is_marked_for_change = #{isMarkedForChange,jdbcType=BIT},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      operation_id = #{operationId,jdbcType=BIGINT},
      operation_name = #{operationName,jdbcType=VARCHAR},
      task_prediction = #{taskPrediction,jdbcType=LONGVARCHAR},
      collection_notes = #{collectionNotes,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.linhong.boot.management.model.entity.FollowUpRecords">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Nov 26 16:39:14 HKT 2024.
    -->
    update follow_up_records
    set user_id = #{userId,jdbcType=BIGINT},
      called_number = #{calledNumber,jdbcType=VARCHAR},
      caller_number = #{callerNumber,jdbcType=VARCHAR},
      collection_result = #{collectionResult,jdbcType=CHAR},
      missing_contact_result = #{missingContactResult,jdbcType=CHAR},
      repayment_amount = #{repaymentAmount,jdbcType=DECIMAL},
      repayment_date = #{repaymentDate,jdbcType=DATE},
      is_marked_for_change = #{isMarkedForChange,jdbcType=BIT},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      operation_id = #{operationId,jdbcType=BIGINT},
      operation_name = #{operationName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>