package com.linhong.boot.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.linhong.boot.system.model.entity.LoginCultureConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Delete;

import java.util.List;

/**
 * 企业文化配置 访问层
 *
 * <AUTHOR>
 */
@Mapper
public interface LoginCultureConfigMapper extends BaseMapper<LoginCultureConfig> {

    /**
     * 查询所有启用的企业文化配置，按排序字段排序
     *
     * @return 企业文化配置列表
     */
    @Select("SELECT * FROM login_culture_config WHERE status = 1 ORDER BY sort_order")
    List<LoginCultureConfig> selectAllActiveOrderBySort();

    /**
     * 删除所有企业文化配置
     *
     * @return 删除行数
     */
    @Delete("DELETE FROM login_culture_config")
    int deleteAll();
}
