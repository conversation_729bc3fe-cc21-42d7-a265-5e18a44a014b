package com.linhong.boot.management.controller;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.linhong.boot.common.annotation.Log;
import com.linhong.boot.common.enums.CaseEnum;
import com.linhong.boot.common.enums.LogModuleEnum;
import com.linhong.boot.common.result.PageResult;
import com.linhong.boot.common.result.Result;
import com.linhong.boot.common.util.ReflectiveCopierUtils;
import com.linhong.boot.management.model.dto.CcUserExport;
import com.linhong.boot.management.model.entity.CcUser;
import com.linhong.boot.management.model.entity.CcUserWork;
import com.linhong.boot.management.model.entity.FollowUpRecords;
import com.linhong.boot.management.model.entity.FollowUpRecordsHistory;
import com.linhong.boot.management.model.entity.TransferRecords;
import com.linhong.boot.management.model.query.CcUserExcQuery;
import com.linhong.boot.management.model.query.CcUserQuery;
import com.linhong.boot.management.model.vo.CcUserVo;
import com.linhong.boot.management.model.vo.ExcelTitleVo;
import com.linhong.boot.management.service.CasePoolService;
import com.linhong.boot.management.service.CcWorksService;
import com.linhong.boot.management.service.FollowUpRecordsHistoryService;
import com.linhong.boot.management.service.FollowUpRecordsService;
import com.linhong.boot.management.service.TransferRecordsService;
import com.linhong.boot.system.model.entity.User;
import com.linhong.boot.system.service.UserService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;

@Tag(name = "4.作业清单")
@RestController
@RequestMapping("/api/v1/work")
@RequiredArgsConstructor
public class WorkController {
	
	private final CasePoolService casePoolService;
	private final UserService userService;
	private final TransferRecordsService transferRecordsService;
	private final CcWorksService worksService;
	private final FollowUpRecordsHistoryService historyService;
	private final FollowUpRecordsService followUpRecordsService;
	
	
	
	
	@Operation(summary = "案件详情里,作业清单目录栏列表")
	@GetMapping("/workMunePage")
	public PageResult<CcUserVo> getPoolWorkPage(CcUserQuery query) {
		List<String> followStatusList = new ArrayList<>();
		followStatusList.add(CaseEnum.TUI_HUI.getValue().toString());
		IPage<CcUserVo> result = casePoolService.getWorkPage(query);
		List<CcUserVo> list = result.getRecords();
		if(list!=null) {
			CcUserVo v = null;
			for (CcUserVo c : list) {
				if(c.getId().equals(query.getId())) {
					v = c;
					break;
				}
			}
			if(v!=null) {
//				list.remove(v);
//				list.add(0,v);
//				result.setRecords(list);
			}
		}
		return PageResult.success(result);
	}
	
	
	/*重点关注性能优化,目前单条更新案件状态*/
	@Operation(summary = "分配案件")
	@PostMapping(value = "/allocate")
	@Log(value = "管理分配案件", module = LogModuleEnum.WORKPOOL)
	public Result<Void> autoAllocate(@RequestBody(required = false) CcUserQuery query) {
		int idsNumber = query.getIds()!=null ?query.getIds().size():0;
		int caseNumber = query.getCIdNum();
		int userNumber = query.getUIds().size();
		query.setPageNum(1);
		if(caseNumber <= 0 && idsNumber <= 0)
			return Result.failed("案件数量必须大于0");
		if(userNumber <= 0)
			return Result.failed("没有选择员工");
		/*设置需要分配的案件数量*/
		query.setPageSize(query.getCIdNum());
		boolean result = casePoolService.workAllocate(query);
		return Result.judge(result);
	}
	
	
	@Operation(summary = "导出案池-前端版")
	@PostMapping("/CaseExport")
	public Result<CcUserExport> CaseExport(@RequestBody CcUserExcQuery query) throws IOException {
		// 是否为退案导出 , 退案导出, 需要进行逻辑删除  跟进状态=99
		String isTuiAn = query.getIsTuiAn(); // 是否为退案导出 , 退案导出, 需要进行逻辑删除  跟进状态=99
		List<ExcelTitleVo> title = new ArrayList<>();
		ExcelTitleVo v1 = new ExcelTitleVo("原催收人","oldUserOrDept");
		ExcelTitleVo v2 = new ExcelTitleVo("现催收人","userOrDept");
		ExcelTitleVo v3 = new ExcelTitleVo("客户索引号","customerIndexNumber");
		
		List<CcUser> ccUserList = new ArrayList<>();
		CcUserExport ex = new CcUserExport();
		String fileName = query.getFileName()==null?"未命名":query.getFileName();
		ex.setFileName(fileName);
		if(query.getIds()!=null && query.getIds().size() > 0) {
			// 勾选
			ccUserList =  casePoolService.getBaseMapper().selectBatchIds(query.getIds());
			CcUser linshi  = ccUserList.get(0);
			/*表头*/
			title = ReflectiveCopierUtils.getNonEmptyFieldNames(linshi);	
		}else{
			// 按条件
			/*表头*/
			title = ReflectiveCopierUtils.getNonEmptyFieldNames(query);
			ccUserList = casePoolService.getWorkPoolExt(query);
		}

		
		/*查询原催收人 现催收人*/
		List<String> ids = ccUserList.stream().map(CcUser::getId).map(Object::toString).collect(Collectors.toList());
		
		List<TransferRecords> reList = transferRecordsService.selectUserOrDept(ids);
		List<User> userList = userService.getBaseMapper().selectList(null);
		Map<Long,String> mapUserName = userList.stream().collect(Collectors.toMap(User::getId, User::getNickname));
//		if(!reList.isEmpty()) {
//			Map<Long, TransferRecords> mapById = reList.stream().collect(Collectors.toMap(
//					TransferRecords::getId, 
//				    record -> {
//				            return record;
//				    },(existing, replacement) -> existing
//			));
//			for (CcUser cu : ccUserList) {
//				if(mapById.containsKey(cu.getId())) {
//					TransferRecords r = mapById.get(cu.getId());
//					String soureName = r.getSourceUser();
//					String tarName = r.getTargetUser();
//					String operator = r.getOperator();
//					if(!soureName.equals("案池") && mapUserName.containsKey(Long.valueOf(soureName)))
//						soureName = mapUserName.get(Long.valueOf(soureName));
//					if(!tarName.equals("案池") &&mapUserName.containsKey(Long.valueOf(tarName)))
//						tarName = mapUserName.get(Long.valueOf(tarName));
//					if(mapUserName.containsKey(Long.valueOf(operator)))
//						operator = mapUserName.get(Long.valueOf(operator));
//					cu.setOldUserOrDept(soureName!=null && soureName.equals("案池")?operator:soureName);
//					cu.setUserOrDept(tarName!=null&&tarName.equals("案池")?operator:tarName);
//				}else {
//					cu.setOldUserOrDept("");
//					cu.setUserOrDept("");
//				}
//			}
//		}
		
		Map<Long, TransferRecords> mapById = null;
		if (reList != null) {
		    mapById = reList.stream().collect(Collectors.toMap(TransferRecords::getId, record -> record, (existing, replacement) -> existing));
		}
		for (CcUser cu : ccUserList) {
		    if (mapById != null && mapById.containsKey(cu.getId())) {
		        TransferRecords r = mapById.get(cu.getId());
		        String soureName = r.getSourceUser(); //源催收人
		        String tarName = r.getTargetUser(); // 现催收人
		        String operator = r.getOperator();
		        
		        if (!soureName.equals("案池") && mapUserName.containsKey(Long.valueOf(soureName))) {
		            soureName = mapUserName.get(Long.valueOf(soureName));
		        }
		        if (!tarName.equals("案池") && mapUserName.containsKey(Long.valueOf(tarName))) {
		            tarName = mapUserName.get(Long.valueOf(tarName));
		        }
		        if (mapUserName.containsKey(Long.valueOf(operator))) {
		            operator = mapUserName.get(Long.valueOf(operator));
		        }
		        
		        cu.setOldUserOrDept(soureName == null? "空" : soureName);
		        cu.setUserOrDept(tarName==null?"空":tarName); //2025.01.11 根据客户要求修改. 
		    } else {
		    	//没有分配记录
		        cu.setOldUserOrDept("案池");
		        cu.setUserOrDept("案池");
		    }
		}
		
		title.add(v1);title.add(v2);title.add(v3);
		ex.setTitleList(title);
		ex.setList(ccUserList);
		if(isTuiAn!=null && isTuiAn.equals("1")) {
			List<CcUser> tuiAnList = new ArrayList<>();
			LambdaQueryWrapper<CcUserWork> workQuery = new LambdaQueryWrapper<>();
			workQuery.in(CcUserWork::getCcId, ids);
			worksService.getBaseMapper().delete(workQuery);
			for (CcUser ccUser : ccUserList) {
				CcUser tuiAnUser = new CcUser();
				tuiAnUser.setId(ccUser.getId());
				tuiAnUser.setUserOrDept("");
				tuiAnUser.setIsLock("0");
				tuiAnUser.setFollowUpStatus(CaseEnum.TUI_AN.getValue().toString());
				tuiAnList.add(tuiAnUser);
			}
			if(!tuiAnList.isEmpty())
				casePoolService.updateBatchById(tuiAnList);	
			/*跟进记录转为历史跟进记录*/
			LambdaQueryWrapper<FollowUpRecords> followQuery = new LambdaQueryWrapper<>();
			followQuery.in(FollowUpRecords::getUserId, ids);
			List<FollowUpRecords> recordsList = followUpRecordsService.getBaseMapper().selectList(followQuery);
			if(recordsList.size()>0) {
				List<FollowUpRecordsHistory> historyRecords = new ArrayList<>();
				try {
					for (FollowUpRecords r : recordsList) {
						FollowUpRecordsHistory h = new FollowUpRecordsHistory();
						ReflectiveCopierUtils.copy(r, h);
						historyRecords.add(h);
					}
				} catch (Exception e) {
					e.printStackTrace();
				}
				historyService.saveBatchHistory(historyRecords);
				followUpRecordsService.getBaseMapper().deleteBatchIds(recordsList);
			}
		}
		return Result.success(ex);
	}
	
	
	
	
	/**
	 * 输入案件id.list 返回null则没有锁定案件. 返回字符串为锁定的案件字符串[xxx,xxx]
	 * @param ids
	 * @return
	 */
	public String getLockResult(List<Long> ids) {
		/*判断是否为锁定案件*/
		String st = null;
		LambdaQueryWrapper<CcUser> q = new LambdaQueryWrapper<>();
		q.eq(CcUser::getIsLock, "1").in(CcUser::getId, ids);
			List<CcUser> locklist = casePoolService.getBaseMapper().selectList(q);
			if(locklist!=null && locklist.size()>0) {
				st = locklist.stream().map(CcUser::getId).map(Object::toString).collect(Collectors.joining(",","[","]"));
			}
		return st;
	}
	
	/**
	 * 输入案件id.list. 返回字符串为没有换单权限的案件字符串[xxx,xxx]
	 * @param ids
	 * @return
	 */
	public String getMarkChangeResult(List<Long> ids) {
		/*判断是否为锁定案件*/
		String st = null;
		LambdaQueryWrapper<CcUser> q = new LambdaQueryWrapper<>();
		// isMarkChange=-1 没有换单权限
		q.eq(CcUser::getIsMarkChange, "-1").in(CcUser::getId, ids);
			List<CcUser> locklist = casePoolService.getBaseMapper().selectList(q);
			if(locklist!=null && locklist.size()>0) {
				st = locklist.stream().map(CcUser::getId).map(Object::toString).collect(Collectors.joining(",","[","]"));
			}
		return st;
	}
	
	
	
	
}
