package com.linhong.boot.management.model.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

public class FollowUpRecordsHistoryExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table follow_up_records_history
     *
     * @mbg.generated Thu Dec 26 15:31:06 CST 2024
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table follow_up_records_history
     *
     * @mbg.generated Thu Dec 26 15:31:06 CST 2024
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table follow_up_records_history
     *
     * @mbg.generated Thu Dec 26 15:31:06 CST 2024
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table follow_up_records_history
     *
     * @mbg.generated Thu Dec 26 15:31:06 CST 2024
     */
    public FollowUpRecordsHistoryExample() {
        oredCriteria = new ArrayList<>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table follow_up_records_history
     *
     * @mbg.generated Thu Dec 26 15:31:06 CST 2024
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table follow_up_records_history
     *
     * @mbg.generated Thu Dec 26 15:31:06 CST 2024
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table follow_up_records_history
     *
     * @mbg.generated Thu Dec 26 15:31:06 CST 2024
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table follow_up_records_history
     *
     * @mbg.generated Thu Dec 26 15:31:06 CST 2024
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table follow_up_records_history
     *
     * @mbg.generated Thu Dec 26 15:31:06 CST 2024
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table follow_up_records_history
     *
     * @mbg.generated Thu Dec 26 15:31:06 CST 2024
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table follow_up_records_history
     *
     * @mbg.generated Thu Dec 26 15:31:06 CST 2024
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table follow_up_records_history
     *
     * @mbg.generated Thu Dec 26 15:31:06 CST 2024
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table follow_up_records_history
     *
     * @mbg.generated Thu Dec 26 15:31:06 CST 2024
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table follow_up_records_history
     *
     * @mbg.generated Thu Dec 26 15:31:06 CST 2024
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table follow_up_records_history
     *
     * @mbg.generated Thu Dec 26 15:31:06 CST 2024
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        protected void addCriterionForJDBCDate(String condition, Date value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value.getTime()), property);
        }

        protected void addCriterionForJDBCDate(String condition, List<Date> values, String property) {
            if (values == null || values.size() == 0) {
                throw new RuntimeException("Value list for " + property + " cannot be null or empty");
            }
            List<java.sql.Date> dateList = new ArrayList<>();
            Iterator<Date> iter = values.iterator();
            while (iter.hasNext()) {
                dateList.add(new java.sql.Date(iter.next().getTime()));
            }
            addCriterion(condition, dateList, property);
        }

        protected void addCriterionForJDBCDate(String condition, Date value1, Date value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value1.getTime()), new java.sql.Date(value2.getTime()), property);
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andCalledNumberIsNull() {
            addCriterion("called_number is null");
            return (Criteria) this;
        }

        public Criteria andCalledNumberIsNotNull() {
            addCriterion("called_number is not null");
            return (Criteria) this;
        }

        public Criteria andCalledNumberEqualTo(String value) {
            addCriterion("called_number =", value, "calledNumber");
            return (Criteria) this;
        }

        public Criteria andCalledNumberNotEqualTo(String value) {
            addCriterion("called_number <>", value, "calledNumber");
            return (Criteria) this;
        }

        public Criteria andCalledNumberGreaterThan(String value) {
            addCriterion("called_number >", value, "calledNumber");
            return (Criteria) this;
        }

        public Criteria andCalledNumberGreaterThanOrEqualTo(String value) {
            addCriterion("called_number >=", value, "calledNumber");
            return (Criteria) this;
        }

        public Criteria andCalledNumberLessThan(String value) {
            addCriterion("called_number <", value, "calledNumber");
            return (Criteria) this;
        }

        public Criteria andCalledNumberLessThanOrEqualTo(String value) {
            addCriterion("called_number <=", value, "calledNumber");
            return (Criteria) this;
        }

        public Criteria andCalledNumberLike(String value) {
            addCriterion("called_number like", value, "calledNumber");
            return (Criteria) this;
        }

        public Criteria andCalledNumberNotLike(String value) {
            addCriterion("called_number not like", value, "calledNumber");
            return (Criteria) this;
        }

        public Criteria andCalledNumberIn(List<String> values) {
            addCriterion("called_number in", values, "calledNumber");
            return (Criteria) this;
        }

        public Criteria andCalledNumberNotIn(List<String> values) {
            addCriterion("called_number not in", values, "calledNumber");
            return (Criteria) this;
        }

        public Criteria andCalledNumberBetween(String value1, String value2) {
            addCriterion("called_number between", value1, value2, "calledNumber");
            return (Criteria) this;
        }

        public Criteria andCalledNumberNotBetween(String value1, String value2) {
            addCriterion("called_number not between", value1, value2, "calledNumber");
            return (Criteria) this;
        }

        public Criteria andCallerNumberIsNull() {
            addCriterion("caller_number is null");
            return (Criteria) this;
        }

        public Criteria andCallerNumberIsNotNull() {
            addCriterion("caller_number is not null");
            return (Criteria) this;
        }

        public Criteria andCallerNumberEqualTo(String value) {
            addCriterion("caller_number =", value, "callerNumber");
            return (Criteria) this;
        }

        public Criteria andCallerNumberNotEqualTo(String value) {
            addCriterion("caller_number <>", value, "callerNumber");
            return (Criteria) this;
        }

        public Criteria andCallerNumberGreaterThan(String value) {
            addCriterion("caller_number >", value, "callerNumber");
            return (Criteria) this;
        }

        public Criteria andCallerNumberGreaterThanOrEqualTo(String value) {
            addCriterion("caller_number >=", value, "callerNumber");
            return (Criteria) this;
        }

        public Criteria andCallerNumberLessThan(String value) {
            addCriterion("caller_number <", value, "callerNumber");
            return (Criteria) this;
        }

        public Criteria andCallerNumberLessThanOrEqualTo(String value) {
            addCriterion("caller_number <=", value, "callerNumber");
            return (Criteria) this;
        }

        public Criteria andCallerNumberLike(String value) {
            addCriterion("caller_number like", value, "callerNumber");
            return (Criteria) this;
        }

        public Criteria andCallerNumberNotLike(String value) {
            addCriterion("caller_number not like", value, "callerNumber");
            return (Criteria) this;
        }

        public Criteria andCallerNumberIn(List<String> values) {
            addCriterion("caller_number in", values, "callerNumber");
            return (Criteria) this;
        }

        public Criteria andCallerNumberNotIn(List<String> values) {
            addCriterion("caller_number not in", values, "callerNumber");
            return (Criteria) this;
        }

        public Criteria andCallerNumberBetween(String value1, String value2) {
            addCriterion("caller_number between", value1, value2, "callerNumber");
            return (Criteria) this;
        }

        public Criteria andCallerNumberNotBetween(String value1, String value2) {
            addCriterion("caller_number not between", value1, value2, "callerNumber");
            return (Criteria) this;
        }

        public Criteria andCollectionResultIsNull() {
            addCriterion("collection_result is null");
            return (Criteria) this;
        }

        public Criteria andCollectionResultIsNotNull() {
            addCriterion("collection_result is not null");
            return (Criteria) this;
        }

        public Criteria andCollectionResultEqualTo(String value) {
            addCriterion("collection_result =", value, "collectionResult");
            return (Criteria) this;
        }

        public Criteria andCollectionResultNotEqualTo(String value) {
            addCriterion("collection_result <>", value, "collectionResult");
            return (Criteria) this;
        }

        public Criteria andCollectionResultGreaterThan(String value) {
            addCriterion("collection_result >", value, "collectionResult");
            return (Criteria) this;
        }

        public Criteria andCollectionResultGreaterThanOrEqualTo(String value) {
            addCriterion("collection_result >=", value, "collectionResult");
            return (Criteria) this;
        }

        public Criteria andCollectionResultLessThan(String value) {
            addCriterion("collection_result <", value, "collectionResult");
            return (Criteria) this;
        }

        public Criteria andCollectionResultLessThanOrEqualTo(String value) {
            addCriterion("collection_result <=", value, "collectionResult");
            return (Criteria) this;
        }

        public Criteria andCollectionResultLike(String value) {
            addCriterion("collection_result like", value, "collectionResult");
            return (Criteria) this;
        }

        public Criteria andCollectionResultNotLike(String value) {
            addCriterion("collection_result not like", value, "collectionResult");
            return (Criteria) this;
        }

        public Criteria andCollectionResultIn(List<String> values) {
            addCriterion("collection_result in", values, "collectionResult");
            return (Criteria) this;
        }

        public Criteria andCollectionResultNotIn(List<String> values) {
            addCriterion("collection_result not in", values, "collectionResult");
            return (Criteria) this;
        }

        public Criteria andCollectionResultBetween(String value1, String value2) {
            addCriterion("collection_result between", value1, value2, "collectionResult");
            return (Criteria) this;
        }

        public Criteria andCollectionResultNotBetween(String value1, String value2) {
            addCriterion("collection_result not between", value1, value2, "collectionResult");
            return (Criteria) this;
        }

        public Criteria andMissingContactResultIsNull() {
            addCriterion("missing_contact_result is null");
            return (Criteria) this;
        }

        public Criteria andMissingContactResultIsNotNull() {
            addCriterion("missing_contact_result is not null");
            return (Criteria) this;
        }

        public Criteria andMissingContactResultEqualTo(String value) {
            addCriterion("missing_contact_result =", value, "missingContactResult");
            return (Criteria) this;
        }

        public Criteria andMissingContactResultNotEqualTo(String value) {
            addCriterion("missing_contact_result <>", value, "missingContactResult");
            return (Criteria) this;
        }

        public Criteria andMissingContactResultGreaterThan(String value) {
            addCriterion("missing_contact_result >", value, "missingContactResult");
            return (Criteria) this;
        }

        public Criteria andMissingContactResultGreaterThanOrEqualTo(String value) {
            addCriterion("missing_contact_result >=", value, "missingContactResult");
            return (Criteria) this;
        }

        public Criteria andMissingContactResultLessThan(String value) {
            addCriterion("missing_contact_result <", value, "missingContactResult");
            return (Criteria) this;
        }

        public Criteria andMissingContactResultLessThanOrEqualTo(String value) {
            addCriterion("missing_contact_result <=", value, "missingContactResult");
            return (Criteria) this;
        }

        public Criteria andMissingContactResultLike(String value) {
            addCriterion("missing_contact_result like", value, "missingContactResult");
            return (Criteria) this;
        }

        public Criteria andMissingContactResultNotLike(String value) {
            addCriterion("missing_contact_result not like", value, "missingContactResult");
            return (Criteria) this;
        }

        public Criteria andMissingContactResultIn(List<String> values) {
            addCriterion("missing_contact_result in", values, "missingContactResult");
            return (Criteria) this;
        }

        public Criteria andMissingContactResultNotIn(List<String> values) {
            addCriterion("missing_contact_result not in", values, "missingContactResult");
            return (Criteria) this;
        }

        public Criteria andMissingContactResultBetween(String value1, String value2) {
            addCriterion("missing_contact_result between", value1, value2, "missingContactResult");
            return (Criteria) this;
        }

        public Criteria andMissingContactResultNotBetween(String value1, String value2) {
            addCriterion("missing_contact_result not between", value1, value2, "missingContactResult");
            return (Criteria) this;
        }

        public Criteria andRepaymentAmountIsNull() {
            addCriterion("repayment_amount is null");
            return (Criteria) this;
        }

        public Criteria andRepaymentAmountIsNotNull() {
            addCriterion("repayment_amount is not null");
            return (Criteria) this;
        }

        public Criteria andRepaymentAmountEqualTo(BigDecimal value) {
            addCriterion("repayment_amount =", value, "repaymentAmount");
            return (Criteria) this;
        }

        public Criteria andRepaymentAmountNotEqualTo(BigDecimal value) {
            addCriterion("repayment_amount <>", value, "repaymentAmount");
            return (Criteria) this;
        }

        public Criteria andRepaymentAmountGreaterThan(BigDecimal value) {
            addCriterion("repayment_amount >", value, "repaymentAmount");
            return (Criteria) this;
        }

        public Criteria andRepaymentAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("repayment_amount >=", value, "repaymentAmount");
            return (Criteria) this;
        }

        public Criteria andRepaymentAmountLessThan(BigDecimal value) {
            addCriterion("repayment_amount <", value, "repaymentAmount");
            return (Criteria) this;
        }

        public Criteria andRepaymentAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("repayment_amount <=", value, "repaymentAmount");
            return (Criteria) this;
        }

        public Criteria andRepaymentAmountIn(List<BigDecimal> values) {
            addCriterion("repayment_amount in", values, "repaymentAmount");
            return (Criteria) this;
        }

        public Criteria andRepaymentAmountNotIn(List<BigDecimal> values) {
            addCriterion("repayment_amount not in", values, "repaymentAmount");
            return (Criteria) this;
        }

        public Criteria andRepaymentAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("repayment_amount between", value1, value2, "repaymentAmount");
            return (Criteria) this;
        }

        public Criteria andRepaymentAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("repayment_amount not between", value1, value2, "repaymentAmount");
            return (Criteria) this;
        }

        public Criteria andRepaymentDateIsNull() {
            addCriterion("repayment_date is null");
            return (Criteria) this;
        }

        public Criteria andRepaymentDateIsNotNull() {
            addCriterion("repayment_date is not null");
            return (Criteria) this;
        }

        public Criteria andRepaymentDateEqualTo(Date value) {
            addCriterionForJDBCDate("repayment_date =", value, "repaymentDate");
            return (Criteria) this;
        }

        public Criteria andRepaymentDateNotEqualTo(Date value) {
            addCriterionForJDBCDate("repayment_date <>", value, "repaymentDate");
            return (Criteria) this;
        }

        public Criteria andRepaymentDateGreaterThan(Date value) {
            addCriterionForJDBCDate("repayment_date >", value, "repaymentDate");
            return (Criteria) this;
        }

        public Criteria andRepaymentDateGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("repayment_date >=", value, "repaymentDate");
            return (Criteria) this;
        }

        public Criteria andRepaymentDateLessThan(Date value) {
            addCriterionForJDBCDate("repayment_date <", value, "repaymentDate");
            return (Criteria) this;
        }

        public Criteria andRepaymentDateLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("repayment_date <=", value, "repaymentDate");
            return (Criteria) this;
        }

        public Criteria andRepaymentDateIn(List<Date> values) {
            addCriterionForJDBCDate("repayment_date in", values, "repaymentDate");
            return (Criteria) this;
        }

        public Criteria andRepaymentDateNotIn(List<Date> values) {
            addCriterionForJDBCDate("repayment_date not in", values, "repaymentDate");
            return (Criteria) this;
        }

        public Criteria andRepaymentDateBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("repayment_date between", value1, value2, "repaymentDate");
            return (Criteria) this;
        }

        public Criteria andRepaymentDateNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("repayment_date not between", value1, value2, "repaymentDate");
            return (Criteria) this;
        }

        public Criteria andIsMarkedForChangeIsNull() {
            addCriterion("is_marked_for_change is null");
            return (Criteria) this;
        }

        public Criteria andIsMarkedForChangeIsNotNull() {
            addCriterion("is_marked_for_change is not null");
            return (Criteria) this;
        }

        public Criteria andIsMarkedForChangeEqualTo(Boolean value) {
            addCriterion("is_marked_for_change =", value, "isMarkedForChange");
            return (Criteria) this;
        }

        public Criteria andIsMarkedForChangeNotEqualTo(Boolean value) {
            addCriterion("is_marked_for_change <>", value, "isMarkedForChange");
            return (Criteria) this;
        }

        public Criteria andIsMarkedForChangeGreaterThan(Boolean value) {
            addCriterion("is_marked_for_change >", value, "isMarkedForChange");
            return (Criteria) this;
        }

        public Criteria andIsMarkedForChangeGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_marked_for_change >=", value, "isMarkedForChange");
            return (Criteria) this;
        }

        public Criteria andIsMarkedForChangeLessThan(Boolean value) {
            addCriterion("is_marked_for_change <", value, "isMarkedForChange");
            return (Criteria) this;
        }

        public Criteria andIsMarkedForChangeLessThanOrEqualTo(Boolean value) {
            addCriterion("is_marked_for_change <=", value, "isMarkedForChange");
            return (Criteria) this;
        }

        public Criteria andIsMarkedForChangeIn(List<Boolean> values) {
            addCriterion("is_marked_for_change in", values, "isMarkedForChange");
            return (Criteria) this;
        }

        public Criteria andIsMarkedForChangeNotIn(List<Boolean> values) {
            addCriterion("is_marked_for_change not in", values, "isMarkedForChange");
            return (Criteria) this;
        }

        public Criteria andIsMarkedForChangeBetween(Boolean value1, Boolean value2) {
            addCriterion("is_marked_for_change between", value1, value2, "isMarkedForChange");
            return (Criteria) this;
        }

        public Criteria andIsMarkedForChangeNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_marked_for_change not between", value1, value2, "isMarkedForChange");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIsNull() {
            addCriterion("created_at is null");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIsNotNull() {
            addCriterion("created_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedAtEqualTo(Date value) {
            addCriterion("created_at =", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotEqualTo(Date value) {
            addCriterion("created_at <>", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtGreaterThan(Date value) {
            addCriterion("created_at >", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtGreaterThanOrEqualTo(Date value) {
            addCriterion("created_at >=", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtLessThan(Date value) {
            addCriterion("created_at <", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtLessThanOrEqualTo(Date value) {
            addCriterion("created_at <=", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIn(List<Date> values) {
            addCriterion("created_at in", values, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotIn(List<Date> values) {
            addCriterion("created_at not in", values, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtBetween(Date value1, Date value2) {
            addCriterion("created_at between", value1, value2, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotBetween(Date value1, Date value2) {
            addCriterion("created_at not between", value1, value2, "createdAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIsNull() {
            addCriterion("updated_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIsNotNull() {
            addCriterion("updated_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtEqualTo(Date value) {
            addCriterion("updated_at =", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotEqualTo(Date value) {
            addCriterion("updated_at <>", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtGreaterThan(Date value) {
            addCriterion("updated_at >", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtGreaterThanOrEqualTo(Date value) {
            addCriterion("updated_at >=", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtLessThan(Date value) {
            addCriterion("updated_at <", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtLessThanOrEqualTo(Date value) {
            addCriterion("updated_at <=", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIn(List<Date> values) {
            addCriterion("updated_at in", values, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotIn(List<Date> values) {
            addCriterion("updated_at not in", values, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtBetween(Date value1, Date value2) {
            addCriterion("updated_at between", value1, value2, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotBetween(Date value1, Date value2) {
            addCriterion("updated_at not between", value1, value2, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andOperationIdIsNull() {
            addCriterion("operation_id is null");
            return (Criteria) this;
        }

        public Criteria andOperationIdIsNotNull() {
            addCriterion("operation_id is not null");
            return (Criteria) this;
        }

        public Criteria andOperationIdEqualTo(Long value) {
            addCriterion("operation_id =", value, "operationId");
            return (Criteria) this;
        }

        public Criteria andOperationIdNotEqualTo(Long value) {
            addCriterion("operation_id <>", value, "operationId");
            return (Criteria) this;
        }

        public Criteria andOperationIdGreaterThan(Long value) {
            addCriterion("operation_id >", value, "operationId");
            return (Criteria) this;
        }

        public Criteria andOperationIdGreaterThanOrEqualTo(Long value) {
            addCriterion("operation_id >=", value, "operationId");
            return (Criteria) this;
        }

        public Criteria andOperationIdLessThan(Long value) {
            addCriterion("operation_id <", value, "operationId");
            return (Criteria) this;
        }

        public Criteria andOperationIdLessThanOrEqualTo(Long value) {
            addCriterion("operation_id <=", value, "operationId");
            return (Criteria) this;
        }

        public Criteria andOperationIdIn(List<Long> values) {
            addCriterion("operation_id in", values, "operationId");
            return (Criteria) this;
        }

        public Criteria andOperationIdNotIn(List<Long> values) {
            addCriterion("operation_id not in", values, "operationId");
            return (Criteria) this;
        }

        public Criteria andOperationIdBetween(Long value1, Long value2) {
            addCriterion("operation_id between", value1, value2, "operationId");
            return (Criteria) this;
        }

        public Criteria andOperationIdNotBetween(Long value1, Long value2) {
            addCriterion("operation_id not between", value1, value2, "operationId");
            return (Criteria) this;
        }

        public Criteria andOperationNameIsNull() {
            addCriterion("operation_name is null");
            return (Criteria) this;
        }

        public Criteria andOperationNameIsNotNull() {
            addCriterion("operation_name is not null");
            return (Criteria) this;
        }

        public Criteria andOperationNameEqualTo(String value) {
            addCriterion("operation_name =", value, "operationName");
            return (Criteria) this;
        }

        public Criteria andOperationNameNotEqualTo(String value) {
            addCriterion("operation_name <>", value, "operationName");
            return (Criteria) this;
        }

        public Criteria andOperationNameGreaterThan(String value) {
            addCriterion("operation_name >", value, "operationName");
            return (Criteria) this;
        }

        public Criteria andOperationNameGreaterThanOrEqualTo(String value) {
            addCriterion("operation_name >=", value, "operationName");
            return (Criteria) this;
        }

        public Criteria andOperationNameLessThan(String value) {
            addCriterion("operation_name <", value, "operationName");
            return (Criteria) this;
        }

        public Criteria andOperationNameLessThanOrEqualTo(String value) {
            addCriterion("operation_name <=", value, "operationName");
            return (Criteria) this;
        }

        public Criteria andOperationNameLike(String value) {
            addCriterion("operation_name like", value, "operationName");
            return (Criteria) this;
        }

        public Criteria andOperationNameNotLike(String value) {
            addCriterion("operation_name not like", value, "operationName");
            return (Criteria) this;
        }

        public Criteria andOperationNameIn(List<String> values) {
            addCriterion("operation_name in", values, "operationName");
            return (Criteria) this;
        }

        public Criteria andOperationNameNotIn(List<String> values) {
            addCriterion("operation_name not in", values, "operationName");
            return (Criteria) this;
        }

        public Criteria andOperationNameBetween(String value1, String value2) {
            addCriterion("operation_name between", value1, value2, "operationName");
            return (Criteria) this;
        }

        public Criteria andOperationNameNotBetween(String value1, String value2) {
            addCriterion("operation_name not between", value1, value2, "operationName");
            return (Criteria) this;
        }

        public Criteria andUnfollowedDaysIsNull() {
            addCriterion("unfollowed_days is null");
            return (Criteria) this;
        }

        public Criteria andUnfollowedDaysIsNotNull() {
            addCriterion("unfollowed_days is not null");
            return (Criteria) this;
        }

        public Criteria andUnfollowedDaysEqualTo(Integer value) {
            addCriterion("unfollowed_days =", value, "unfollowedDays");
            return (Criteria) this;
        }

        public Criteria andUnfollowedDaysNotEqualTo(Integer value) {
            addCriterion("unfollowed_days <>", value, "unfollowedDays");
            return (Criteria) this;
        }

        public Criteria andUnfollowedDaysGreaterThan(Integer value) {
            addCriterion("unfollowed_days >", value, "unfollowedDays");
            return (Criteria) this;
        }

        public Criteria andUnfollowedDaysGreaterThanOrEqualTo(Integer value) {
            addCriterion("unfollowed_days >=", value, "unfollowedDays");
            return (Criteria) this;
        }

        public Criteria andUnfollowedDaysLessThan(Integer value) {
            addCriterion("unfollowed_days <", value, "unfollowedDays");
            return (Criteria) this;
        }

        public Criteria andUnfollowedDaysLessThanOrEqualTo(Integer value) {
            addCriterion("unfollowed_days <=", value, "unfollowedDays");
            return (Criteria) this;
        }

        public Criteria andUnfollowedDaysIn(List<Integer> values) {
            addCriterion("unfollowed_days in", values, "unfollowedDays");
            return (Criteria) this;
        }

        public Criteria andUnfollowedDaysNotIn(List<Integer> values) {
            addCriterion("unfollowed_days not in", values, "unfollowedDays");
            return (Criteria) this;
        }

        public Criteria andUnfollowedDaysBetween(Integer value1, Integer value2) {
            addCriterion("unfollowed_days between", value1, value2, "unfollowedDays");
            return (Criteria) this;
        }

        public Criteria andUnfollowedDaysNotBetween(Integer value1, Integer value2) {
            addCriterion("unfollowed_days not between", value1, value2, "unfollowedDays");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table follow_up_records_history
     *
     * @mbg.generated do_not_delete_during_merge Thu Dec 26 15:31:06 CST 2024
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table follow_up_records_history
     *
     * @mbg.generated Thu Dec 26 15:31:06 CST 2024
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}