package com.linhong.boot.system.converter;

import com.linhong.boot.system.model.entity.Menu;
import com.linhong.boot.system.model.form.MenuForm;
import com.linhong.boot.system.model.vo.MenuVO;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * 菜单对象转换器
 *
 * <AUTHOR>
 * @since 2024/5/26
 */
@Mapper(componentModel = "spring")
public interface MenuConverter {

    MenuVO toVo(Menu entity);

    @Mapping(target = "params", ignore = true)
    MenuForm toForm(Menu entity);

    @Mapping(target = "params", ignore = true)
    Menu toEntity(MenuForm menuForm);

}