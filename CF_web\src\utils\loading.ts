import { ElLoading } from "element-plus";

//Loading加载

const openFullScreen = (loadingMsg = "请稍等...") => {
  const loading = ElLoading.service({
    // 打开遮罩层
    lock: true,
    text: loadingMsg,
    background: "rgba(255, 255, 255, 0.5)",
  });
  return loading;
};

//关闭loading
const closeFullScreen = (loading) => {
  if (!loading) return;
  loading.close();
};

export { openFullScreen, closeFullScreen };
