import { useUserStore, useArchitectureStoreHook } from "@/store";

import { hasAuth } from "@/plugins/permission";
import dataSystemAPI from "@/api/dataSystem/pool";
import { queryMode, switchOptions, relationOptions } from "@/enums/optionsConfig/pool";
const userStore = useUserStore();

const architectureStore = useArchitectureStoreHook();
architectureStore.getArchitectureInfo();

/**-------------------------动态获取筛选内容---------------------------**/

//催收人/组织
const userOrDept = reactive({
  label: "催收人/组织",
  type: "cascader",
  options: JSON.parse(JSON.stringify(architectureStore.architectureInfoTissue)),
  field: "userOrDept",
  placeholder: "请选择",
  disabled: userStore.userInfo.roles[0] == "YG" ? true : false,
  props: {
    value: "value",
    label: "label",
    checkStrictly: true,
  },
  rules: [{ required: true, message: "请选择催收人/组织", trigger: "blur" }],
});

/**批次号工厂函数 - 为每个页面创建独立的批次号配置**/
const createBatchNumberField = () => {
  const batchNumberField = reactive({
    label: "批次号",
    type: "select",
    options: [] as Array<{ value: string; label: string }>,
    field: "batchNumber",
    placeholder: "请选择批次号",
    multiple: true,
    collapseTags: true,
    collapseTagsTooltip: true,
    rules: "",
  });

  // 每个实例独立的防抖定时器和缓存
  let batchNumberTimer: NodeJS.Timeout | null = null;
  let lastFilterParams: string = "";
  let isUpdating = false;

  // 动态获取批次号选项（带防抖和缓存）
  const getDynamicBatchNumber = (filterParams = {}, pageType = 'pool') => {
    const paramsKey = JSON.stringify({ ...filterParams, pageType });

    if (isUpdating && lastFilterParams === paramsKey) {
      return Promise.resolve(batchNumberField.options);
    }

    if (batchNumberTimer) {
      clearTimeout(batchNumberTimer);
    }

    return new Promise((resolve, reject) => {
      batchNumberTimer = setTimeout(() => {
        if (lastFilterParams === paramsKey && batchNumberField.options.length > 0) {
          resolve(batchNumberField.options);
          return;
        }

        isUpdating = true;
        lastFilterParams = paramsKey;

        // 根据页面类型选择不同的接口，并传递必要的参数
        const baseParams = { pageSize: 9999, pageNum: 1 };

        // 不同页面类型需要传递不同的过滤参数
        let queryParams = baseParams;
        let apiCall;

        if (pageType === 'work') {
          // 作业清单需要传递userOrDept等过滤参数
          queryParams = { ...baseParams, ...filterParams };
          apiCall = dataSystemAPI.getCasepoolWorkPage(queryParams);
        } else if (pageType === 'repayment') {
          // 还款管理需要传递operatorId等过滤参数
          queryParams = { ...baseParams, ...filterParams };
          apiCall = dataSystemAPI.casepoolRepaymentPage(queryParams);
        } else {
          // 案池管理不需要额外参数
          apiCall = dataSystemAPI.getCasepoolPage(queryParams);
        }

        apiCall
          .then((response: any) => {
            // 从page接口返回的数据中提取批次号
            const records = response.data?.list || response.list || [];
            const batchNumbers = records
              .map((record: any) => record.batchNumber)
              .filter((batchNumber: string) => batchNumber && batchNumber.trim() !== '')
              .filter((value: string, index: number, self: string[]) => self.indexOf(value) === index) // 去重
              .sort(); // 排序

            const options = batchNumbers.map((batchNumber: string) => ({
              value: batchNumber,
              label: batchNumber,
            }));
            batchNumberField.options = options;
            const pageTypeName = pageType === 'work' ? '作业清单' : pageType === 'repayment' ? '还款管理' : '案池';
            console.log(`${pageTypeName}批次号选项已更新，共 ${options.length} 个选项`);
            resolve(options);
          })
          .catch((error: any) => {
            console.error("获取批次号失败，使用备用API:", error);
            // 失败时使用旧的API作为备用
            dataSystemAPI
              .casepoolGetChangeList({ batchNumber: 1 })
              .then((data: any) => {
                const options = data.map((e: any) => ({
                  value: e.batchNumber,
                  label: e.batchNumber,
                }));
                batchNumberField.options = options;
                resolve(options);
              })
              .catch(() => {
                batchNumberField.options = [];
                reject(error);
              });
          })
          .finally(() => {
            isUpdating = false;
          });
      }, 300);
    });
  };

  const getBatchNumber = (filterParams = {}, pageType = 'pool') => {
    return getDynamicBatchNumber(filterParams, pageType);
  };

  const clearBatchNumberCache = () => {
    lastFilterParams = "";
    batchNumberField.options = [];
    console.log("批次号缓存已清除");
  };

  // 初始加载批次号（默认为案池类型）
  getDynamicBatchNumber({}, 'pool');

  return {
    batchNumber: batchNumberField,
    getDynamicBatchNumber,
    getBatchNumber,
    clearBatchNumberCache,
  };
};

// 为了向后兼容，创建一个默认实例
const defaultBatchNumberInstance = createBatchNumberField();
const batchNumber = defaultBatchNumberInstance.batchNumber;
const getDynamicBatchNumber = defaultBatchNumberInstance.getDynamicBatchNumber;
const getBatchNumber = defaultBatchNumberInstance.getBatchNumber;
const clearBatchNumberCache = defaultBatchNumberInstance.clearBatchNumberCache;

/**案件类型**/
const caseType = reactive({
  label: "案件类型",
  type: "select",
  options: [],
  field: "caseType",
  placeholder: "请选择",
  rules: "",
});
const getCaseType = () => {
  dataSystemAPI.casepoolGetChangeList({ caseType: 1 }).then((data: any) => {
    caseType.options = data.map((e: any) => {
      return {
        value: e.caseType,
        label: e.caseType,
      };
    });
  });
};
getCaseType();
/**月分档**/
const entrustAgeBracket = reactive({
  label: "委托月龄分档",
  type: "select",
  options: [],
  field: "entrustSgeBracket",
  placeholder: "请选择",
  rules: "",
  hasAuth: !hasAuth("sys:pool:commissionedMonths"),
});
const getEntrustAgeBracket = () => {
  dataSystemAPI.casepoolGetChangeList({ entrustAgeBracket: 1 }).then((data: any) => {
    entrustAgeBracket.options = data.map((e: any) => {
      return {
        value: e.entrustAgeBracket,
        label: e.entrustAgeBracket,
      };
    });
  });
};
getEntrustAgeBracket();
/**佣金分档**/
const entrustCommissionBracket = reactive({
  label: "佣金分档",
  type: "select",
  options: [],
  field: "entrustCommissionBracket",
  placeholder: "请选择",
  rules: "",
  hasAuth: !hasAuth("sys:pool:CommissionFiling"),
});
const getEntrustCommissionBracket = () => {
  dataSystemAPI.casepoolGetChangeList({ entrustCommissionBracket: 1 }).then((data: any) => {
    entrustCommissionBracket.options = data.map((e: any) => {
      return {
        value: e?.entrustCommissionBracket || "",
        label: e?.entrustCommissionBracket || "",
      };
    });
  });
};
getEntrustCommissionBracket();

/** 委案机构 **/
const caseAgencyOpert = reactive({
  label: "委托机构",
  type: "select",
  options: [],
  field: "caseAgency",
  placeholder: "请选择",
  rules: "",
});
const getCaseAgencyOpert = () => {
  dataSystemAPI.casepoolGetChangeList({ caseAgency: 1 }).then((data: any) => {
    caseAgencyOpert.options = data.map((e: any) => {
      return {
        value: e?.caseAgency || "",
        label: e?.caseAgency || "",
      };
    });
  });
};
getCaseAgencyOpert();

const isMarkChange = reactive({
  label: "是否标记换单",
  type: "select",
  options: switchOptions,
  field: "isMarkChange",
  placeholder: "请选择",
  rules: "",
});

/**-----------------------页面呈现列表相关内容-----------------------------**/

// 案池筛选字段 - 完整按照案池.html的25个筛选条件布局
const screenList: any = [
  // 第1排：客户姓名、客户索引号、分案城市、当前逾期时段、入案池次数
  {
    label: "客户姓名",
    type: "input",
    options: queryMode,
    fieldKey: "key4-key",
    field: "customerName",
    placeholder: "请输入关键词",
    rules: "",
  },
  {
    label: "客户索引号",
    type: "input",
    options: queryMode,
    fieldKey: "key13-key",
    field: "customerIndexNumber",
    placeholder: "唯一标识需手动输入，粘贴去除前后空格",
    rules: "",
  },
  {
    label: "分案城市",
    type: "input",
    options: [],
    field: "caseCity", // 使用listFields中的实际字段
    placeholder: "输入城市名称",
    rules: "",
  },
  {
    label: "当前逾期时段",
    type: "input",
    options: [],
    field: "currentOverduePeriod", // 使用listFields中的实际字段
    placeholder: "输入逾期时段",
    rules: "",
  },
  {
    label: "入案池次数",
    type: "input",
    options: [],
    field: "poolCount", // 使用listFields中的实际字段
    placeholder: "输入次数",
    rules: "",
  },

  // 第2排：批次号、持卡人代码、专项类型、委托时金额段、未跟进天数
  batchNumber, // 批次号 - 使用实际存在的字段
  {
    label: "持卡人代码",
    type: "input",
    options: queryMode,
    fieldKey: "key6-key",
    field: "cardholderCode",
    placeholder: "输入持卡人代码",
    rules: "",
  },
  {
    label: "专项类型",
    type: "input",
    options: [],
    field: "specialProjectType", // 使用listFields中的实际字段
    placeholder: "输入专项类型",
    rules: "",
  },
  {
    label: "委托时金额段",
    type: "number-range",
    options: [],
    field: "entrustTotalAmount",
    placeholder: "例: 0万-5万",
    rules: "",
    minValue: "minValue",
    maxValue: "maxValue",
    valueRange: [0, 9999999999],
    minPlaceholder: "最小值",
    maxPlaceholder: "最大值",
  },
  {
    label: "未跟进天数",
    type: "input",
    options: [],
    field: "unfollowedDays",
    placeholder: "输入天数范围",
    rules: "",
  },

  // 第3排：委托机构、委托开始日、目标时段、委托时月龄分档、保留次数
  caseAgencyOpert, // 委托机构 - 使用实际存在的字段
  {
    label: "委托开始日",
    type: "date-picker",
    options: [],
    field: "entrustStartDate",
    placeholder: "选择日期范围",
    rules: "",
    formType: "daterange",
    size: "default",
  },
  {
    label: "目标时段",
    type: "input",
    options: [],
    field: "targetPeriod", // 使用listFields中的实际字段
    placeholder: "输入目标时段",
    rules: "",
  },
  entrustAgeBracket, // 委托时月龄分档 - 使用实际存在的字段
  {
    label: "保留次数",
    type: "input",
    options: [],
    field: "retentionCount", // 使用listFields中的实际字段
    placeholder: "输入保留次数",
    rules: "",
  },

  // 第4排：案件类型、委托结束日、诉讼标签、委托时佣金分档、评分档
  {
    label: "案件类型",
    type: "input",
    options: [],
    field: "caseType",
    placeholder: "输入案件类型",
    rules: "",
  },
  {
    label: "委托结束日",
    type: "date-picker",
    options: [],
    field: "entrustEndDate",
    placeholder: "选择日期范围",
    rules: "",
    formType: "daterange",
    size: "default",
  },
  {
    label: "诉讼标签",
    type: "input",
    options: [],
    field: "litigationLabel", // 使用listFields中的实际字段
    placeholder: "输入诉讼状态",
    rules: "",
  },
  entrustCommissionBracket, // 委托时佣金分档 - 使用实际存在的字段
  {
    label: "评分档",
    type: "input",
    options: [],
    field: "ratingBracket", // 使用listFields中的实际字段
    placeholder: "输入评分等级",
    rules: "",
  },

  // 第5排：失联查询结果、配置状态、(搜索重置按钮自动添加到第5排)
  {
    label: "失联查询结果",
    type: "select",
    options: [],
    field: "lostContactQueryResult",
    placeholder: "请选择",
    rules: "",
  },
  {
    label: "配置状态",
    type: "select",
    options: [],
    field: "followUpStatus",
    placeholder: "请选择",
    rules: "",
  },
];
// 案池筛选字段 - screenList已经包含了entrustAgeBracket和entrustCommissionBracket
const screenAllList: any = [...screenList];

//跟进记录
const followUpList: any = [
  {
    label: "被叫号码",
    type: "input",
    field: "calledNumber",
    placeholder: "请输入",
    size: "small",
    rules: [{ required: true, message: "请输入被叫号码", trigger: "blur" }],
  },
  {
    label: "主叫号码",
    type: "input",
    field: "callerNumber",
    placeholder: "请输入",
    size: "small",
    rules: [{ required: true, message: "请输入主叫号码", trigger: "blur" }],
  },
  {
    label: "跟进状态",
    type: "select",
    field: "collectionResult",
    options: [],
    size: "small",
    placeholder: "请选择",
    rules: [{ required: true, message: "请选择", trigger: "blur" }],
  },
  {
    label: "失联查询结果",
    type: "select",
    field: "missingContactResult",
    options: [],
    size: "small",
    placeholder: "请选择",
    labelWidth: "110px",
    rules: [], // 移除必填验证，因为添加了"全部"选项
  },
  {
    label: "回款金额",
    type: "input",
    formType: "number",
    field: "repaymentAmount",
    placeholder: "请输入",
    size: "small",
    rules: "",
  },
  {
    label: "回款日期",
    type: "date-picker",
    options: [],
    field: "repaymentDate",
    placeholder: "请选择",
    rules: "",
    formType: "date",
    size: "small",
    disabledDate: (time: Date) => {
      return time.getTime() > Date.now();
    },
  },
  {
    label: "是否标记换单", //-1:否 1:是
    type: "switch",
    field: "isMarkedForChange",
    labelWidth: "130px",
    placeholder: "请选择",
    tooltipMsg: "催收结果是《继续跟进》才能进行换单操作！",
    rules: "",
    disabled: true,
    icon: "WarnTriangleFilled",
  },
  // {
  //   label: "任务预测",
  //   type: "input",
  //   field: "taskPrediction",
  //   placeholder: "请输入",
  //   rules: "",
  // },
  {
    label: "催记内容",
    type: "input",
    formType: "textarea",
    field: "collectionNotes",
    placeholder: "请输入",
    rules: "",
    spanItem: 20,
  },
  {
    label: "提交记录",
    type: "button—submit",
    formType: "primary",
    icon: "search",
    spanItem: 4,
  },
];

// 列表表头数据 - 按照案池.html的表头顺序排列
const listFields = [
  { label: "客户姓名", prop: "customerName", fixed: "left" },
  { label: "客户索引号", prop: "customerIndexNumber", width: "180px", fixed: "left" },
  { label: "持卡人代码", prop: "cardholderCode" },
  { label: "委托金额", prop: "entrustTotalAmount" },
  { label: "委托本金", prop: "entrustPrincipalTotal" },
  { label: "批次号", prop: "batchNumber" },
  { label: "入案池次数", prop: "poolCount" },
  { label: "未跟进天数", prop: "unfollowedDays" },
  { label: "配置状态", prop: "isLock", option: { "0": "解锁", "1": "锁定" } },
  { label: "余额OPS", prop: "balanceOps" },
  { label: "本金OPS", prop: "principalOps" },
  { label: "案件类型", prop: "caseType" },
  { label: "专项类型", prop: "specialProjectType" },
  { label: "失联修复结果", prop: "lostContactQueryResult" },
  { label: "委托时逾期时段", prop: "entrustOverduePeriod" },
  { label: "当前逾期时段", prop: "currentOverduePeriod" },
  { label: "目标时段", prop: "targetPeriod" },
  { label: "分案城市", prop: "caseCity" },
  { label: "户籍城市", prop: "householdCity" },
  { label: "客户年龄", prop: "customerAge" },
  { label: "职业", prop: "occupation" },
  { label: "开卡日期", prop: "cardOpeningDate" },
  { label: "信用额度", prop: "creditLimit" },
  { label: "最后缴款日期", prop: "lastPaymentDate" },
  { label: "账户号后7位", prop: "accountNumberLast7" },
  { label: "委托时月龄分档", prop: "entrustAgeBracket" },
  { label: "委托时金额段", prop: "entrustAmountBracket" },
  { label: "委托时佣金分档", prop: "entrustCommissionBracket" },
  { label: "评分档", prop: "ratingBracket" },
  { label: "是否诉讼(含风险代理)", prop: "isLitigation" },
  { label: "客户当月主动还款金额", prop: "customerCurrentMonthRepayment" },
  { label: "客户前日主动还款金额", prop: "customerPreviousDayRepayment" },
  { label: "最后跟进日期", prop: "lastFollowUpDate" },
  { label: "委托开始日", prop: "entrustStartDate" },
  { label: "委托结束日", prop: "entrustEndDate" },
  { label: "新旧案标志", prop: "newOldCaseFlag" },
  { label: "委托机构", prop: "caseAgency" },
  { label: "保留次数", prop: "retentionCount" },
  { label: "个性化分期状态", prop: "personalizedInstallmentStatus" },
  { label: "个性化分期履约状态", prop: "personalizedInstallmentFulfillmentStatus" },
  { label: "投诉标签", prop: "complaintLabel" },
  { label: "个分履约标签", prop: "personalizedFulfillmentLabel" },
  { label: "诉讼标签", prop: "litigationLabel" },
  { label: "智能语音标签", prop: "smartVoice_Label" },
  { label: "专项标签", prop: "specialProjectLabel" },
  { label: "是否诉讼(结佣)", prop: "isLitigationForCommission" },
  // {
  //   label: "是否换单",
  //   prop: "isMarkChange",
  //   option: {
  //     "-1": "否",
  //     1: "是",
  //   },
  // },
  // { label: "委托总金额", prop: "entrustTotalAmount" },
  // { label: "委托本金总额", prop: "entrustPrincipalTotal" },

  // { label: "跟进状态", prop: "followUpStatus" },
  // { label: "催收人/组织", prop: "userOrDept" },
  // { label: "评分档", prop: "ratingBracket" },
  // { label: "抢案用户", prop: "caseGrabbingUser" },
  // { label: "委案机构", prop: "caseAgency‌" },
];

// 作业清单专用筛选条件 - 标签和布局按照HTML，字段对应Vue
const jobScreenList: any = [
  // 第1排：作业员/组织(userOrDept)、客户姓名、客户索引号、分案城市、当前逾期时段
  {
    label: "客户姓名",
    type: "input",
    options: queryMode,
    fieldKey: "key4-key",
    field: "customerName",
    placeholder: "请输入关键词",
    rules: "",
  },
  {
    label: "客户索引号",
    type: "input",
    options: queryMode,
    fieldKey: "key13-key",
    field: "customerIndexNumber",
    placeholder: "唯一标识需手动输入",
    rules: "",
  },
  {
    label: "分案城市",
    type: "input",
    options: [],
    field: "caseCity",
    placeholder: "输入城市名称",
    rules: "",
  },
  {
    label: "当前逾期时段",
    type: "input",
    options: [],
    field: "currentOverduePeriod",
    placeholder: "输入逾期时段",
    rules: "",
  },

  // 第2排：批次号(batchNumber)、持卡人代码、专项类型、委托时金额段、未跟进天数
  // 注意：批次号字段在jobList.vue中动态替换为独立实例
  {
    label: "批次号",
    type: "select",
    options: [],
    field: "batchNumber",
    placeholder: "请选择批次号",
    multiple: true,
    rules: "",
  },
  {
    label: "持卡人代码",
    type: "input",
    options: [],
    field: "cardholderCode",
    placeholder: "输入持卡人代码",
    rules: "",
  },
  {
    label: "专项类型",
    type: "input",
    options: [],
    field: "specialProjectType",
    placeholder: "输入专项类型",
    rules: "",
  },
  {
    label: "委托时金额段",
    type: "number-range",
    options: [],
    field: "entrustAmountBracket",
    placeholder: "例: 0万-5万",
    rules: "",
    minPlaceholder: "最小值",
    maxPlaceholder: "最大值",
  },
  {
    label: "未跟进天数",
    type: "input",
    options: [],
    field: "unfollowedDays",
    placeholder: "输入天数范围",
    rules: "",
  },

  // 第3排：委托机构、委托开始日、目标时段、委托时月龄分档、保留次数
  {
    label: "委托机构",
    type: "input",
    options: [],
    field: "caseAgency", // Vue中对应委托机构的字段
    placeholder: "输入委托机构名称",
    rules: "",
  },
  {
    label: "委托开始日",
    type: "input",
    options: [],
    field: "entrustStartDate",
    placeholder: "输入委托开始日",
    rules: "",
  },
  {
    label: "目标时段",
    type: "input",
    options: [],
    field: "targetPeriod",
    placeholder: "输入目标时段",
    rules: "",
  },
  {
    label: "委托时月龄分档",
    type: "input",
    options: [],
    field: "entrustAgeBracket",
    placeholder: "输入月份区间",
    rules: "",
  },
  {
    label: "保留次数",
    type: "input",
    options: [],
    field: "retentionCount",
    placeholder: "输入保留次数",
    rules: "",
  },

  // 第4排：案件类型、委托结束日、诉讼标签、委托时佣金分档、评分档
  {
    label: "案件类型",
    type: "input",
    options: [],
    field: "caseType",
    placeholder: "输入案件类型",
    rules: "",
  },
  {
    label: "委托结束日",
    type: "input",
    options: [],
    field: "entrustEndDate",
    placeholder: "输入委托结束日",
    rules: "",
  },
  {
    label: "诉讼标签",
    type: "input",
    options: [],
    field: "litigationLabel",
    placeholder: "输入诉讼状态",
    rules: "",
  },
  {
    label: "委托时佣金分档",
    type: "input",
    options: [],
    field: "entrustCommissionBracket",
    placeholder: "输入佣金等级",
    rules: "",
  },
  {
    label: "评分档",
    type: "input",
    options: [],
    field: "ratingBracket",
    placeholder: "输入评分等级",
    rules: "",
  },

  // 第5排：跟进状态、是否标记换单、失联修复结果
  {
    label: "跟进状态",
    type: "select",
    options: [
      { value: "", label: "全部" },
      { value: "继续跟进", label: "继续跟进" },
      { value: "空号停机", label: "空号停机" },
      { value: "重点跟进", label: "重点跟进" },
      { value: "谈方案", label: "谈方案" },
      { value: "线下分期", label: "线下分期" },
      { value: "个性化分期", label: "个性化分期" },
      { value: "待减免", label: "待减免" },
      { value: "投诉倾向", label: "投诉倾向" },
      { value: "要求退案", label: "要求退案" },
    ],
    field: "followUpStatus",
    placeholder: "请选择跟进状态",
    rules: "",
  },
  {
    label: "是否标记换单",
    type: "select",
    options: [
      { value: "", label: "全部" },
      { value: "1", label: "是" },
      { value: "-1", label: "否" },
    ],
    field: "isMarkedForChange",
    placeholder: "请选择",
    rules: "",
  },
  {
    label: "失联查询结果",
    type: "select",
    options: [], // 这个会在initOptions中动态设置
    field: "lostContactQueryResult",
    placeholder: "请选择",
    rules: "",
  },
];

// 作业清单表头 - 按照HTML中的顺序排列
const jobListFields = [
  // 固定列
  { label: "客户姓名", prop: "customerName", fixed: "left" },
  { label: "客户索引号", prop: "customerIndexNumber", width: "180px", fixed: "left" },

  // 按照HTML顺序排列的列
  { label: "持卡人代码", prop: "cardholderCode" },
  { label: "委托金额", prop: "entrustTotalAmount" },
  { label: "委托本金", prop: "entrustPrincipalTotal" },
  { label: "批次号", prop: "batchNumber" },
  { label: "余额OPS", prop: "balanceOps" },
  { label: "本金OPS", prop: "principalOps" },
  { label: "未跟进天数", prop: "unfollowedDays" },
  { label: "跟进状态", prop: "followUpStatus" },
  { label: "作业员", prop: "userOrDept" },
  { label: "部门", prop: "deptName" },
  { label: "委托机构", prop: "caseAgency" },
  { label: "失联查询结果", prop: "lostContactQueryResult" },
  {
    label: "是否标记换单",
    prop: "isMarkChange",
    option: {
      "-1": "否",
      1: "是",
    },
  },
  { label: "保留次数", prop: "retentionCount" },
  { label: "案件类型", prop: "caseType" },
  { label: "专项类型", prop: "specialProjectType" },
  { label: "委托开始日", prop: "entrustStartDate" },
  { label: "委托结束日", prop: "entrustEndDate" },
  { label: "开卡日期", prop: "cardOpeningDate" },
  { label: "信用额度", prop: "creditLimit" },
  { label: "最后缴款日期", prop: "lastPaymentDate" },
  { label: "委托时逾期时段", prop: "entrustOverduePeriod" },
  { label: "目标时段", prop: "targetPeriod" },
  { label: "分案城市", prop: "caseCity" },
  { label: "最后跟进日期", prop: "lastFollowUpDate" },
  { label: "新旧案标志", prop: "newOldCaseFlag" },
  { label: "当前逾期时段", prop: "currentOverduePeriod" },
  { label: "户籍城市", prop: "householdCity" },
  { label: "客户年龄", prop: "customerAge" },
  { label: "职业", prop: "occupation" },
  { label: "账户号后7位", prop: "accountNumberLast7" },
  { label: "委托时月龄分档", prop: "entrustAgeBracket" },
  { label: "委托时金额段", prop: "entrustAmountBracket" },
  { label: "委托时佣金分档", prop: "entrustCommissionBracket" },
  { label: "评分档", prop: "ratingBracket" },
  { label: "是否诉讼(含风险代理)", prop: "isLitigation" },
  { label: "客户当月主动还款金额", prop: "customerCurrentMonthRepayment" },
  { label: "客户前日主动还款金额", prop: "customerPreviousDayRepayment" },
  { label: "个性化分期状态", prop: "personalizedInstallmentStatus" },
  { label: "个性化分期履约状态", prop: "personalizedInstallmentFulfillmentStatus" },
  { label: "投诉标签", prop: "complaintLabel" },
  { label: "个分履约标签", prop: "personalizedFulfillmentLabel" },
  { label: "诉讼标签", prop: "litigationLabel" },
  { label: "智能语音标签", prop: "smartVoice_Label" },
  { label: "专项标签", prop: "specialProjectLabel" },
  { label: "是否诉讼(结佣)", prop: "isLitigationForCommission" },
];

//联系人目录
const connectionMune = [
  { label: "序号", prop: "id", width: "60px" },
  { label: "姓名", prop: "name" },
  { label: "关系", prop: "relationship" },
  { label: "手机号码", prop: "phone" },
];

// 新增联系人字段
const contactPersonList: any = [
  {
    label: "姓名",
    type: "input",
    field: "name",
    placeholder: "请输入",
    labelWidth: "80px",
    rules: [{ required: true, message: "请输入姓名", trigger: "blur" }],
  },
  {
    label: "关系",
    type: "select",
    field: "relationship",
    options: relationOptions,
    labelWidth: "80px",
    placeholder: "请选择",
    rules: [{ required: true, message: "用户昵称不能为空", trigger: "blur" }],
  },
  {
    label: "手机号码",
    type: "input",
    formType: "tel",
    field: "phone",
    maxlength: "11",
    labelWidth: "80px",
    placeholder: "请输入",
    rules: [
      { required: true, message: "请输入手机号码", trigger: "blur" },
      { pattern: /^1[3456789]\d{9}$/, message: "手机号码格式不正确", trigger: "blur" },
    ],
  },
];

//详情总列表目录
const catalogueMune = [
  { label: "姓名", prop: "customerName", width: "70px", isClick: true },
  { label: "委托金额", prop: "entrustTotalAmount", width: "110px", sortable: true },
  { label: "未跟进天数", prop: "unfollowedDays", sortable: true },
];

//催记表头数据 - 按照案件详情.html的顺序排列
const reminderMune = [
  { label: "序号", prop: "sort", width: "80px" },
  { label: "跟进时间", prop: "updatedAt", width: "200px" },
  { label: "被叫号码", prop: "calledNumber", width: "140px" },
  { label: "主叫号码", prop: "callerNumber", width: "140px" },
  { label: "还款金额", prop: "repaymentAmount" }, // 改名：回款金额 -> 还款金额
  { label: "还款时间", prop: "repaymentDate" }, // 新增字段
  { label: "跟进记录", prop: "collectionNotes", noEllipsis: true }, // 改名：催记内容 -> 跟进记录，禁用默认tooltip
  { label: "跟进状态", prop: "collectionResult" },
  { 
    label: "失联查询结果", 
    prop: "missingContactResult", 
    width: "120px",
    noEllipsis: true // 不使用省略号，完全展示内容
  },
  {
    label: "是否标记换单",
    prop: "isMarkedForChange",
    width: "120px",
    noEllipsis: true, // 不使用省略号，完全展示内容
    option: {
      "-1": "否",
      1: "是",
    },
  },
  { label: "作业员", prop: "operationName" }, // 改名：操作员 -> 作业员
];

//基本信息 - 按照案件详情.html的排序
const basicInformationMune = [
  // 第一行：客户姓名、信用额度、批次号、诉讼标签
  { label: "客户姓名", prop: "customerName", isEditior: true, isShow: true },
  { label: "信用额度", prop: "creditLimit", isShow: true },
  { label: "批次号", prop: "batchNumber", isShow: true },
  { label: "诉讼标签", prop: "litigationLabel", isShow: true },

  // 第二行：客户索引号、委托机构、余额OPS、委托开始日
  { label: "客户索引号", prop: "customerIndexNumber", isShow: true },
  { label: "委托机构", prop: "caseAgency", isShow: true },
  { label: "余额OPS", prop: "balanceOps", isShow: true },
  { label: "委托开始日", prop: "entrustStartDate", isShow: true },

  // 第三行：保留次数、持卡人代码、委托金额、本金OPS
  { label: "保留次数", prop: "retentionCount", isShow: true },
  { label: "持卡人代码", prop: "cardholderCode", isEditior: true, isShow: true },
  { label: "委托金额", prop: "entrustTotalAmount", isShow: true },
  { label: "本金OPS", prop: "principalOps", isShow: true },

  // 第四行：委托结束日、投诉标签、身份证号、委托本金
  { label: "委托结束日", prop: "entrustEndDate", isShow: true },
  { label: "投诉标签", prop: "complaintLabel", isShow: true },
  { label: "身份证号", prop: "cardholderIdNumber", isEditior: true, isShow: true },
  { label: "委托本金", prop: "entrustPrincipalTotal", isShow: true },

  // 第五行：专项类型、最后跟进日期、专项标签、户籍城市
  { label: "专项类型", prop: "specialProjectType", isShow: true },
  { label: "最后跟进日期", prop: "lastFollowUpDate", isShow: true },
  { label: "专项标签", prop: "specialProjectLabel", isShow: true },
  { label: "户籍城市", prop: "householdCity", isEditior: true, isShow: true },

  // 第六行：案件类型、开卡日期、最后缴款日期、新旧案标志
  { label: "案件类型", prop: "caseType", isShow: true },
  { label: "开卡日期", prop: "cardOpeningDate", isShow: true },
  { label: "最后缴款日期", prop: "lastPaymentDate", isShow: true },
  { label: "新旧案标志", prop: "newOldCaseFlag", isShow: true },

  // 第七行：工作单位（可编辑字段）
  { label: "工作单位", prop: "workUnit", isEditior: true, isShow: true },
];

//职业信息 - 从职业字段开始的所有辅助信息（参考案件详情.html中sub-info区域）
const occupationInformationMune = [
  // 职业及相关辅助信息区域 - 按照HTML中sub-info的顺序
  { label: "职业", prop: "occupation" },
  { label: "客户当月主动还款金额", prop: "customerCurrentMonthRepayment" },
  { label: "最后缴款日期", prop: "lastPaymentDate" },
  { label: "当前逾期时段", prop: "currentOverduePeriod" },
  { label: "委托时月龄分档", prop: "entrustAgeBracket", hasAuth: !hasAuth("sys:pool:commissionedMonths") },
  { label: "智能语音标签", prop: "smartVoice_Label" },
  { label: "客户年龄", prop: "customerAge" },
  { label: "客户前日主动还款金额", prop: "customerPreviousDayRepayment" },
  { label: "最后跟进日期", prop: "lastFollowUpDate" },
  { label: "委托时逾期时段", prop: "entrustOverduePeriod" },
  { label: "个分履约标签", prop: "personalizedFulfillmentLabel" },
  { label: "账户号后七位", prop: "accountNumberLast7" },
  { label: "个性化履约状态", prop: "personalizedInstallmentFulfillmentStatus" },
  { label: "目标时段", prop: "targetPeriod" },
  { label: "是否诉讼（含风险代理）", prop: "isLitigation" },
  { label: "委托时金额段", prop: "entrustAmountBracket" },
  { label: "是否诉讼（结佣）", prop: "isLitigationForCommission" },
  { label: "个性化分期状态", prop: "personalizedInstallmentStatus" },

  // 其他字段
  { label: "分案城市", prop: "caseCity" },
  { label: "佣金分档", prop: "entrustCommissionBracket", hasAuth: !hasAuth("sys:pool:CommissionFiling") },
  { label: "卡号", prop: "bankCardNumber", isEditior: true, isShow: true },
];

/**
 * 新增统计表单配置
 */
const statisticsFormList = ref([
  {
    label: "协商总金额",
    type: "input",
    field: "negotiationAmount",
    placeholder: "请输入协商总金额",
    labelWidth: "120px",
    rules: [{ required: true, message: "请输入协商总金额", trigger: "blur" }],
  },
  {
    label: "分期方式",
    type: "select",
    field: "installmentMethod",
    placeholder: "请选择分期方式",
    labelWidth: "120px",
    options: [
      { label: "月付", value: "monthly" },
      { label: "季付", value: "quarterly" },
      { label: "半年付", value: "semiannual" },
      { label: "年付", value: "annual" },
    ],
    rules: [{ required: true, message: "请选择分期方式", trigger: "blur" }],
  },
  {
    label: "分期数",
    type: "input",
    field: "installmentCount",
    placeholder: "请输入分期数",
    labelWidth: "120px",
    rules: [{ required: true, message: "请输入分期数", trigger: "blur" }],
  },
  {
    label: "已还款金额",
    type: "input",
    field: "paidAmount",
    placeholder: "请输入已还款金额",
    labelWidth: "120px",
    rules: [{ required: true, message: "请输入已还款金额", trigger: "blur" }],
  },
  {
    label: "次月还款金额",
    type: "input",
    field: "nextPaymentAmount",
    placeholder: "请输入次月还款金额",
    labelWidth: "120px",
    rules: [{ required: true, message: "请输入次月还款金额", trigger: "blur" }],
  },
  {
    label: "次月还款时间",
    type: "date-picker",
    field: "nextPaymentDate",
    placeholder: "请选择次月还款时间",
    formType: "date",
    labelWidth: "120px",
    rules: [{ required: true, message: "请选择次月还款时间", trigger: "blur" }],
  },
  {
    label: "登记时间",
    type: "date-picker",
    field: "registerTime",
    placeholder: "请选择登记时间",
    formType: "datetime",
    labelWidth: "120px",
    rules: [{ required: true, message: "请选择登记时间", trigger: "blur" }],
  },
  {
    label: "登记人",
    type: "input",
    field: "registrar",
    placeholder: "请输入登记人",
    labelWidth: "120px",
    rules: [{ required: true, message: "请输入登记人", trigger: "blur" }],
  },
  {
    label: "备注",
    type: "input",
    formType: "textarea",
    field: "remarks",
    placeholder: "请输入备注信息，如特殊还款约定、沟通记录等",
    labelWidth: "120px",
    rules: "",
  },
]);

export {
  screenAllList,
  screenList,
  jobScreenList,
  listFields,
  connectionMune,
  catalogueMune,
  reminderMune,
  basicInformationMune,
  occupationInformationMune, // 新增：职业信息字段配置
  followUpList,
  contactPersonList,
  userOrDept,
  batchNumber,
  caseType,
  jobListFields,
  isMarkChange,
  statisticsFormList,
  getDynamicBatchNumber,
  getBatchNumber,
  clearBatchNumberCache,
  createBatchNumberField, // 新增：批次号工厂函数
};
