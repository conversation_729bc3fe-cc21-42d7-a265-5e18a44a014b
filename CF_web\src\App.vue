<template>
  <el-config-provider :locale="locale" :size="size">
    <!-- 开启水印   :content="defaultSettings.watermarkContent"-->
    <el-watermark v-if="watermarkEnabled" :font="{ color: fontColor }" :content="userDate" :z-index="9999"
      class="wh-full">
      <router-view />
    </el-watermark>
    <!-- 关闭水印 -->
    <router-view v-else />
  </el-config-provider>
</template>

<script setup lang="ts">
import { useAppStore, useSettingsStore } from "@/store";
import defaultSettings from "@/settings";
import { ThemeEnum } from "@/enums/ThemeEnum";
import { SizeEnum } from "@/enums/SizeEnum";
import { useUserStore } from "@/store/modules/user";

const userStore = useUserStore();

// 动态计算水印内容，使用当前登录用户的username
const userDate = computed(() => {
  const username = userStore.userInfo.username || '未知用户';
  return `${username}`;
});

const appStore = useAppStore();
const settingsStore = useSettingsStore();

const locale = computed(() => appStore.locale);
const size = computed(() => appStore.size as SizeEnum);
const watermarkEnabled = computed(() => settingsStore.watermarkEnabled);

// 明亮/暗黑主题水印字体颜色适配
const fontColor = computed(() => {
  return settingsStore.theme === ThemeEnum.DARK ? "rgba(255, 255, 255, .15)" : "rgba(0, 0, 0, .15)";
});
</script>
<style>
.app-container {
  /* height: 100%; */
}
</style>
