import { createApp } from "vue";
import App from "./App.vue";
import setupPlugins from "@/plugins";
import "lodash-js";
import service from "@/utils/request";

// 本地SVG图标
import "virtual:svg-icons-register";

// 样式
import "element-plus/theme-chalk/dark/css-vars.css";
// 暗黑模式自定义变量
import "@/styles/dark/css-vars.css";
import "@/styles/index.scss";
import "uno.css";
import "animate.css";

const app = createApp(App);
if (typeof window !== "undefined") {
  (window as any).axios = service;
}

// 注册插件
app.use(setupPlugins);
app.mount("#app");
