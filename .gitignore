# IDE
.idea/
*.iml
*.iws
*.ipr
AugmentWebviewStateStore.xml
misc.xml

# Build files
target/
build/
out/
dist/
dist.tar

# Frontend build files
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Large files
*.jar
*.sql
*.war
*.ear
*.nar

# OS
.DS_Store
Thumbs.db
*.swp
*.swo

# Logs
*.log
logs/

# Temporary files
*.tmp
*.temp
*.bak
*.backup

# Database
*.db
*.sqlite
*.sqlite3

# Configuration files (if they contain sensitive info)
application-local.properties
application-dev.properties

# Web assets
CF_web/node_modules/
CF_web/dist/
CF_web/dist.rar
CF_web/__MACOSX/
CF_web/npminstall-debug.log
CF_web/*.timestamp-*
static/uploads/

# Maven
.mvn/
mvnw
mvnw.cmd

# Gradle
.gradle/
gradle/
gradlew
gradlew.bat
