package com.linhong.boot.management.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.linhong.boot.management.model.entity.FollowUpRecords;
import com.linhong.boot.management.model.entity.FollowUpRecordsExample;
import com.linhong.boot.management.model.entity.FollowUpRecordsWithBLOBs;

@Mapper
public interface FollowUpRecordsMapper extends BaseMapper<FollowUpRecords>{
	
	
	/**
	 * 查询所有案件未跟进小时数
	 * @return
	 */
	public List<FollowUpRecords> TaskUnFollowDay();
	
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table follow_up_records
     *
     * @mbg.generated Tue Nov 26 07:12:46 HKT 2024
     */
    long countByExample(FollowUpRecordsExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table follow_up_records
     *
     * @mbg.generated Tue Nov 26 07:12:46 HKT 2024
     */
    int deleteByExample(FollowUpRecordsExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table follow_up_records
     *
     * @mbg.generated Tue Nov 26 07:12:46 HKT 2024
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table follow_up_records
     *
     * @mbg.generated Tue Nov 26 07:12:46 HKT 2024
     */
    int insert(FollowUpRecords row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table follow_up_records
     *
     * @mbg.generated Tue Nov 26 07:12:46 HKT 2024
     */
    int insertSelective(FollowUpRecords row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table follow_up_records
     *
     * @mbg.generated Tue Nov 26 07:12:46 HKT 2024
     */
    List<FollowUpRecordsWithBLOBs> selectByExampleWithBLOBs(FollowUpRecordsExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table follow_up_records
     *
     * @mbg.generated Tue Nov 26 07:12:46 HKT 2024
     */
    List<FollowUpRecords> selectByExample(FollowUpRecordsExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table follow_up_records
     *
     * @mbg.generated Tue Nov 26 07:12:46 HKT 2024
     */
    FollowUpRecordsWithBLOBs selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table follow_up_records
     *
     * @mbg.generated Tue Nov 26 07:12:46 HKT 2024
     */
    int updateByExampleSelective(@Param("row") FollowUpRecordsWithBLOBs row, @Param("example") FollowUpRecordsExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table follow_up_records
     *
     * @mbg.generated Tue Nov 26 07:12:46 HKT 2024
     */
    int updateByExampleWithBLOBs(@Param("row") FollowUpRecordsWithBLOBs row, @Param("example") FollowUpRecordsExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table follow_up_records
     *
     * @mbg.generated Tue Nov 26 07:12:46 HKT 2024
     */
    int updateByExample(@Param("row") FollowUpRecords row, @Param("example") FollowUpRecordsExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table follow_up_records
     *
     * @mbg.generated Tue Nov 26 07:12:46 HKT 2024
     */
    int updateByPrimaryKeySelective(FollowUpRecordsWithBLOBs row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table follow_up_records
     *
     * @mbg.generated Tue Nov 26 07:12:46 HKT 2024
     */
    int updateByPrimaryKeyWithBLOBs(FollowUpRecordsWithBLOBs row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table follow_up_records
     *
     * @mbg.generated Tue Nov 26 07:12:46 HKT 2024
     */
    int updateByPrimaryKey(FollowUpRecords row);
}