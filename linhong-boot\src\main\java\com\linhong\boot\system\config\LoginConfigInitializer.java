package com.linhong.boot.system.config;

import com.linhong.boot.system.service.LoginConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

/**
 * 登录页配置初始化器
 * 应用启动时预加载配置到缓存
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class LoginConfigInitializer implements ApplicationRunner {

    private final LoginConfigService loginConfigService;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        try {
            log.info("开始初始化登录页配置缓存...");
            
            // 预加载配置到缓存
            loginConfigService.getLoginPageConfig();
            
            log.info("登录页配置缓存初始化完成");
        } catch (Exception e) {
            log.error("登录页配置缓存初始化失败", e);
        }
    }
}
