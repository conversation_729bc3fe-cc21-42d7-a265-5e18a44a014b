package com.linhong.boot.management.model.entity;

import java.util.ArrayList;
import java.util.List;

public class KeyValueStoreExample {
    /**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table key_value_store
	 * @mbg.generated  Fri Dec 06 15:01:17 CST 2024
	 */
	protected String orderByClause;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table key_value_store
	 * @mbg.generated  Fri Dec 06 15:01:17 CST 2024
	 */
	protected boolean distinct;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table key_value_store
	 * @mbg.generated  Fri Dec 06 15:01:17 CST 2024
	 */
	protected List<Criteria> oredCriteria;

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table key_value_store
	 * @mbg.generated  Fri Dec 06 15:01:17 CST 2024
	 */
	public KeyValueStoreExample() {
		oredCriteria = new ArrayList<>();
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table key_value_store
	 * @mbg.generated  Fri Dec 06 15:01:17 CST 2024
	 */
	public void setOrderByClause(String orderByClause) {
		this.orderByClause = orderByClause;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table key_value_store
	 * @mbg.generated  Fri Dec 06 15:01:17 CST 2024
	 */
	public String getOrderByClause() {
		return orderByClause;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table key_value_store
	 * @mbg.generated  Fri Dec 06 15:01:17 CST 2024
	 */
	public void setDistinct(boolean distinct) {
		this.distinct = distinct;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table key_value_store
	 * @mbg.generated  Fri Dec 06 15:01:17 CST 2024
	 */
	public boolean isDistinct() {
		return distinct;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table key_value_store
	 * @mbg.generated  Fri Dec 06 15:01:17 CST 2024
	 */
	public List<Criteria> getOredCriteria() {
		return oredCriteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table key_value_store
	 * @mbg.generated  Fri Dec 06 15:01:17 CST 2024
	 */
	public void or(Criteria criteria) {
		oredCriteria.add(criteria);
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table key_value_store
	 * @mbg.generated  Fri Dec 06 15:01:17 CST 2024
	 */
	public Criteria or() {
		Criteria criteria = createCriteriaInternal();
		oredCriteria.add(criteria);
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table key_value_store
	 * @mbg.generated  Fri Dec 06 15:01:17 CST 2024
	 */
	public Criteria createCriteria() {
		Criteria criteria = createCriteriaInternal();
		if (oredCriteria.size() == 0) {
			oredCriteria.add(criteria);
		}
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table key_value_store
	 * @mbg.generated  Fri Dec 06 15:01:17 CST 2024
	 */
	protected Criteria createCriteriaInternal() {
		Criteria criteria = new Criteria();
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table key_value_store
	 * @mbg.generated  Fri Dec 06 15:01:17 CST 2024
	 */
	public void clear() {
		oredCriteria.clear();
		orderByClause = null;
		distinct = false;
	}

	/**
	 * This class was generated by MyBatis Generator. This class corresponds to the database table key_value_store
	 * @mbg.generated  Fri Dec 06 15:01:17 CST 2024
	 */
	protected abstract static class GeneratedCriteria {
		protected List<Criterion> criteria;

		protected GeneratedCriteria() {
			super();
			criteria = new ArrayList<>();
		}

		public boolean isValid() {
			return criteria.size() > 0;
		}

		public List<Criterion> getAllCriteria() {
			return criteria;
		}

		public List<Criterion> getCriteria() {
			return criteria;
		}

		protected void addCriterion(String condition) {
			if (condition == null) {
				throw new RuntimeException("Value for condition cannot be null");
			}
			criteria.add(new Criterion(condition));
		}

		protected void addCriterion(String condition, Object value, String property) {
			if (value == null) {
				throw new RuntimeException("Value for " + property + " cannot be null");
			}
			criteria.add(new Criterion(condition, value));
		}

		protected void addCriterion(String condition, Object value1, Object value2, String property) {
			if (value1 == null || value2 == null) {
				throw new RuntimeException("Between values for " + property + " cannot be null");
			}
			criteria.add(new Criterion(condition, value1, value2));
		}

		public Criteria andIdIsNull() {
			addCriterion("id is null");
			return (Criteria) this;
		}

		public Criteria andIdIsNotNull() {
			addCriterion("id is not null");
			return (Criteria) this;
		}

		public Criteria andIdEqualTo(Integer value) {
			addCriterion("id =", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotEqualTo(Integer value) {
			addCriterion("id <>", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdGreaterThan(Integer value) {
			addCriterion("id >", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdGreaterThanOrEqualTo(Integer value) {
			addCriterion("id >=", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdLessThan(Integer value) {
			addCriterion("id <", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdLessThanOrEqualTo(Integer value) {
			addCriterion("id <=", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdIn(List<Integer> values) {
			addCriterion("id in", values, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotIn(List<Integer> values) {
			addCriterion("id not in", values, "id");
			return (Criteria) this;
		}

		public Criteria andIdBetween(Integer value1, Integer value2) {
			addCriterion("id between", value1, value2, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotBetween(Integer value1, Integer value2) {
			addCriterion("id not between", value1, value2, "id");
			return (Criteria) this;
		}

		public Criteria andCaseKeyIsNull() {
			addCriterion("case_key is null");
			return (Criteria) this;
		}

		public Criteria andCaseKeyIsNotNull() {
			addCriterion("case_key is not null");
			return (Criteria) this;
		}

		public Criteria andCaseKeyEqualTo(String value) {
			addCriterion("case_key =", value, "caseKey");
			return (Criteria) this;
		}

		public Criteria andCaseKeyNotEqualTo(String value) {
			addCriterion("case_key <>", value, "caseKey");
			return (Criteria) this;
		}

		public Criteria andCaseKeyGreaterThan(String value) {
			addCriterion("case_key >", value, "caseKey");
			return (Criteria) this;
		}

		public Criteria andCaseKeyGreaterThanOrEqualTo(String value) {
			addCriterion("case_key >=", value, "caseKey");
			return (Criteria) this;
		}

		public Criteria andCaseKeyLessThan(String value) {
			addCriterion("case_key <", value, "caseKey");
			return (Criteria) this;
		}

		public Criteria andCaseKeyLessThanOrEqualTo(String value) {
			addCriterion("case_key <=", value, "caseKey");
			return (Criteria) this;
		}

		public Criteria andCaseKeyLike(String value) {
			addCriterion("case_key like", value, "caseKey");
			return (Criteria) this;
		}

		public Criteria andCaseKeyNotLike(String value) {
			addCriterion("case_key not like", value, "caseKey");
			return (Criteria) this;
		}

		public Criteria andCaseKeyIn(List<String> values) {
			addCriterion("case_key in", values, "caseKey");
			return (Criteria) this;
		}

		public Criteria andCaseKeyNotIn(List<String> values) {
			addCriterion("case_key not in", values, "caseKey");
			return (Criteria) this;
		}

		public Criteria andCaseKeyBetween(String value1, String value2) {
			addCriterion("case_key between", value1, value2, "caseKey");
			return (Criteria) this;
		}

		public Criteria andCaseKeyNotBetween(String value1, String value2) {
			addCriterion("case_key not between", value1, value2, "caseKey");
			return (Criteria) this;
		}

		public Criteria andCaseValueIsNull() {
			addCriterion("case_value is null");
			return (Criteria) this;
		}

		public Criteria andCaseValueIsNotNull() {
			addCriterion("case_value is not null");
			return (Criteria) this;
		}

		public Criteria andCaseValueEqualTo(String value) {
			addCriterion("case_value =", value, "caseValue");
			return (Criteria) this;
		}

		public Criteria andCaseValueNotEqualTo(String value) {
			addCriterion("case_value <>", value, "caseValue");
			return (Criteria) this;
		}

		public Criteria andCaseValueGreaterThan(String value) {
			addCriterion("case_value >", value, "caseValue");
			return (Criteria) this;
		}

		public Criteria andCaseValueGreaterThanOrEqualTo(String value) {
			addCriterion("case_value >=", value, "caseValue");
			return (Criteria) this;
		}

		public Criteria andCaseValueLessThan(String value) {
			addCriterion("case_value <", value, "caseValue");
			return (Criteria) this;
		}

		public Criteria andCaseValueLessThanOrEqualTo(String value) {
			addCriterion("case_value <=", value, "caseValue");
			return (Criteria) this;
		}

		public Criteria andCaseValueLike(String value) {
			addCriterion("case_value like", value, "caseValue");
			return (Criteria) this;
		}

		public Criteria andCaseValueNotLike(String value) {
			addCriterion("case_value not like", value, "caseValue");
			return (Criteria) this;
		}

		public Criteria andCaseValueIn(List<String> values) {
			addCriterion("case_value in", values, "caseValue");
			return (Criteria) this;
		}

		public Criteria andCaseValueNotIn(List<String> values) {
			addCriterion("case_value not in", values, "caseValue");
			return (Criteria) this;
		}

		public Criteria andCaseValueBetween(String value1, String value2) {
			addCriterion("case_value between", value1, value2, "caseValue");
			return (Criteria) this;
		}

		public Criteria andCaseValueNotBetween(String value1, String value2) {
			addCriterion("case_value not between", value1, value2, "caseValue");
			return (Criteria) this;
		}

		public Criteria andCaseGroupIsNull() {
			addCriterion("case_group is null");
			return (Criteria) this;
		}

		public Criteria andCaseGroupIsNotNull() {
			addCriterion("case_group is not null");
			return (Criteria) this;
		}

		public Criteria andCaseGroupEqualTo(String value) {
			addCriterion("case_group =", value, "caseGroup");
			return (Criteria) this;
		}

		public Criteria andCaseGroupNotEqualTo(String value) {
			addCriterion("case_group <>", value, "caseGroup");
			return (Criteria) this;
		}

		public Criteria andCaseGroupGreaterThan(String value) {
			addCriterion("case_group >", value, "caseGroup");
			return (Criteria) this;
		}

		public Criteria andCaseGroupGreaterThanOrEqualTo(String value) {
			addCriterion("case_group >=", value, "caseGroup");
			return (Criteria) this;
		}

		public Criteria andCaseGroupLessThan(String value) {
			addCriterion("case_group <", value, "caseGroup");
			return (Criteria) this;
		}

		public Criteria andCaseGroupLessThanOrEqualTo(String value) {
			addCriterion("case_group <=", value, "caseGroup");
			return (Criteria) this;
		}

		public Criteria andCaseGroupLike(String value) {
			addCriterion("case_group like", value, "caseGroup");
			return (Criteria) this;
		}

		public Criteria andCaseGroupNotLike(String value) {
			addCriterion("case_group not like", value, "caseGroup");
			return (Criteria) this;
		}

		public Criteria andCaseGroupIn(List<String> values) {
			addCriterion("case_group in", values, "caseGroup");
			return (Criteria) this;
		}

		public Criteria andCaseGroupNotIn(List<String> values) {
			addCriterion("case_group not in", values, "caseGroup");
			return (Criteria) this;
		}

		public Criteria andCaseGroupBetween(String value1, String value2) {
			addCriterion("case_group between", value1, value2, "caseGroup");
			return (Criteria) this;
		}

		public Criteria andCaseGroupNotBetween(String value1, String value2) {
			addCriterion("case_group not between", value1, value2, "caseGroup");
			return (Criteria) this;
		}

		public Criteria andIsDeletedIsNull() {
			addCriterion("is_deleted is null");
			return (Criteria) this;
		}

		public Criteria andIsDeletedIsNotNull() {
			addCriterion("is_deleted is not null");
			return (Criteria) this;
		}

		public Criteria andIsDeletedEqualTo(String value) {
			addCriterion("is_deleted =", value, "isDeleted");
			return (Criteria) this;
		}

		public Criteria andIsDeletedNotEqualTo(String value) {
			addCriterion("is_deleted <>", value, "isDeleted");
			return (Criteria) this;
		}

		public Criteria andIsDeletedGreaterThan(String value) {
			addCriterion("is_deleted >", value, "isDeleted");
			return (Criteria) this;
		}

		public Criteria andIsDeletedGreaterThanOrEqualTo(String value) {
			addCriterion("is_deleted >=", value, "isDeleted");
			return (Criteria) this;
		}

		public Criteria andIsDeletedLessThan(String value) {
			addCriterion("is_deleted <", value, "isDeleted");
			return (Criteria) this;
		}

		public Criteria andIsDeletedLessThanOrEqualTo(String value) {
			addCriterion("is_deleted <=", value, "isDeleted");
			return (Criteria) this;
		}

		public Criteria andIsDeletedLike(String value) {
			addCriterion("is_deleted like", value, "isDeleted");
			return (Criteria) this;
		}

		public Criteria andIsDeletedNotLike(String value) {
			addCriterion("is_deleted not like", value, "isDeleted");
			return (Criteria) this;
		}

		public Criteria andIsDeletedIn(List<String> values) {
			addCriterion("is_deleted in", values, "isDeleted");
			return (Criteria) this;
		}

		public Criteria andIsDeletedNotIn(List<String> values) {
			addCriterion("is_deleted not in", values, "isDeleted");
			return (Criteria) this;
		}

		public Criteria andIsDeletedBetween(String value1, String value2) {
			addCriterion("is_deleted between", value1, value2, "isDeleted");
			return (Criteria) this;
		}

		public Criteria andIsDeletedNotBetween(String value1, String value2) {
			addCriterion("is_deleted not between", value1, value2, "isDeleted");
			return (Criteria) this;
		}
	}

	/**
	 * This class was generated by MyBatis Generator. This class corresponds to the database table key_value_store
	 * @mbg.generated  Fri Dec 06 15:01:17 CST 2024
	 */
	public static class Criterion {
		private String condition;
		private Object value;
		private Object secondValue;
		private boolean noValue;
		private boolean singleValue;
		private boolean betweenValue;
		private boolean listValue;
		private String typeHandler;

		public String getCondition() {
			return condition;
		}

		public Object getValue() {
			return value;
		}

		public Object getSecondValue() {
			return secondValue;
		}

		public boolean isNoValue() {
			return noValue;
		}

		public boolean isSingleValue() {
			return singleValue;
		}

		public boolean isBetweenValue() {
			return betweenValue;
		}

		public boolean isListValue() {
			return listValue;
		}

		public String getTypeHandler() {
			return typeHandler;
		}

		protected Criterion(String condition) {
			super();
			this.condition = condition;
			this.typeHandler = null;
			this.noValue = true;
		}

		protected Criterion(String condition, Object value, String typeHandler) {
			super();
			this.condition = condition;
			this.value = value;
			this.typeHandler = typeHandler;
			if (value instanceof List<?>) {
				this.listValue = true;
			} else {
				this.singleValue = true;
			}
		}

		protected Criterion(String condition, Object value) {
			this(condition, value, null);
		}

		protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
			super();
			this.condition = condition;
			this.value = value;
			this.secondValue = secondValue;
			this.typeHandler = typeHandler;
			this.betweenValue = true;
		}

		protected Criterion(String condition, Object value, Object secondValue) {
			this(condition, value, secondValue, null);
		}
	}

	/**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table key_value_store
     *
     * @mbg.generated do_not_delete_during_merge Fri Dec 06 14:42:34 CST 2024
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }
}