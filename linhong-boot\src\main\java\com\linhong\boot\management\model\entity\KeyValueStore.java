package com.linhong.boot.management.model.entity;

public class KeyValueStore {

	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column key_value_store.id
	 * @mbg.generated  Fri Dec 06 15:01:17 CST 2024
	 */
	private Integer id;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column key_value_store.case_key
	 * @mbg.generated  Fri Dec 06 15:01:17 CST 2024
	 */
	private String caseKey;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column key_value_store.case_value
	 * @mbg.generated  Fri Dec 06 15:01:17 CST 2024
	 */
	private String caseValue;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column key_value_store.case_group
	 * @mbg.generated  Fri Dec 06 15:01:17 CST 2024
	 */
	private String caseGroup;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column key_value_store.is_deleted
	 * @mbg.generated  Fri Dec 06 15:01:17 CST 2024
	 */
	private String isDeleted;

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column key_value_store.id
	 * @return  the value of key_value_store.id
	 * @mbg.generated  Fri Dec 06 15:01:17 CST 2024
	 */
	public Integer getId() {
		return id;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column key_value_store.id
	 * @param id  the value for key_value_store.id
	 * @mbg.generated  Fri Dec 06 15:01:17 CST 2024
	 */
	public void setId(Integer id) {
		this.id = id;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column key_value_store.case_key
	 * @return  the value of key_value_store.case_key
	 * @mbg.generated  Fri Dec 06 15:01:17 CST 2024
	 */
	public String getCaseKey() {
		return caseKey;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column key_value_store.case_key
	 * @param caseKey  the value for key_value_store.case_key
	 * @mbg.generated  Fri Dec 06 15:01:17 CST 2024
	 */
	public void setCaseKey(String caseKey) {
		this.caseKey = caseKey == null ? null : caseKey.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column key_value_store.case_value
	 * @return  the value of key_value_store.case_value
	 * @mbg.generated  Fri Dec 06 15:01:17 CST 2024
	 */
	public String getCaseValue() {
		return caseValue;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column key_value_store.case_value
	 * @param caseValue  the value for key_value_store.case_value
	 * @mbg.generated  Fri Dec 06 15:01:17 CST 2024
	 */
	public void setCaseValue(String caseValue) {
		this.caseValue = caseValue == null ? null : caseValue.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column key_value_store.case_group
	 * @return  the value of key_value_store.case_group
	 * @mbg.generated  Fri Dec 06 15:01:17 CST 2024
	 */
	public String getCaseGroup() {
		return caseGroup;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column key_value_store.case_group
	 * @param caseGroup  the value for key_value_store.case_group
	 * @mbg.generated  Fri Dec 06 15:01:17 CST 2024
	 */
	public void setCaseGroup(String caseGroup) {
		this.caseGroup = caseGroup == null ? null : caseGroup.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column key_value_store.is_deleted
	 * @return  the value of key_value_store.is_deleted
	 * @mbg.generated  Fri Dec 06 15:01:17 CST 2024
	 */
	public String getIsDeleted() {
		return isDeleted;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column key_value_store.is_deleted
	 * @param isDeleted  the value for key_value_store.is_deleted
	 * @mbg.generated  Fri Dec 06 15:01:17 CST 2024
	 */
	public void setIsDeleted(String isDeleted) {
		this.isDeleted = isDeleted == null ? null : isDeleted.trim();
	}
}