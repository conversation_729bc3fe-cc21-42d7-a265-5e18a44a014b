package com.linhong.boot.management.service;

import java.math.BigDecimal;
import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.linhong.boot.management.model.entity.RepaymentRecords;
import com.linhong.boot.management.model.entity.UserMonthlyRanking;
import com.linhong.boot.management.model.query.RepaymentQuery;

/**
 * 还款记录
 */
public interface RepaymentRecordService extends IService<RepaymentRecords>{

	
	/**
	 * 上月还款排名
	 * @return
	 */
	public List<UserMonthlyRanking> userRank();
	/**
	 * 本月还款排名
	 * @return
	 */
	public List<UserMonthlyRanking> userRankNow();

	/**
	 * 上月团队还款排名
	 * @return
	 */
	public List<UserMonthlyRanking> userRankByTeam();

	/**
	 * 本月团队还款排名
	 * @return
	 */
	public List<UserMonthlyRanking> userRankNowByTeam();

	/**
	 * 还款列表总金额
	 * @return
	 */
	public BigDecimal repaySumAmount(RepaymentQuery queryParams);


	
}
