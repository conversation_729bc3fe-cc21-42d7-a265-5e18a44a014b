package com.linhong.boot.management.service.impl;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.linhong.boot.common.enums.CaseEnum;
import com.linhong.boot.common.enums.TransferRecordEnum;
import com.linhong.boot.common.util.ReflectiveCopierUtils;
import com.linhong.boot.common.util.StrUtils;
import com.linhong.boot.core.security.util.SecurityUtils;
import com.linhong.boot.management.mapper.CcUserMapper;
import com.linhong.boot.management.model.entity.CcUser;
import com.linhong.boot.management.model.entity.CcUserWork;
import com.linhong.boot.management.model.entity.EmergencyContacts;
import com.linhong.boot.management.model.entity.FollowUpRecords;
import com.linhong.boot.management.model.entity.RepaymentRecords;
import com.linhong.boot.management.model.entity.TransferRecords;
import com.linhong.boot.management.model.query.CcUserExcQuery;
import com.linhong.boot.management.model.query.CcUserQuery;
import com.linhong.boot.management.model.query.ChangeCaseQuery;
import com.linhong.boot.management.model.query.RepaymentQuery;
import com.linhong.boot.management.model.vo.CcUserVo;
import com.linhong.boot.management.model.vo.TransferRecordsVO;
import com.linhong.boot.management.service.CasePoolService;
import com.linhong.boot.management.service.CcWorksService;
import com.linhong.boot.management.service.EmergencyContactsService;
import com.linhong.boot.management.service.FollowUpRecordsService;
import com.linhong.boot.management.service.RepaymentRecordService;
import com.linhong.boot.management.service.TransferRecordsService;
import com.linhong.boot.system.model.entity.Dept;
import com.linhong.boot.system.model.entity.User;
import com.linhong.boot.system.model.query.UserPageQuery;
import com.linhong.boot.system.model.vo.UserPageVO;
import com.linhong.boot.system.model.vo.UserProfileVO;
import com.linhong.boot.system.service.DeptService;
import com.linhong.boot.system.service.UserService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 案件管理实现类
 *
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CasePoolServiceImpl extends ServiceImpl<CcUserMapper, CcUser> implements CasePoolService {

	/* 紧急联系人 */
	private final EmergencyContactsService contactsService;
	/* 跟进功能 */
	private final FollowUpRecordsService recordsService;
	/* 用户 */
	private final UserService userService;
	/* 用户和案件关联表 */
	private final CcWorksService worksService;
	/* 分配记录 */
	private final TransferRecordsService transferRecordsService;
	/* 还款管理 */
	private final RepaymentRecordService repaymentRecordService;
	/* 部门管理 */
	private final DeptService deptService;
	

	@Override
	public IPage<CcUserVo> getPoolPage(CcUserQuery queryParams) {
		Page<CcUserVo> page = new Page<>(queryParams.getPageNum(), queryParams.getPageSize());
		Page<CcUserVo> poolpage = this.baseMapper.getPoolPage(page, queryParams);
		if (poolpage.getRecords().size() > 0) {
			CcUserVo user = this.baseMapper.getPoolPageAmount("", queryParams);
			poolpage.getRecords().get(0).setSumentrustPrincipalTotal(user.getSumentrustPrincipalTotal());
			poolpage.getRecords().get(0).setSumEntrustTotalAmount(user.getSumEntrustTotalAmount());
		}
		return poolpage;
	}
	
	@Override
	public IPage<CcUserVo> getWorkPage(CcUserQuery queryParams) {
		Page<CcUserVo> page = new Page<>(queryParams.getPageNum(), queryParams.getPageSize());
		if(queryParams.getUserOrDept()!=null) {
			/*判断是部门 还是 员工*/
			String uod = queryParams.getUserOrDept();
			Dept d = deptService.getBaseMapper().selectById(uod);
			List<Long> ids = new ArrayList<>();
			if(d!=null) {
				/*为部门查询 , 查询该部门下所有员工*/
				UserPageQuery q = new UserPageQuery();
				q.setPageSize(10000);
				q.setDeptId(uod);
				IPage<UserPageVO> u = userService.getUserPage(q);
				ids = u.getRecords().stream().map(UserPageVO::getId).collect(Collectors.toList());
			}else {
				/*指定的员工查询*/
				ids.add(Long.valueOf(uod));
				queryParams.setUIds(ids);
			}
			queryParams.setUIds(ids);
		}
		Page<CcUserVo> poolpage = this.baseMapper.getWorkPage(page, queryParams);
		if (poolpage.getRecords().size() > 0) {
			List<String> userIds =  poolpage.getRecords().stream().map(CcUserVo::getUserOrDept).collect(Collectors.toList());
			List<User> users = userService.getBaseMapper().selectBatchIds(userIds);
			List<String> deptIds = users.stream().map(User::getDeptId).collect(Collectors.toList());
			List<Dept> depts =  deptService.getBaseMapper().selectBatchIds(deptIds);
			Map<Long,String> mapUser =users.stream().collect(Collectors.toMap(User::getId, User::getNickname));
			Map<Long,String> mapUserDept =users.stream().collect(Collectors.toMap(User::getId, User::getDeptId));
			Map<String,String> mapDept = depts.stream().collect(Collectors.toMap(Dept::getId, Dept::getName));
			for (CcUserVo r : poolpage.getRecords()) {
				Long userOrDeptId = Long.valueOf(r.getUserOrDept());
			    String deptName = mapUserDept.containsKey(userOrDeptId)? mapDept.get(mapUserDept.get(userOrDeptId)): "未知部门";
			    r.setDeptName(deptName);
			    if (mapUser.containsKey(userOrDeptId)) {
			        r.setUserOrDept(mapUser.get(userOrDeptId));
			    }
			}
			CcUserVo user = this.baseMapper.getPoolPageAmount("", queryParams);
			poolpage.getRecords().get(0).setSumentrustPrincipalTotal(user.getSumentrustPrincipalTotal());
			poolpage.getRecords().get(0).setSumEntrustTotalAmount(user.getSumEntrustTotalAmount());
		}
		return poolpage;
	}
	
	@Override
	public IPage<TransferRecordsVO> getHuanPage(CcUserQuery queryParams) {
		Page<CcUserVo> page = new Page<>(queryParams.getPageNum(), queryParams.getPageSize());
		if(queryParams.getUserOrDept()!=null) {
			/*判断是部门 还是 员工*/
			String uod = queryParams.getUserOrDept();
			Dept d = deptService.getBaseMapper().selectById(uod);
			List<Long> ids = new ArrayList<>();
			if(d!=null) {
				/*为部门查询 , 查询该部门下所有员工*/
				UserPageQuery q = new UserPageQuery();
				q.setPageSize(10000);
				q.setDeptId(uod);
				IPage<UserPageVO> u = userService.getUserPage(q);
				ids = u.getRecords().stream().map(UserPageVO::getId).collect(Collectors.toList());
			}else {
				/*指定的员工查询*/
				ids.add(Long.valueOf(uod));
				queryParams.setUIds(ids);
			}
			queryParams.setUIds(ids);
		}
		
		Page<TransferRecordsVO> poolpage = transferRecordsService.selectTransPage(queryParams);
		
		if (poolpage.getRecords().size() > 0) {
			List<TransferRecordsVO> list = poolpage.getRecords();
			List<String> userIds = list.stream().map(TransferRecordsVO::getOperator).collect(Collectors.toList());
			List<User> users = userService.getBaseMapper().selectBatchIds(userIds);
			List<String> deptIds = users.stream().map(User::getDeptId).collect(Collectors.toList());
			List<Dept> depts = deptService.getBaseMapper().selectBatchIds(deptIds);
			
			Map<String, Dept> deptMap = depts.stream().collect(Collectors.toMap(Dept::getId, dept -> dept));
	        Map<Long, Map<String, String>> resultMap = users.stream()
	                .collect(Collectors.toMap(
	                        User::getId, 
	                        user -> {
	                            Dept dept = deptMap.get(user.getDeptId());
	                            Map<String, String> infoMap = new HashMap<>();
	                            infoMap.put("nickName", user.getNickname());
	                            if (dept != null) {
	                                infoMap.put("deptName", dept.getName());
	                            } else {
	                                infoMap.put("deptName", "未知部门");
	                            }
	                            return infoMap;
	                        }
	                ));
			for (TransferRecordsVO t : list) {
				String userOrDept = "";
				t.setAssignmentType(TransferRecordEnum.fromValue(Integer.valueOf(t.getAssignmentType())));
				t.setHuanType(t.getAssType().equals("1")?"换进":"换出");
				t.setHuanTime(t.getOperationTime());
				if(!StrUtils.isChineseAndNotLong(t.getOperator())) {
					if(resultMap.containsKey(Long.valueOf(t.getOperator()))) {
						Map<String,String> userAndDept = resultMap.get(Long.valueOf(t.getOperator()));
						String nickName = userAndDept.get("nickName");
						String deptName = userAndDept.get("deptName");
						userOrDept = nickName+"/"+deptName;
					}
				}
				t.setUserOrDept(userOrDept);
			}
		}
		return poolpage;
	}

	@Override
	public CcUserVo getPoolDetail(Long id) {
		/* 案件 */
		CcUser ccUser = this.baseMapper.selectByPrimaryKey(id);
		CcUserVo vo = new CcUserVo();
		if (ccUser != null) {
			Long userId = ccUser.getId();
			try {
				ReflectiveCopierUtils.copy(ccUser, vo);
				vo.setCaseType(ccUser.getCaseType());
				vo.setComplaintLabel(ccUser.getComplaintLabel());
			} catch (IllegalAccessException e) {
				e.printStackTrace();
			}
			/* 查询紧急联系人 */
			QueryWrapper<EmergencyContacts> contactsWrapper = new QueryWrapper<>();
			contactsWrapper.eq("user_id", userId); // user_id = cc_id
			List<EmergencyContacts> emList = contactsService.getBaseMapper().selectList(contactsWrapper);
			vo.setContacts(emList);
			/* 查询跟进记录 */
			QueryWrapper<FollowUpRecords> foolQueryWrapper = new QueryWrapper<>();
			foolQueryWrapper.eq("user_id", userId); // user_id = cc_id 
			List<FollowUpRecords> followUpRecords = recordsService.getBaseMapper().selectList(foolQueryWrapper);
			followUpRecords.forEach(r->{
				r.setCollectionResult(CaseEnum.fromValue(Integer.valueOf(r.getCollectionResult())));
			});
			vo.setRecords(followUpRecords);
		}
		return vo;
	}

	@Override
	public List<CcUser> getPoolExt(CcUserExcQuery queryParams) {
		if(queryParams.getUserOrDept()!=null) {
			/*判断是部门 还是 员工*/
			String uod = queryParams.getUserOrDept();
			Dept d = deptService.getBaseMapper().selectById(uod);
			List<Long> ids = new ArrayList<>();
			if(d!=null) {
				/*为部门查询 , 查询该部门下所有员工*/
				UserPageQuery q = new UserPageQuery();
				q.setPageSize(10000);
				q.setDeptId(uod);
				IPage<UserPageVO> u = userService.getUserPage(q);
				ids = u.getRecords().stream().map(UserPageVO::getId).collect(Collectors.toList());
			}else {
				/*指定的员工查询*/
				ids.add(Long.valueOf(uod));
				queryParams.setUIds(ids);
			}
			queryParams.setUIds(ids);
		}
		List<CcUser> cuList = this.baseMapper.getPoolExt("", queryParams);
		return cuList;
	}
	
	@Override
	public List<CcUser> getWorkPoolExt(CcUserExcQuery queryParams) {
		if(queryParams.getUserOrDept()!=null) {
			/*判断是部门 还是 员工*/
			String uod = queryParams.getUserOrDept();
			Dept d = deptService.getBaseMapper().selectById(uod);
			List<Long> ids = new ArrayList<>();
			if(d!=null) {
				/*为部门查询 , 查询该部门下所有员工*/
				UserPageQuery q = new UserPageQuery();
				q.setPageSize(10000);
				q.setDeptId(uod);
				IPage<UserPageVO> u = userService.getUserPage(q);
				ids = u.getRecords().stream().map(UserPageVO::getId).collect(Collectors.toList());
			}else {
				/*指定的员工查询*/
				ids.add(Long.valueOf(uod));
				queryParams.setUIds(ids);
			}
			
			queryParams.setUIds(ids);
		}
		List<CcUser> cuList = this.baseMapper.getWorkPoolExt("", queryParams);
		return cuList;
	}

	@Override
	@Transactional
	public boolean allocate(CcUserQuery query) {
		/* 获取当前登录人信息 */
		Long userId = SecurityUtils.getUserId();
		/* 权限级别 1:部门及其子部门 2:本部门  3:本人数据  0:所有数据 */
		Integer scope = SecurityUtils.getDataScope();
		String scope_level = TransferRecordEnum.ZHU_GUAN_FEN_PAI.getValue().toString();
		if(scope==3)
			scope_level = TransferRecordEnum.HUAN_DAN.getValue().toString();
		User user = userService.getById(userId);
		/* 员工id.list */
		List<Long> uList = query.getUIds();
		/* 案件id.list */
		List<Long> cIds = new ArrayList<>();
		/* 已勾选, 旧案件及其催收人 */
		Map<Long,Long> map_OldWork = new HashMap<>();
		/* 存放用户部门信息 */
		Map<Long,String> usersDept = new HashMap<>();
		/* 状态为0的 换单后 作废临时作业清单 */
		List<Long> changeId = new ArrayList<>();
		/* 新案件及其催收人 | 新案件 或是 换单案件(通过ccUserWork表的 wrokStatus区别. 为0的是换单后案件. 同一案件只能存在一个 */
//		List<Long> missWorkList = new ArrayList<>();
		if(query.getCIdNum() > 0) {
			/*按案件数量分配*/
			/*获取指定数量的案件*/
			CcUserQuery qCidParam = new CcUserQuery();
			List<String> followStatusList = new ArrayList<>();
			followStatusList.add(CaseEnum.CASE_POOL.getValue().toString());
			followStatusList.add(CaseEnum.NEW_CASE.getValue().toString());
			followStatusList.add(CaseEnum.TUI_HUI.getValue().toString());
			qCidParam.setFollowUpStatusList(followStatusList);
			Page<CcUser> pool = this.baseMapper.getPoolPage2(new Page<>(query.getPageNum(), query.getPageSize()), qCidParam);
			cIds = pool.getRecords().stream().map(CcUser::getId).collect(Collectors.toList());
//			missWorkList.addAll(cIds);
		}else {
			/*按指定案件分配*/
			cIds = query.getIds();
			QueryWrapper<CcUserWork> work = new QueryWrapper<>();
			// 新增查询作业清单状态. 换单后, work_status置为0 , 一个cc_id 对应 的一个user_id , 这称为一组. 同一组数据不能重复出现 , 重新分配改案件后, 为0的这组数据做物理删除
			work.in("cc_id", cIds);
			/*查询ccUserWork表*/
			List<CcUserWork> workList = worksService.getBaseMapper().selectList(work);
			/* 区分勾选的案件是否已有催收人 , missWorkList=没有催收人负责的案件 */
			if(workList != null && workList.size() > 0 ) {
				/*查询用户获取部门id*/
				Set<Long> work_user_idList = workList.stream().map(CcUserWork::getUserId).collect(Collectors.toSet());
				usersDept = userService.getBaseMapper().selectBatchIds(work_user_idList).stream().collect(Collectors.toMap(User::getId, User::getDeptId));
				/*changeCase为换单案件*/
//				Set<Long> workList_NoContains = workList.stream().map(CcUserWork::getCcId).collect(Collectors.toSet());
//				List<Long> missIds = cIds.stream().filter(cId -> !workList_NoContains.contains(cId)).collect(Collectors.toList());
				/*换单情况*/
				changeId = workList.stream().filter(w->w.getWorkStatus()!=null && w.getWorkStatus().equals("0"))
						.map(CcUserWork::getCcId)
						.collect(Collectors.toList());
//				missWorkList.addAll(missIds);
			}
			/* 分配记录功能 , 获取案件旧催收人, 案件和催收人为多对一关系 K:案件id,V:催收人ID */
			map_OldWork = workList.stream().collect(Collectors.toMap(CcUserWork::getCcId, CcUserWork::getUserId));
			
		}
		
		/* 递归 递归       K:员工id  V:案件ID               递归 递归 */
		/* 分配案件 员工:案件 一对多 */
		Map<Long, List<Long>> re = distributeProducts(uList, cIds);
		/* 作业清单 */
		List<CcUserWork> userWorkList = new ArrayList<>();
		/* 案件 */
		List<CcUser> ccUser_newCase = new ArrayList<>();
		/* 员工名字, 用以记录案件催收人名字 */
		Map<Long, User> u_newUser = userService.getBaseMapper().selectBatchIds(uList).stream().collect(Collectors.toMap(User::getId, Function.identity()));
		/* 分配记录功能 */
		List<TransferRecords> TransferRecordsList = new ArrayList<>();
		for (Long u : re.keySet()) {
			List<Long> ccList = re.get(u); //获取某员工被分配的案件
			for (Long cId : ccList) {
				/* 新增案件催收人 */
				CcUserWork cw = new CcUserWork();
				cw.setCcId(cId);
				cw.setUserId(u);
				userWorkList.add(cw);
				/* 更新案件状态 */
				CcUser newCase = new CcUser();
				newCase.setId(cId);
				newCase.setFollowUpStatus(CaseEnum.NEW_CASE.getValue().toString());
				//案件催收人所属部门
				String u_newUser_deptId = u_newUser.get(u).getDeptId();
				newCase.setUserOrDept(u.toString());
				ccUser_newCase.add(newCase);
				/* 案件分配记录 */
				if(map_OldWork.containsKey(cId)) {
					log.info(user.getNickname()+"调配案件["+cId+"]给"+u);
					/*老案件催收人*/
					/* 调配 */
					Long old_user_id = map_OldWork.get(cId);
					String old_user_dept_id = null;
					if(usersDept.containsKey(old_user_id)) {
						old_user_dept_id = usersDept.get(old_user_id);
					}
					/*保存分配记录*/
					TransferRecords transferRecords = transferRecordsService.saveTransFerRecord(cId,old_user_id.toString(),old_user_dept_id.toString(),u.toString(),u_newUser_deptId.toString(),user.getId().toString(),scope_level);
					TransferRecordsList.add(transferRecords);
				}else{
					log.info(user.getNickname()+"分配案件["+cId+"]给"+u);
					/*不存在旧催收人 , 为手动分配案池案件*/
					/*保存分配记录*/
					TransferRecords transferRecords = transferRecordsService.saveTransFerRecord(cId,"案池","案池",u.toString(),u_newUser_deptId.toString(),user.getId().toString(),TransferRecordEnum.AN_CHI_FEN_PAI.getValue().toString());
					TransferRecordsList.add(transferRecords);
				}

				
			}
		}
		if(changeId.size()>0) {
			LambdaQueryWrapper<CcUserWork> wq = new LambdaQueryWrapper<>();
			wq.in(CcUserWork::getCcId, changeId);
			wq.eq(CcUserWork::getWorkStatus, "0");
			worksService.getBaseMapper().delete(wq);
		}
		/* 批量新增工单 */
		worksService.saveBatch(userWorkList);
		/* 批量更新更新案件 */
		this.updateBatchById(ccUser_newCase);
		/* 批量新增分配记录 */
		transferRecordsService.saveBatch(TransferRecordsList);
		return true;

	}
	
	@Override
	@Transactional
	public boolean workAllocate(CcUserQuery query) {
		/* 获取当前登录人信息 */
		Long userId = SecurityUtils.getUserId();
		/* 权限级别 1:部门及其子部门 2:本部门  3:本人数据  0:所有数据 */
		Integer scope = SecurityUtils.getDataScope();
		String scope_level = TransferRecordEnum.ZHU_GUAN_FEN_PAI.getValue().toString();
		if(scope==3)
			scope_level = TransferRecordEnum.HUAN_DAN.getValue().toString();
//		User user = userService.getById(userId);
		/* 被分配员工id.list */
		List<Long> uList = query.getUIds();
		/* 案件id.list */
		List<Long> cIds = new ArrayList<>();
		/* 已勾选, 旧案件及其催收人 */
		Map<Long,Long> map_OldWork = new HashMap<>();
		/* 存放用户部门信息 */
		Map<Long,String> usersDept = new HashMap<>();
		/* 状态为0的 换单后 临时作废作业清单 */
		List<Long> changeId = new ArrayList<>();
		/* 新案件及其催收人 | 新案件 或是 换单案件(通过ccUserWork表的 wrokStatus区别. 为0的是换单后案件. 同一案件只能存在一个 */
//		List<Long> missWorkList = new ArrayList<>();
		/*按指定案件分配*/
		cIds = query.getIds();
		QueryWrapper<CcUserWork> work = new QueryWrapper<>();
		// 新增查询作业清单状态. 换单后, work_status置为0 , 一个cc_id 对应 的一个user_id , 这称为一组. 同一组数据不能重复出现 , 重新分配改案件后, 为0的这组数据做物理删除
		work.in("cc_id", cIds);
		/*查询ccUserWork表*/
		List<CcUserWork> workList = worksService.getBaseMapper().selectList(work);
		/* 区分勾选的案件是否已有催收人 */
		if(workList != null && workList.size() > 0 ) {
			/*查询用户获取部门id*/
			Set<Long> work_user_idList = workList.stream().map(CcUserWork::getUserId).collect(Collectors.toSet());
			usersDept = userService.getBaseMapper().selectBatchIds(work_user_idList).stream().collect(Collectors.toMap(User::getId, User::getDeptId));
			/*换单情况*/
			changeId = cIds;
		}
		/* 分配记录功能 , 获取案件旧催收人 K:案件id,V:催收人ID */
		map_OldWork = workList.stream().collect(Collectors.toMap(CcUserWork::getCcId, CcUserWork::getUserId));
		/* 递归 递归       K:员工id  V:案件ID               递归 递归 */
		/* 分配案件 员工:案件 一对多 */
		Map<Long, List<Long>> re = distributeProducts(uList, cIds);
		/* 作业清单 */
		List<CcUserWork> userWorkList = new ArrayList<>();
		/* 案件 */
		List<CcUser> ccUser_newCase = new ArrayList<>();
		/* 员工名字, 用以记录案件催收人名字 */
		Map<Long, User> u_newUser = userService.getBaseMapper().selectBatchIds(uList).stream().collect(Collectors.toMap(User::getId, Function.identity()));
		/* 分配记录功能 */
		List<TransferRecords> TransferRecordsList = new ArrayList<>();
		for (Long u : re.keySet()) {
			List<Long> ccList = re.get(u); //获取某员工被分配的案件
			for (Long cId : ccList) {
				/* 新增案件催收人 */
				CcUserWork cw = new CcUserWork();
				cw.setCcId(cId);
				cw.setUserId(u);
				userWorkList.add(cw);
				/* 更新案件状态 */
				CcUser newCase = new CcUser();
				newCase.setId(cId);
//				newCase.setFollowUpStatus(CaseEnum.NEW_CASE.getValue().toString());
				/* 更新案件负责人 */
//				String nickName = u_newUser.get(u).getNickname();
				String u_newUser_deptId = u_newUser.get(u).getDeptId();
				newCase.setUserOrDept(u.toString());
				ccUser_newCase.add(newCase);
				/* 案件分配记录 */
				if(map_OldWork.containsKey(cId)) {
					log.info(userId+"调配案件["+cId+"]给"+u);
					/*老案件催收人*/
					/* 调配 */
					Long old_user_id = map_OldWork.get(cId);
					String old_user_dept_id = null;
					if(usersDept.containsKey(old_user_id)) {
						old_user_dept_id = usersDept.get(old_user_id);
					}
					/*保存分配记录*/
					TransferRecords transferRecords = transferRecordsService.saveTransFerRecord(cId,old_user_id.toString(),old_user_dept_id.toString(),u.toString(),u_newUser_deptId.toString(),userId.toString(),scope_level);
					TransferRecordsList.add(transferRecords);
				}else{
					log.info(userId+"分配案件["+cId+"]给"+u);
					/*不存在旧催收人 , 为手动分配案池案件*/
					/*保存分配记录*/
					TransferRecords transferRecords = transferRecordsService.saveTransFerRecord(cId,"案池","案池",u.toString(),u_newUser_deptId.toString(),userId.toString(),TransferRecordEnum.AN_CHI_FEN_PAI.getValue().toString());
					TransferRecordsList.add(transferRecords);
				}
				
			}
		}
		/* 删除老旧工单 */
		if(changeId.size()>0) {
			LambdaQueryWrapper<CcUserWork> wq = new LambdaQueryWrapper<>();
			wq.in(CcUserWork::getCcId, changeId);
			worksService.getBaseMapper().delete(wq);
		}
		/* 批量新增工单 */
		worksService.saveBatch(userWorkList);
		/* 批量更新更新案件 */
		this.updateBatchById(ccUser_newCase);
		/* 批量新增分配记录 */
		transferRecordsService.saveBatch(TransferRecordsList);
		return true;

	}
	
	/**
	 * 按案件数量分配
	 * @param cIds 案件list
	 * @param uIds 员工list
	 */
	public void fromCaseNumberDis(List<Long> cIds , List<Long> uIds) {
		/* 避免脏数据,后续性能优化可优化这个 */
		List<Long> userList = userService.getBaseMapper().selectBatchIds(uIds).stream().filter(u->u.getIsDeleted().equals(0)).map(User::getId).collect(Collectors.toList());
		Map<Long, List<Long>> re = distributeProducts(userList, cIds);
		for (Long u : re.keySet()) {
			List<Long> ccList = re.get(u);
			ccList.stream().forEach(cId -> {
				CcUserWork cw = new CcUserWork();
				cw.setCcId(cId);
				cw.setUserId(u);
				worksService.save(cw);
				/* 更新案件状态 */
				this.updateStatusForCcUser(cId, CaseEnum.NEW_CASE.getValue().toString());
				/* 更新案件负责人 */
				CcUser cu = new CcUser();
				cu.setUserOrDept(u.toString());
				this.updateUserOrDept(cu, cId);
			});
		}
	}
	
	
	/**
	 * 分配案件
	 * 
	 * @param users
	 * @param products
	 * @return
	 */
	public Map<Long, List<Long>> distributeProducts(List<Long> uIds, List<Long> cIds) {
		if (uIds == null || uIds.isEmpty() || cIds == null || cIds.isEmpty()) {
			throw new IllegalArgumentException("自动分配失败,用户数据 或 产品数据 异常.");
		}

		// 打乱产品列表 , 客户要求随机分配
		Collections.shuffle(cIds);

		int numUsers = uIds.size();
		int numProducts = cIds.size();

		// 每个用户至少分到的产品数量，以及剩余的产品数量
		int baseProductsPerUser = numProducts / numUsers;
		int remainingProducts = numProducts % numUsers;

		Map<Long, List<Long>> distribution = new HashMap<>();
		int currentIndex = 0;

		for (int i = 0; i < numUsers; i++) {
			// 每个用户分到的案件数量
			int productsForUser = baseProductsPerUser + (i < remainingProducts ? 1 : 0);

			// 为用户分配案件
			List<Long> userProducts = new ArrayList<>(cIds.subList(currentIndex, currentIndex + productsForUser));
			distribution.put(uIds.get(i), userProducts);

			// 更新索引
			currentIndex += productsForUser;
		}

		return distribution;
	}

	
	
	@Override
	public boolean updateStatusForCcUser(Long id, String status) {
		CcUser setUser = new CcUser();
		setUser.setFollowUpStatus(status);
		QueryWrapper<CcUser> wu = new QueryWrapper<>();
		wu.eq("id", id);
		this.getBaseMapper().update(setUser, wu);
		return true;
	}
	@Override
	public boolean 	updateUserOrDeptById(Long id, String userOrDept) {
		CcUser setUser = new CcUser();
		setUser.setUserOrDept(userOrDept);
		QueryWrapper<CcUser> wu = new QueryWrapper<>();
		wu.eq("id", id);
		this.getBaseMapper().update(setUser, wu);
		return true;
	}
	@Override
	public boolean updateUserOrDept(CcUser u, Long ccId) {
		QueryWrapper<CcUser> query = new QueryWrapper<>();
		query.eq("id", ccId);
		return this.update(u, query);
	}

	/**
	 * 获取用户换出的历史案件ID列表
	 * @param userId 用户ID
	 * @return 用户曾经换出的案件ID列表
	 */
	private List<Long> getUserExchangeOutHistory(Long userId) {
		LambdaQueryWrapper<TransferRecords> query = new LambdaQueryWrapper<>();
		query.eq(TransferRecords::getSourceUser, userId.toString())
			 .eq(TransferRecords::getAssignmentType, TransferRecordEnum.HUAN_DAN.getValue().toString())
			 .select(TransferRecords::getId);

		List<TransferRecords> records = transferRecordsService.list(query);
		return records.stream()
					  .map(TransferRecords::getId)
					  .distinct()
					  .collect(Collectors.toList());
	}

	/**
	 * 智能获取换单案件（优先非历史案件）
	 * 换单数据来源：follow_up_status IN ('0', '1', '12') AND (user_or_dept IS NULL OR user_or_dept = '') AND (is_lock = '0' OR is_lock IS NULL)
	 * @param needCount 需要的案件数量
	 * @param currentUserId 当前用户ID
	 * @return 案件列表
	 */
	private List<CcUser> getSmartExchangeCases(int needCount, Long currentUserId) {
		List<CcUser> result = new ArrayList<>();

		// 1. 获取用户换出历史案件ID列表
		List<Long> userExchangeHistory = getUserExchangeOutHistory(currentUserId);

		// 2. 构建换单数据的基础查询条件
		// follow_up_status IN ('0', '1', '12') AND (user_or_dept IS NULL OR user_or_dept = '') AND (is_lock = '0' OR is_lock IS NULL)
		LambdaQueryWrapper<CcUser> baseQuery = new LambdaQueryWrapper<>();
		baseQuery.in(CcUser::getFollowUpStatus, Arrays.asList("0", "1", "12"))
				 .and(wrapper -> wrapper.isNull(CcUser::getUserOrDept).or().eq(CcUser::getUserOrDept, ""))
				 .and(wrapper -> wrapper.eq(CcUser::getIsLock, "0").or().isNull(CcUser::getIsLock));

		// 3. 优先获取非历史案件
		LambdaQueryWrapper<CcUser> nonHistoryQuery = new LambdaQueryWrapper<>();
		nonHistoryQuery.in(CcUser::getFollowUpStatus, Arrays.asList("0", "1", "12"))
					   .and(wrapper -> wrapper.isNull(CcUser::getUserOrDept).or().eq(CcUser::getUserOrDept, ""))
					   .and(wrapper -> wrapper.eq(CcUser::getIsLock, "0").or().isNull(CcUser::getIsLock));

		// 如果有历史记录，排除历史案件
		if (!userExchangeHistory.isEmpty()) {
			nonHistoryQuery.notIn(CcUser::getId, userExchangeHistory);
		}
		// 按批次号倒序排序
		nonHistoryQuery.orderByDesc(CcUser::getBatchNumber);
		nonHistoryQuery.last("limit " + needCount);
		List<CcUser> nonHistoryCases = this.baseMapper.selectList(nonHistoryQuery);
		result.addAll(nonHistoryCases);

		// 4. 如果数量不足，从历史案件中补充
		int remainingCount = needCount - result.size();
		if (remainingCount > 0 && !userExchangeHistory.isEmpty()) {
			LambdaQueryWrapper<CcUser> historyQuery = new LambdaQueryWrapper<>();
			historyQuery.in(CcUser::getFollowUpStatus, Arrays.asList("0", "1", "12"))
						.and(wrapper -> wrapper.isNull(CcUser::getUserOrDept).or().eq(CcUser::getUserOrDept, ""))
						.and(wrapper -> wrapper.eq(CcUser::getIsLock, "0").or().isNull(CcUser::getIsLock))
						.in(CcUser::getId, userExchangeHistory)
						.orderByDesc(CcUser::getBatchNumber)
						.last("limit " + remainingCount);

			List<CcUser> historyCases = this.baseMapper.selectList(historyQuery);
			result.addAll(historyCases);
		}

		return result;
	}

	@Override
	@Transactional
	public Integer changeCase(ChangeCaseQuery query) {
		/*操作人*/
		String opation = SecurityUtils.getUsername();
		Long userId = SecurityUtils.getUserId();
		query.setUserId(userId.toString());
		/* 分派级别 */
		String scope_level = TransferRecordEnum.HUAN_DAN.getValue().toString();
		/* 换进案件 分配记录*/
		List<TransferRecords> InTransferRecordsList = new ArrayList<>();
		/* 换出案件 分配记录 */
		List<TransferRecords> OutTransferRecordsList = new ArrayList<>();
		/*查询操作人昵称*/
		User sysUser = userService.getById(query.getUserId());
		/* ===== 使用智能换单算法获取案件（优先非历史案件） ===== */
		List<CcUser> newCaseList = getSmartExchangeCases(query.getCcIds().size(), userId);
		/* 同等数量新案件 */
		if (newCaseList.size() == 0)
			return CaseEnum.CHANGE_FAILED.getValue();
		List<Long> changeId = newCaseList.stream().map(CcUser::getId).collect(Collectors.toList());
		/* 给换出去的案件重置跟进状态为新案 */
		List<CcUser> cList = query.getCcIds().stream().map(id -> {
			CcUser u = new CcUser();
			u.setId(id);
			u.setFollowUpStatus(CaseEnum.NEW_CASE.getValue().toString());
			u.setUserOrDept("");
			u.setIsMarkChange(CaseEnum.CHANGE_N.getValue().toString());// 是否换单,CHANGE_N:否 CHANGE_Y:是
			/* 保存换出去案件的 分配记录 */
			TransferRecords transferRecords = transferRecordsService.saveTransFerRecord(id,sysUser.getId().toString(),sysUser.getDeptId(),"案池","案池",userId.toString(),scope_level);
			OutTransferRecordsList.add(transferRecords);
			return u;
		}).collect(Collectors.toList());
		this.updateBatchById(cList); //更改老的案件状态
		/* 换回同等数量案件 */
		List<CcUserWork> nWorkList = newCaseList.stream().map(cu -> {
			CcUserWork w = new CcUserWork();
			w.setUserId(Long.valueOf(query.getUserId()));
			w.setCcId(cu.getId());
			return w;
		}).collect(Collectors.toList());
		worksService.saveBatch(nWorkList); //新增作业清单
		/*修改新案件的状态*/
		List<CcUser> newCase = newCaseList.stream().map(n->{
			CcUser ca = new CcUser();
			ca.setId(n.getId());
			ca.setFollowUpStatus(CaseEnum.NEW_CASE.getValue().toString());
			ca.setUserOrDept(userId.toString());
			/*保存 换进来的案件 分配记录*/
			// 查询该案件的上一任催收员
			LambdaQueryWrapper<TransferRecords> trq = new LambdaQueryWrapper<>();
			trq.eq(TransferRecords::getId, n.getId());
			trq.orderByDesc(TransferRecords::getOperationTime);
			trq.last(" limit 1 ");
			TransferRecords r = transferRecordsService.getBaseMapper().selectOne(trq);
			String old_uid = "案池";
			String old_dept = "案池";
			if(r!=null) {
				old_uid = r.getTargetUser();
				old_dept = r.getTargetOrganization();
			}
			TransferRecords transferRecords = transferRecordsService.saveTransFerRecord(n.getId(),old_uid,old_dept.toString(),query.getUserId().toString(),sysUser.getDeptId(),userId.toString(),scope_level);
			InTransferRecordsList.add(transferRecords);
			return ca;
		}).collect(Collectors.toList());
		this.updateBatchById(newCase); //更改新的案件状态
		/* 逻辑删除该员工作业清单中已换的案件 */
		CcUserWork w = new CcUserWork();
		w.setWorkStatus("0");
		UpdateWrapper<CcUserWork> uw = new UpdateWrapper<>();
		uw.in("cc_id", query.getCcIds());
		uw.eq("user_id", query.getUserId());
		worksService.update(w,uw);
		/* 删除老旧工单 */
		if(changeId.size()>0) {
			LambdaQueryWrapper<CcUserWork> wq = new LambdaQueryWrapper<>();
			wq.in(CcUserWork::getCcId, changeId);
			wq.eq(CcUserWork::getWorkStatus, "0");
			worksService.remove(wq);
		}
		/* 批量新增分配记录 */
		InTransferRecordsList.addAll(OutTransferRecordsList);
		transferRecordsService.saveBatch(InTransferRecordsList);
		
		return CaseEnum.CHANGE_SUCCESS.getValue();
	}

	@Override
	public List<TransferRecords> getAallocatPage(CcUserQuery queryParams) {
		QueryWrapper<TransferRecords> query = new QueryWrapper<>();
		query.eq("id", queryParams.getId());
		query.orderByDesc("operation_time");
		query.last("limit 10");
		List<TransferRecords> list = transferRecordsService.getBaseMapper().selectList(query);
		List<TransferRecords> res = list.stream().map(r->{
			User operator = userService.getBaseMapper().selectById(r.getOperator());
			String operatorName = r.getOperator();
			if(operator!=null) {
				operatorName = operator.getNickname()==null?"未知员工:"+r.getOperator():operator.getNickname();
			}
			TransferRecords re = new TransferRecords();
			String tarNickName = null;
			String tarDeptName = null;
			if(r.getTargetUser().equals("案池")) {
				tarNickName = "案池";
				tarDeptName = "案池";
			}else {
				UserProfileVO tUser = userService.getUserProfile(Long.valueOf(r.getTargetUser()));
				tarNickName = tUser.getNickname();
				tarDeptName = tUser.getDeptName();
			}
			
			if(!r.getSourceUser().equals("案池")) {
				UserProfileVO sUser = userService.getUserProfile(Long.valueOf(r.getSourceUser()));
				re.setId(r.getId());
				re.setSourceUser(sUser.getNickname());
				re.setSourceOrganization(sUser.getDeptName());
				re.setTargetUser(tarNickName);
				re.setTargetOrganization(tarDeptName);
				re.setAssignmentType(TransferRecordEnum.fromValue(Integer.valueOf(r.getAssignmentType())));
				re.setOperator(operatorName);
				re.setOperationTime(r.getOperationTime());
			}else {
				re.setId(r.getId());
				re.setSourceUser(r.getSourceUser());
				re.setSourceOrganization(r.getSourceOrganization());
				re.setTargetUser(tarNickName);
				re.setTargetOrganization(tarDeptName);
				re.setAssignmentType(TransferRecordEnum.fromValue(Integer.valueOf(r.getAssignmentType())));
				re.setOperator(operatorName);
				re.setOperationTime(r.getOperationTime());
			}
			return re;
		}).collect(Collectors.toList());
		return res;
	}

	@Override
	public IPage<RepaymentRecords> getRepaymentPage(RepaymentQuery queryParams) {
		String uod = null;
		if(queryParams.getOperatorId()!=null) {
			/*判断是部门 还是 员工*/
			uod = queryParams.getOperatorId();
		}else {
			uod = queryParams.getUserOrDept();
		}
		Dept d = deptService.getBaseMapper().selectById(uod);
		List<Long> ids = new ArrayList<>();
		if(d!=null) {
			/*为部门查询 , 查询该部门下所有员工*/
			UserPageQuery q = new UserPageQuery();
			q.setPageSize(10000);
			q.setDeptId(uod);
			IPage<UserPageVO> u = userService.getUserPage(q);
			ids = u.getRecords().stream().map(UserPageVO::getId).collect(Collectors.toList());
		}else {
			/*指定的员工查询*/
			ids.add(Long.valueOf(uod));
			queryParams.setUIds(ids);
		}
		queryParams.setUIds(ids);
		
		Page<RepaymentRecords> page = new Page<>(queryParams.getPageNum(), queryParams.getPageSize());
		LambdaQueryWrapper<RepaymentRecords> query = new LambdaQueryWrapper<>();
		query.eq(queryParams.getCustomerIndexNumber()!=null,RepaymentRecords::getCustomerIndexNumber, queryParams.getCustomerIndexNumber())
		.eq(queryParams.getOutsourceSerialNumber()!=null,RepaymentRecords::getOutsourceSerialNumber, queryParams.getOutsourceSerialNumber())
		.in(queryParams.getBatchNumber()!=null && !queryParams.getBatchNumber().isEmpty(),RepaymentRecords::getBatchNumber, queryParams.getBatchNumber())
		.like(queryParams.getCustomerName()!=null,RepaymentRecords::getCustomerName, queryParams.getCustomerName())
		.eq(queryParams.getCardholderCode()!=null,RepaymentRecords::getCardholderCode, queryParams.getCardholderCode())
		.in(RepaymentRecords::getOperatorId, queryParams.getUIds())
		.eq(RepaymentRecords::getIsDelete, "0");
		if(queryParams.getEntrustStartDate()!=null) {
			query.between(RepaymentRecords::getRepaymentDate, queryParams.getEntrustStartDate()[0], queryParams.getEntrustStartDate()[1]);
		}
		// 少一个 案件类型,需求未明确
		return repaymentRecordService.getBaseMapper().selectPage(page, query);
	}

	@Override
	public int updateBatchByIdNumber(List<CcUser> ccUsers) {
		return this.baseMapper.updateBatchByIdNumber(ccUsers);
	}

	@Override
	public List<CcUser> getCaseList(CcUserQuery queryParams) {
		
		return this.baseMapper.getCaseList(queryParams);
	}
	
	
	
	
	
	

}
