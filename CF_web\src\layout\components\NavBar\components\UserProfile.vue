<template>
  <el-dropdown trigger="click">
    <div class="user-profile-container">
      <img :src="userStore.userInfo.avatar" class="user-avatar" />
      <div class="user-details">
        <span class="user-name">
          {{ userStore.userInfo.nickname || userStore.userInfo.username || "用户" }}
        </span>
        <span class="user-id">工号: {{ userStore.userInfo.username || "未设置" }}</span>
      </div>
    </div>
    <template #dropdown>
      <el-dropdown-menu>
        <el-dropdown-item @click="handleOpenUserProfile">
          {{ $t("navbar.profile") }}
        </el-dropdown-item>
        <!-- <a
          target="_blank"
          href="https://gitee.com/youlaiorg/vue3-element-admin"
        >
          <el-dropdown-item>{{ $t("navbar.gitee") }}</el-dropdown-item>
        </a>
        <a target="_blank" href="https://juejin.cn/post/7228990409909108793">
          <el-dropdown-item>{{ $t("navbar.document") }}</el-dropdown-item>
        </a> -->
        <el-dropdown-item divided @click="logout">
          {{ $t("navbar.logout") }}
        </el-dropdown-item>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
</template>

<script setup lang="ts">
defineOptions({
  name: "UserProfile",
});

import { useTagsViewStore, useUserStore, dataSystemStore } from "@/store";

const tagsViewStore = useTagsViewStore();
const userStore = useUserStore();
const systemStore = dataSystemStore();

const route = useRoute();
const router = useRouter();

/**
 * 打开个人中心页面
 */
function handleOpenUserProfile() {
  router.push({ name: "Profile" });
}

/**
 * 注销登出
 */
function logout() {
  ElMessageBox.confirm("确定注销并退出系统吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
    lockScroll: false,
  }).then(() => {
    userStore
      .logout()
      .then(() => {
        tagsViewStore.delAllViews();
        systemStore.clearArchitectureInfo();
      })
      .then(() => {
        router.push(`/login?redirect=${route.fullPath}`);
      });
  });
}
</script>

<style lang="scss" scoped>
.user-profile-container {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px 13px;
  cursor: pointer;

  .user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid #fff;
  }

  .user-details {
    display: flex;
    flex-direction: column;
    gap: 2px;

    .user-name {
      color: #fff;
      font-size: 14px;
      font-weight: 500;
      line-height: 1.2;
    }

    .user-id {
      color: #bdc3c7;
      font-size: 12px;
      line-height: 1.2;
    }
  }
}
</style>
