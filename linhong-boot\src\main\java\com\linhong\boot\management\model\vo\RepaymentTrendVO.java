package com.linhong.boot.management.model.vo;

import lombok.Data;

import java.util.List;

/**
 * 回款趋势数据VO
 */
@Data
public class RepaymentTrendVO {

    /**
     * X轴数据（日期）
     */
    private List<String> xAxis;

    /**
     * 日期数组（完整日期格式）
     */
    private List<String> dates;

    /**
     * 图表系列数据
     */
    private List<SeriesData> series;

    @Data
    public static class SeriesData {
        /**
         * 系列名称
         */
        private String name;

        /**
         * 数据数组
         */
        private List<Number> data;

        /**
         * 图表类型
         */
        private String type;

        /**
         * 颜色
         */
        private String color;
    }
}
