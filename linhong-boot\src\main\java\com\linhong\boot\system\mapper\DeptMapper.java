package com.linhong.boot.system.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.linhong.boot.common.annotation.DataPermission;
import com.linhong.boot.system.model.entity.Dept;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;


@Mapper
public interface DeptMapper extends BaseMapper<Dept> {

    @DataPermission(deptIdColumnName = "id")
    @Override
    List<Dept> selectList(@Param(Constants.WRAPPER) Wrapper<Dept> queryWrapper);
    
    /**
     * select d.id from sys_dept d , sys_user u where u.dept_id = d.id and u.id = #{id}
     * 查询最上级部门id 
     * @return 
     */
    @Select("select d.id from sys_dept d where name = 'root'")
    String selectMogamiDeptId();
}
