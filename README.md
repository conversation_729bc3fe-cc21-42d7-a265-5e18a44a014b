# 债务催收管理系统

<div align="center">
  <h1>债务催收管理系统</h1>
  <p>基于 Vue3 + Spring Boot 的现代化债务催收管理平台</p>
  
  <img src="https://img.shields.io/badge/Vue-3.5.12-brightgreen.svg"/>
  <img src="https://img.shields.io/badge/Vite-5.4.11-green.svg"/>
  <img src="https://img.shields.io/badge/Element Plus-2.8.7-blue.svg"/>
  <img src="https://img.shields.io/badge/Spring Boot-3.3.4-brightgreen.svg"/>
  <img src="https://img.shields.io/badge/Java-17-orange.svg"/>
  <img src="https://img.shields.io/badge/MySQL-8.0-blue.svg"/>
  <img src="https://img.shields.io/badge/license-MIT-green.svg"/>
</div>

## 📋 项目简介

债务催收管理系统是一个专业的债务管理和催收平台，采用前后端分离架构，提供完整的债务管理、催收流程、数据分析等功能。系统支持多种催收方式，提供详细的数据统计和分析报告，帮助企业高效管理债务催收业务。

## ✨ 系统特色

- 🎯 **专业催收流程**：完整的债务催收业务流程管理
- 📊 **数据可视化**：丰富的图表和统计分析功能
- 👥 **多角色权限**：支持管理员、催收员等多种角色
- 📱 **响应式设计**：支持PC端和移动端访问
- 🔒 **安全可靠**：完善的权限控制和数据安全保障
- 🚀 **高性能**：基于现代化技术栈，性能优异

## 🏗️ 技术架构

### 前端技术栈
- **框架**: Vue 3.5.12 + TypeScript 5.5.4
- **构建工具**: Vite 5.4.11
- **UI组件**: Element Plus 2.8.7
- **状态管理**: Pinia 2.2.6
- **路由**: Vue Router 4.4.5
- **HTTP客户端**: Axios 1.7.7
- **图表**: ECharts 5.5.1
- **样式**: Sass + UnoCSS
- **代码规范**: ESLint + Prettier + Stylelint

### 后端技术栈
- **框架**: Spring Boot 3.3.4
- **JDK版本**: Java 17
- **数据库**: MySQL 8.0
- **ORM**: MyBatis Plus 3.5.5
- **连接池**: Druid 1.2.23
- **缓存**: Redis + Redisson 3.30.0
- **安全**: Spring Security + JWT
- **文档**: Knife4j 4.5.0 (Swagger)
- **任务调度**: XXL-Job 2.4.1
- **对象存储**: MinIO 8.5.10 / 阿里云OSS
- **工具类**: Hutool 5.8.27

## 📁 项目结构

```
debt-collection-system/
├── CF_web/                    # 前端项目
│   ├── src/
│   │   ├── api/              # API接口
│   │   ├── components/       # 公共组件
│   │   ├── views/            # 页面视图
│   │   │   ├── dashboard/    # 仪表盘
│   │   │   ├── dataSystem/   # 数据系统
│   │   │   ├── system/       # 系统管理
│   │   │   └── ...
│   │   ├── store/            # 状态管理
│   │   ├── router/           # 路由配置
│   │   ├── utils/            # 工具函数
│   │   └── styles/           # 样式文件
│   ├── public/               # 静态资源
│   └── package.json          # 依赖配置
│
├── linhong-boot/             # 后端项目
│   ├── src/main/java/com/linhong/boot/
│   │   ├── common/           # 公共模块
│   │   ├── config/           # 配置类
│   │   ├── core/             # 核心功能
│   │   ├── management/       # 催收管理模块
│   │   │   ├── controller/   # 控制器
│   │   │   ├── service/      # 业务逻辑
│   │   │   ├── mapper/       # 数据访问
│   │   │   └── model/        # 数据模型
│   │   ├── shared/           # 共享模块
│   │   │   ├── auth/         # 认证授权
│   │   │   ├── codegen/      # 代码生成
│   │   │   ├── file/         # 文件管理
│   │   │   └── websocket/    # WebSocket
│   │   └── system/           # 系统管理模块
│   ├── src/main/resources/
│   │   ├── mapper/           # MyBatis映射文件
│   │   ├── templates/        # 模板文件
│   │   └── application.yml   # 配置文件
│   └── pom.xml               # Maven配置
│
└── README.md                 # 项目说明
```

## 🚀 快速开始

### 环境要求

| 环境 | 版本要求 |
|------|----------|
| Node.js | ≥ 18.0.0 |
| Java | 17+ |
| MySQL | 8.0+ |
| Redis | 6.0+ |

### 前端启动

```bash
# 进入前端目录
cd CF_web

# 安装 pnpm (如果未安装)
npm install pnpm -g

# 设置镜像源(可选)
pnpm config set registry https://registry.npmmirror.com

# 安装依赖
pnpm install

# 启动开发服务器
pnpm run dev

# 构建生产版本
pnpm run build
```

### 后端启动

```bash
# 进入后端目录
cd linhong-boot

# 配置数据库
# 1. 创建数据库 debt_recovery
# 2. 导入 sql/mysql8/debt_recovery.sql

# 配置 Redis
# 确保 Redis 服务正在运行

# 修改配置文件
# 编辑 src/main/resources/application-dev.yml
# 配置数据库连接信息和 Redis 连接信息

# 启动应用
mvn spring-boot:run

# 或者使用 IDE 运行 LinHongBootApplication.java
```

## 🔧 配置说明

### 前端配置

主要配置文件：
- `.env.development` - 开发环境配置
- `.env.production` - 生产环境配置
- `vite.config.ts` - Vite构建配置

### 后端配置

主要配置文件：
- `application.yml` - 主配置文件
- `application-dev.yml` - 开发环境配置
- `application-prod.yml` - 生产环境配置

## 📖 功能模块

### 核心功能
- 🏠 **仪表盘**: 数据概览、统计图表
- 👤 **用户管理**: 催收员管理、权限分配
- 📋 **案件管理**: 债务案件录入、分配、跟进
- 📞 **催收记录**: 催收活动记录、历史查询
- 📊 **数据分析**: 催收效果分析、报表生成
- 💰 **还款管理**: 还款记录、还款计划
- 📱 **联系人管理**: 紧急联系人信息维护

### 系统管理
- 👥 **用户管理**: 系统用户增删改查
- 🔐 **角色管理**: 角色权限配置
- 📋 **菜单管理**: 系统菜单配置
- 📚 **字典管理**: 数据字典维护
- 🏢 **部门管理**: 组织架构管理
- 📝 **操作日志**: 系统操作记录
- 📢 **通知公告**: 系统消息管理

## 🔐 权限说明

系统采用RBAC权限模型：
- **超级管理员**: 拥有所有权限
- **管理员**: 管理催收业务和用户
- **催收员**: 处理分配的催收案件
- **查看员**: 仅查看相关数据

## 📱 接口文档

启动后端服务后，访问接口文档：
- Swagger UI: http://localhost:8989/doc.html
- 接口地址: http://localhost:8989/api/*

## 🚀 部署说明

### 前端部署

```bash
# 构建项目
pnpm run build

# 将 dist 目录部署到 Web 服务器
# 推荐使用 Nginx
```

### 后端部署

```bash
# 打包项目
mvn clean package -Dmaven.test.skip=true

# 运行 JAR 文件
java -jar target/linhong-boot-2.15.0.jar

# 或使用 Docker 部署
docker build -t debt-collection-system .
docker run -p 8989:8989 debt-collection-system
```

## 🤝 贡献指南

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 开源协议

本项目基于 [MIT](LICENSE) 协议开源。

## 🔧 开发工具推荐

### 前端开发

- **IDE**: Visual Studio Code
- **插件推荐**:
  - Vue Language Features (Volar)
  - TypeScript Vue Plugin (Volar)
  - ESLint
  - Prettier
  - Auto Rename Tag
  - Bracket Pair Colorizer

### 后端开发

- **IDE**: IntelliJ IDEA / Eclipse
- **插件推荐**:
  - Lombok
  - MyBatis X
  - Maven Helper
  - RestfulTool

## 📊 系统截图

### 登录页面

![登录页面](docs/images/login.png)

### 仪表盘

![仪表盘](docs/images/dashboard.png)

### 案件管理

![案件管理](docs/images/case-management.png)

### 催收记录

![催收记录](docs/images/collection-records.png)

## 🐛 常见问题

### 前端问题

#### Q: 启动时出现端口被占用

```bash
# 查看端口占用
netstat -ano | findstr :5173
# 或修改端口
pnpm run dev --port 3000
```

#### Q: 依赖安装失败

```bash
# 清除缓存重新安装
pnpm store prune
rm -rf node_modules
pnpm install
```

### 后端问题

#### Q: 数据库连接失败

- 检查数据库服务是否启动
- 确认数据库连接配置是否正确
- 检查数据库用户权限

#### Q: Redis 连接失败

- 确认 Redis 服务是否启动
- 检查 Redis 连接配置
- 确认防火墙设置

## 🔄 更新日志

### v2.15.0 (2024-01-15)

- ✨ 新增催收案件批量导入功能
- 🐛 修复数据统计图表显示问题
- 🔧 优化系统性能和响应速度
- 📝 完善接口文档

### v2.14.0 (2023-12-20)

- ✨ 新增移动端适配
- 🔒 增强系统安全性
- 📊 新增数据导出功能
- 🎨 优化用户界面

## 🎯 路线图

### 近期计划

- [ ] 移动端 App 开发
- [ ] 微信小程序版本
- [ ] 智能催收建议功能
- [ ] 语音通话集成

### 长期规划

- [ ] AI 智能分析
- [ ] 多语言支持
- [ ] 云原生部署
- [ ] 大数据分析平台

## 📈 性能优化

### 前端优化

- 路由懒加载
- 组件按需引入
- 图片懒加载
- 代码分割
- CDN 加速

### 后端优化

- 数据库索引优化
- Redis 缓存策略
- 连接池配置
- 异步处理
- 分页查询优化

## 🔒 安全说明

- 所有接口都需要身份验证
- 敏感数据加密存储
- SQL 注入防护
- XSS 攻击防护
- CSRF 攻击防护
- 接口访问频率限制

## 📞 联系我们

如有问题或建议，请通过以下方式联系：

- 提交 [Issue](../../issues)
- 发送邮件至项目维护者
- 加入技术交流群

## 🙏 致谢

感谢以下开源项目的支持：

- [Vue.js](https://vuejs.org/)
- [Element Plus](https://element-plus.org/)
- [Spring Boot](https://spring.io/projects/spring-boot)
- [MyBatis Plus](https://baomidou.com/)

---

⭐ 如果这个项目对您有帮助，请给我们一个 Star！
