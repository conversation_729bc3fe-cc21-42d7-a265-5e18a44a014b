<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.linhong.boot.management.mapper.CcUserMapper">
  <resultMap id="BaseResultMap" type="com.linhong.boot.management.model.entity.CcUser">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Nov 20 11:23:04 HKT 2024.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="customer_name" jdbcType="VARCHAR" property="customerName" />
    <result column="cardholder_id_number" jdbcType="VARCHAR" property="cardholderIdNumber" />
    <result column="cardholder_code" jdbcType="VARCHAR" property="cardholderCode" />
    <result column="customer_index_number" jdbcType="VARCHAR" property="customerIndexNumber" />
    <result column="entrust_amount" jdbcType="DECIMAL" property="entrustAmount" />
    <result column="entrust_total_amount" jdbcType="DECIMAL" property="entrustTotalAmount" />
    <result column="entrust_principal" jdbcType="DECIMAL" property="entrustPrincipal" />
    <result column="entrust_principal_total" jdbcType="DECIMAL" property="entrustPrincipalTotal" />
    <result column="entrust_overdue_period" jdbcType="VARCHAR" property="entrustOverduePeriod" />
    <result column="target_period" jdbcType="VARCHAR" property="targetPeriod" />
    <result column="case_city" jdbcType="VARCHAR" property="caseCity" />
    <result column="last_follow_up_date" jdbcType="DATE" property="lastFollowUpDate" />
    <result column="entrust_start_date" jdbcType="DATE" property="entrustStartDate" />
    <result column="entrust_end_date" jdbcType="DATE" property="entrustEndDate" />
    <result column="case_type" jdbcType="VARCHAR" property="caseType" />
    <result column="new_old_case_flag" jdbcType="CHAR" property="newOldCaseFlag" />
    <result column="batch_number" jdbcType="VARCHAR" property="batchNumber" />
    <result column="balance_ops" jdbcType="DECIMAL" property="balanceOps" />
    <result column="principal_ops" jdbcType="DECIMAL" property="principalOps" />
    <result column="current_overdue_period" jdbcType="VARCHAR" property="currentOverduePeriod" />
    <result column="household_city" jdbcType="VARCHAR" property="householdCity" />
    <result column="customer_age" jdbcType="INTEGER" property="customerAge" />
    <result column="occupation" jdbcType="VARCHAR" property="occupation" />
    <result column="card_opening_date" jdbcType="DATE" property="cardOpeningDate" />
    <result column="credit_limit" jdbcType="DECIMAL" property="creditLimit" />
    <result column="last_payment_date" jdbcType="DATE" property="lastPaymentDate" />
    <result column="skill_group" jdbcType="VARCHAR" property="skillGroup" />
    <result column="account_number_last7" jdbcType="VARCHAR" property="accountNumberLast7" />
    <result column="entrust_age_bracket" jdbcType="VARCHAR" property="entrustAgeBracket" />
    <result column="entrust_amount_bracket" jdbcType="VARCHAR" property="entrustAmountBracket" />
    <result column="entrust_commission_bracket" jdbcType="VARCHAR" property="entrustCommissionBracket" />
    <result column="rating_bracket" jdbcType="VARCHAR" property="ratingBracket" />
    <result column="is_litigation" jdbcType="VARCHAR" property="isLitigation" />
    <result column="customer_current_month_repayment" jdbcType="DECIMAL" property="customerCurrentMonthRepayment" />
    <result column="customer_previous_day_repayment" jdbcType="DECIMAL" property="customerPreviousDayRepayment" />
    <result column="case_grabbing_user" jdbcType="VARCHAR" property="caseGrabbingUser" />
    <result column="special_project_type" jdbcType="VARCHAR" property="specialProjectType" />
    <result column="retention_count" jdbcType="INTEGER" property="retentionCount" />
    <result column="personalized_installment_status" jdbcType="VARCHAR" property="personalizedInstallmentStatus" />
    <result column="personalized_installment_fulfillment_status" jdbcType="VARCHAR" property="personalizedInstallmentFulfillmentStatus" />
    <result column="complaint_label" jdbcType="VARCHAR" property="complaintLabel" />
    <result column="personalized_fulfillment_label" jdbcType="VARCHAR" property="personalizedFulfillmentLabel" />
    <result column="litigation_label" jdbcType="VARCHAR" property="litigationLabel" />
    <result column="smart_voice_label" jdbcType="VARCHAR" property="smartVoiceLabel" />
    <result column="special_project_label" jdbcType="VARCHAR" property="specialProjectLabel" />
    <result column="is_litigation_for_commission" jdbcType="CHAR" property="isLitigationForCommission" />
    <result column="case_agency" jdbcType="VARCHAR" property="caseAgency" />
    <result column="follow_up_status" jdbcType="CHAR" property="followUpStatus" />
  	<result column="outsource_serial_number" jdbcType="VARCHAR" property="outsourceSerialNumber" />
  	<result column="is_mark_change" jdbcType="CHAR" property="isMarkChange" />
  	<result column="lost_contact_query_result" jdbcType="VARCHAR" property="lostContactQueryResult" />
  	<result column="user_or_dept" jdbcType="VARCHAR" property="userOrDept" />
  	<result column="unfollowed_days" jdbcType="INTEGER" property="unfollowedDays" />
  	<result column="work_unit" jdbcType="VARCHAR" property="workUnit" />
  	<result column="bank_card_number" jdbcType="VARCHAR" property="bankCardNumber" />
      <result column="is_lock" jdbcType="CHAR" property="isLock" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Nov 20 11:23:04 HKT 2024.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Example_Where_Clause_page">
    <where>
    	<!-- 客户姓名 -->
     	<if test="example.customerName !=null and example.customerName.trim() neq '' ">
     		and cu.customer_name LIKE CONCAT('%', #{example.customerName}, '%')
     	</if>
     	<!-- 批次号 -->
     	<if test="example.batchNumber !=null and example.batchNumber.size() > 0 ">
     		and cu.batch_number IN
     		<foreach item="s" index="index" collection="example.batchNumber" open="(" separator="," close=")">
               #{s}
            </foreach>
     	</if>
     	<!-- 客户索引号 -->
     	<if test="example.customerIndexNumber !=null and example.customerIndexNumber.trim() neq '' ">
     		and cu.customer_index_number LIKE CONCAT('%', #{example.customerIndexNumber}, '%')
     	</if>
     	<!-- 案件类型 -->
     	<if test="example.caseType !=null and example.caseType.trim() neq '' ">
     		and cu.case_type = #{example.caseType}
     	</if>
     	<!-- 委案机构 -->
     	<if test="example.caseAgency !=null and example.caseAgency.trim() neq '' ">
     		and cu.case_agency = #{example.caseAgency}
     	</if>
     	<!-- 跟进状态 -->
     	<if test="example.followUpStatus !=null and example.followUpStatus.trim() neq '' ">
     		and cu.follow_up_status = #{example.followUpStatus}
     	</if>
     	<if test="example.followUpStatus ==null">
     		<if test="example.followUpStatusList != null and example.followUpStatusList.size() > 0">
            	and cu.follow_up_status IN
            	<foreach item="s" index="index" collection="example.followUpStatusList" open="(" separator="," close=")">
               		#{s}
            	</foreach>
        	</if>
     	</if>
     	
     	<!-- 委托开始日期 -->
     	<if test="example.entrustStartDate !=null and example.entrustStartDate.length == 2 ">
     		<if test="example.entrustStartDate[0] != null and example.entrustStartDate[1] != null">
            	<bind name="sT" value="example.entrustStartDate[0]"/>
            	<bind name="eT" value="example.entrustStartDate[1]"/>
                    and cu.entrust_start_date BETWEEN #{sT} and #{eT}
            </if>
     	</if>
     	<!-- 委托结束日期 -->
     	<if test="example.entrustEndDate !=null and example.entrustEndDate.length == 2 ">
     		<if test="example.entrustEndDate[0] != null and example.entrustEndDate[1] != null">
            	<bind name="sT" value="example.entrustEndDate[0]"/>
            	<bind name="eT" value="example.entrustEndDate[1]"/>
                    and cu.entrust_end_date BETWEEN #{sT} and #{eT}
            </if>
     	</if>
     	<!-- 委托总金额 -->
     	<if test="example.entrustTotalAmount !=null and example.entrustTotalAmount.length == 2 ">
     			<bind name="sA" value="example.entrustTotalAmount[0]"/>
            	<bind name="eA" value="example.entrustTotalAmount[1]"/>
     		and cu.entrust_total_amount BETWEEN #{sA} and #{eA}
     	</if>
     	<!-- 持卡人身份证号 -->
     	<if test="example.cardholderIdNumber !=null and example.cardholderIdNumber.trim() neq '' ">
     		and cu.cardholder_id_number LIKE CONCAT('%', #{example.cardholderIdNumber}, '%')
     	</if>
     	<!-- 持卡人代码 -->
     	<if test="example.cardholderCode !=null and example.cardholderCode.trim() neq '' ">
     		and cu.cardholder_code LIKE CONCAT('%', #{example.cardholderCode}, '%')
     	</if>
     	<!-- 委托时月龄分档 -->
     	<if test="example.entrustAgeBracket !=null and example.entrustAgeBracket.trim() neq '' ">
     		and cu.entrust_age_bracket = #{example.entrustAgeBracket}
     	</if>
     	<!-- 委托时佣金分档 -->
     	<if test="example.entrustCommissionBracket !=null and example.entrustCommissionBracket.trim() neq '' ">
     		and cu.entrust_commission_bracket = #{example.entrustCommissionBracket}
     	</if>
     	<!-- 失联查询结果 -->
     	<if test="example.lostContactQueryResult !=null and example.lostContactQueryResult.trim() neq '' ">
     		and cu.lost_contact_query_result = #{example.lostContactQueryResult}
     	</if>
     	<if test="example.isMarkChange !=null and example.isMarkChange.trim() neq '' ">
     		and cu.is_mark_change = #{example.isMarkChange}
     	</if>
     	<if test="example.isLock !=null and example.isLock.trim() neq '' ">
     		and cu.is_lock = #{example.isLock}
     	</if>
     	<!-- 持卡人身份证号 -->
     	<if test="example.outsourceSerialNumber !=null and example.outsourceSerialNumber.trim() neq '' ">
     		and cu.outsource_serial_number LIKE CONCAT('%', #{example.outsourceSerialNumber}, '%')
     	</if>
     	<!-- 催收人/组织 -->
     	<if test="example.userOrDept !=null and example.userOrDept.trim() neq '' ">
     		<if test="example.uIds != null and example.uIds.size() > 0">
            	and w.user_id IN
            	<foreach item="uId" index="index" collection="example.uIds" open="(" separator="," close=")">
               		#{uId}
            	</foreach>
        	</if>
     	</if>
     	<if test="example.userOrDept ==null ">
     		AND (cu.user_Or_Dept IS NULL or cu.user_or_dept = '')
     	</if>
     	
    </where>
  </sql>
  
  <sql id="CASElOCK_Where_Clause_page">
    <where>
    	<!-- 客户姓名 -->
     	<if test="example.customerName !=null and example.customerName.trim() neq '' ">
     		and cu.customer_name LIKE CONCAT('%', #{example.customerName}, '%')
     	</if>
     	<!-- 批次号 -->
     	<if test="example.batchNumber !=null and example.batchNumber.trim() neq '' ">
     		and cu.batch_number = #{example.batchNumber}
     	</if>
     	<!-- 客户索引号 -->
     	<if test="example.customerIndexNumber !=null and example.customerIndexNumber.trim() neq '' ">
     		and cu.customer_index_number = #{example.customerIndexNumber}
     	</if>
     	<!-- 案件类型 -->
     	<if test="example.caseType !=null and example.caseType.trim() neq '' ">
     		and cu.case_type = #{example.caseType}
     	</if>
     	<!-- 委案机构 -->
     	<if test="example.caseAgency !=null and example.caseAgency.trim() neq '' ">
     		and cu.case_agency = #{example.caseAgency}
     	</if>
     	<!-- 跟进状态 -->
     	<if test="example.followUpStatus !=null and example.followUpStatus.trim() neq '' ">
     		and cu.follow_up_status = #{example.followUpStatus}
     	</if>
     	<if test="example.followUpStatus ==null">
     		<if test="example.followUpStatusList != null and example.followUpStatusList.size() > 0">
            	and cu.follow_up_status IN
            	<foreach item="s" index="index" collection="example.followUpStatusList" open="(" separator="," close=")">
               		#{s}
            	</foreach>
        	</if>
     	</if>
     	<!-- 委托开始日期 -->
     	<if test="example.entrustStartDate !=null and example.entrustStartDate.length == 2 ">
     		<if test="example.entrustStartDate[0] != null and example.entrustStartDate[1] != null">
            	<bind name="sT" value="example.entrustStartDate[0]"/>
            	<bind name="eT" value="example.entrustStartDate[1]"/>
                    and cu.entrust_start_date BETWEEN #{sT} and #{eT}
            </if>
     	</if>
     	<!-- 委托结束日期 -->
     	<if test="example.entrustEndDate !=null and example.entrustEndDate.length == 2 ">
     		<if test="example.entrustEndDate[0] != null and example.entrustEndDate[1] != null">
            	<bind name="sT" value="example.entrustEndDate[0]"/>
            	<bind name="eT" value="example.entrustEndDate[1]"/>
                    and cu.entrust_end_date BETWEEN #{sT} and #{eT}
            </if>
     	</if>
     	<!-- 委托总金额 -->
     	<if test="example.entrustTotalAmount !=null and example.entrustTotalAmount.length == 2 ">
     			<bind name="sA" value="example.entrustTotalAmount[0]"/>
            	<bind name="eA" value="example.entrustTotalAmount[1]"/>
     		and cu.entrust_total_amount BETWEEN #{sA} and #{eA}
     	</if>
     	<!-- 持卡人身份证号 -->
     	<if test="example.cardholderIdNumber !=null and example.cardholderIdNumber.trim() neq '' ">
     		and cu.cardholder_id_number LIKE CONCAT('%', #{example.cardholderIdNumber}, '%')
     	</if>
     	<!-- 持卡人代码 -->
     	<if test="example.cardholderCode !=null and example.cardholderCode.trim() neq '' ">
     		and cu.cardholder_code LIKE CONCAT('%', #{example.cardholderCode}, '%')
     	</if>
     	<!-- 委托时月龄分档 -->
     	<if test="example.entrustAgeBracket !=null and example.entrustAgeBracket.trim() neq '' ">
     		and cu.entrust_age_bracket = #{example.entrustAgeBracket}
     	</if>
     	<!-- 委托时佣金分档 -->
     	<if test="example.entrustCommissionBracket !=null and example.entrustCommissionBracket.trim() neq '' ">
     		and cu.entrust_commission_bracket = #{example.entrustCommissionBracket}
     	</if>
     	<!-- 失联查询结果 -->
     	<if test="example.lostContactQueryResult !=null and example.lostContactQueryResult.trim() neq '' ">
     		and cu.lost_contact_query_result = #{example.lostContactQueryResult}
     	</if>
     	<if test="example.isMarkChange !=null and example.isMarkChange.trim() neq '' ">
     		and cu.is_mark_change = #{example.isMarkChange}
     	</if>
     	<!-- 持卡人身份证号 -->
     	<if test="example.outsourceSerialNumber !=null and example.outsourceSerialNumber.trim() neq '' ">
     		and cu.outsource_serial_number LIKE CONCAT('%', #{example.outsourceSerialNumber}, '%')
     	</if>
     	<!-- 催收人/组织 -->
     	<if test="example.userOrDept !=null and example.userOrDept.trim() neq '' ">
     		<if test="example.uIds != null and example.uIds.size() > 0">
            	and w.user_id IN
            	<foreach item="uId" index="index" collection="example.uIds" open="(" separator="," close=")">
               		#{uId}
            	</foreach>
        	</if>
     	</if>
		<if test="example.userOrDept ==null ">
     		AND (cu.user_Or_Dept IS NULL or cu.user_or_dept = '')
     	</if>
     	
    </where>
  </sql>
  
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Nov 20 11:23:04 HKT 2024.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Nov 20 11:23:04 HKT 2024.
    -->
    id, customer_name, cardholder_id_number, cardholder_code, customer_index_number, 
    entrust_amount, entrust_total_amount, entrust_principal, entrust_principal_total, 
    entrust_overdue_period, target_period, case_city, last_follow_up_date, entrust_start_date, 
    entrust_end_date, case_type, new_old_case_flag, batch_number, balance_ops, principal_ops, 
    current_overdue_period, household_city, customer_age, occupation, card_opening_date, 
    credit_limit, last_payment_date, skill_group, account_number_last7, entrust_age_bracket, 
    entrust_amount_bracket, entrust_commission_bracket, rating_bracket, is_litigation, 
    customer_current_month_repayment, customer_previous_day_repayment, case_grabbing_user, 
    special_project_type, retention_count, personalized_installment_status, personalized_installment_fulfillment_status, 
    complaint_label, personalized_fulfillment_label, litigation_label, smart_voice_label, 
    special_project_label, is_litigation_for_commission, case_agency, follow_up_status,
    outsource_serial_number,is_mark_change,lost_contact_query_result,unfollowed_days,user_or_dept,work_unit,bank_card_number,is_lock
  </sql>
  <sql id="Page_Column_List">
    cu.id, customer_name, cardholder_id_number, cardholder_code, customer_index_number, 
    entrust_amount, entrust_total_amount, entrust_principal, entrust_principal_total, 
    entrust_overdue_period, target_period, case_city, last_follow_up_date, entrust_start_date, 
    entrust_end_date, case_type, new_old_case_flag, batch_number, balance_ops, principal_ops, 
    current_overdue_period, household_city, customer_age, occupation, card_opening_date, 
    credit_limit, last_payment_date, skill_group, account_number_last7, entrust_age_bracket, 
    entrust_amount_bracket, entrust_commission_bracket, rating_bracket, is_litigation, 
    customer_current_month_repayment, customer_previous_day_repayment, case_grabbing_user, 
    special_project_type, retention_count, personalized_installment_status, personalized_installment_fulfillment_status, 
    complaint_label, personalized_fulfillment_label, litigation_label, smart_voice_label, 
    special_project_label, is_litigation_for_commission, case_agency, follow_up_status,
    outsource_serial_number,is_mark_change,lost_contact_query_result,unfollowed_days,user_or_dept,work_unit,bank_card_number,is_lock
  </sql>
  
  <sql id="ext_Column_List">
    cu.id ,customer_name, cardholder_id_number, cardholder_code, customer_index_number, 
    entrust_amount, entrust_total_amount, entrust_principal, entrust_principal_total, 
    entrust_overdue_period, target_period, case_city, last_follow_up_date, entrust_start_date, 
    entrust_end_date, case_type, new_old_case_flag, batch_number, balance_ops, principal_ops, 
    current_overdue_period, household_city, customer_age, occupation, card_opening_date, 
    credit_limit, last_payment_date, skill_group, account_number_last7, entrust_age_bracket, 
    entrust_amount_bracket, entrust_commission_bracket, rating_bracket, is_litigation, 
    customer_current_month_repayment, customer_previous_day_repayment, case_grabbing_user, 
    special_project_type, retention_count, personalized_installment_status, personalized_installment_fulfillment_status, 
    complaint_label, personalized_fulfillment_label, litigation_label, smart_voice_label, 
    special_project_label, is_litigation_for_commission, case_agency, follow_up_status,
    outsource_serial_number,is_mark_change,lost_contact_query_result,unfollowed_days
  </sql>
  
  
  <select id="getPoolPage"  resultType="com.linhong.boot.management.model.vo.CcUserVo">
    select
    <include refid="Page_Column_List" />
    from cc_user cu left join cc_user_work w on cu.id = w.cc_id left JOIN sys_user su on w.user_id = su.id
    <include refid="Example_Where_Clause_page" /> 
    <if test="example.orderBy !=null and example.orderBy.trim() neq '' ">
        order by #{example.orderBy} #{example.ascOrDesc}
   	</if>
  </select>
  
  <select id="getWorkPage"  resultType="com.linhong.boot.management.model.vo.CcUserVo">
    select
    <include refid="Page_Column_List" />
    from cc_user cu left join cc_user_work w on cu.id = w.cc_id left JOIN sys_user su on w.user_id = su.id
    <include refid="Example_Where_Clause_page" />
    <if test="example.userOrDept !=null and example.userOrDept.trim() neq '' ">
        AND w.work_status is null
    </if>
    <if test="example.orderBy !=null and example.orderBy.trim() neq '' ">
        order by #{example.orderBy} #{example.ascOrDesc}
   	</if>
  </select>
  
  <select id="getPoolPage2"  resultType="com.linhong.boot.management.model.entity.CcUser">
    select DISTINCT
    <include refid="Page_Column_List" />
    from cc_user cu left join cc_user_work w on cu.id = w.cc_id left JOIN sys_user su on w.user_id = su.id
    <include refid="Example_Where_Clause_page" />
  </select>
  
  <select id="getPoolExt"  resultType="com.linhong.boot.management.model.entity.CcUser">
    select DISTINCT
    <include refid="ext_Column_List" />
    from cc_user cu left join cc_user_work w on cu.id = w.cc_id left JOIN sys_user su on w.user_id = su.id
    <include refid="Example_Where_Clause_page" />
  </select>
  
  <select id="getWorkPoolExt"  resultType="com.linhong.boot.management.model.entity.CcUser">
    select DISTINCT
    <include refid="ext_Column_List" />
    from cc_user cu left join cc_user_work w on cu.id = w.cc_id left JOIN sys_user su on w.user_id = su.id
    <include refid="Example_Where_Clause_page" />
    <if test="example.userOrDept !=null and example.userOrDept.trim() neq '' ">
        AND w.work_status is null
    </if>
  </select>
  
  <select id="getCaseList"  resultType="com.linhong.boot.management.model.entity.CcUser">
    select
    <include refid="Base_Column_List" />
    from cc_user cu
    <include refid="CASElOCK_Where_Clause_page" /> 
  </select>
  
  <!-- 总金额 -->
  <select id="getPoolPageAmount" resultType="com.linhong.boot.management.model.vo.CcUserVo">
  	select sum(cu.entrust_total_amount) as sumEntrustTotalAmount , sum(cu.entrust_principal_total) as sumentrustPrincipalTotal 
  	from cc_user cu left join cc_user_work w on cu.id = w.cc_id left JOIN sys_user su on w.user_id = su.id
    <include refid="Example_Where_Clause_page" />
  </select>
  
  <select id="selectByExample" parameterType="com.linhong.boot.management.model.entity.CcUserExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Nov 20 11:23:04 HKT 2024.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from cc_user
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Nov 20 11:23:04 HKT 2024.
    -->
    select 
    <include refid="Base_Column_List" />
    from cc_user
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Nov 20 11:23:04 HKT 2024.
    -->
    delete from cc_user
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.linhong.boot.management.model.entity.CcUserExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Nov 20 11:23:04 HKT 2024.
    -->
    delete from cc_user
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  
  <insert id="insertSelective" parameterType="com.linhong.boot.management.model.entity.CcUser">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Nov 20 11:23:04 HKT 2024.
    -->
    insert into cc_user
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="customerName != null">
        customer_name,
      </if>
      <if test="cardholderIdNumber != null">
        cardholder_id_number,
      </if>
      <if test="cardholderCode != null">
        cardholder_code,
      </if>
      <if test="customerIndexNumber != null">
        customer_index_number,
      </if>
      <if test="entrustAmount != null">
        entrust_amount,
      </if>
      <if test="entrustTotalAmount != null">
        entrust_total_amount,
      </if>
      <if test="entrustPrincipal != null">
        entrust_principal,
      </if>
      <if test="entrustPrincipalTotal != null">
        entrust_principal_total,
      </if>
      <if test="entrustOverduePeriod != null">
        entrust_overdue_period,
      </if>
      <if test="targetPeriod != null">
        target_period,
      </if>
      <if test="caseCity != null">
        case_city,
      </if>
      <if test="lastFollowUpDate != null">
        last_follow_up_date,
      </if>
      <if test="entrustStartDate != null">
        entrust_start_date,
      </if>
      <if test="entrustEndDate != null">
        entrust_end_date,
      </if>
      <if test="caseType != null">
        case_type,
      </if>
      <if test="newOldCaseFlag != null">
        new_old_case_flag,
      </if>
      <if test="batchNumber != null">
        batch_number,
      </if>
      <if test="balanceOps != null">
        balance_ops,
      </if>
      <if test="principalOps != null">
        principal_ops,
      </if>
      <if test="currentOverduePeriod != null">
        current_overdue_period,
      </if>
      <if test="householdCity != null">
        household_city,
      </if>
      <if test="customerAge != null">
        customer_age,
      </if>
      <if test="occupation != null">
        occupation,
      </if>
      <if test="cardOpeningDate != null">
        card_opening_date,
      </if>
      <if test="creditLimit != null">
        credit_limit,
      </if>
      <if test="lastPaymentDate != null">
        last_payment_date,
      </if>
      <if test="skillGroup != null">
        skill_group,
      </if>
      <if test="accountNumberLast7 != null">
        account_number_last7,
      </if>
      <if test="entrustAgeBracket != null">
        entrust_age_bracket,
      </if>
      <if test="entrustAmountBracket != null">
        entrust_amount_bracket,
      </if>
      <if test="entrustCommissionBracket != null">
        entrust_commission_bracket,
      </if>
      <if test="ratingBracket != null">
        rating_bracket,
      </if>
      <if test="isLitigation != null">
        is_litigation,
      </if>
      <if test="customerCurrentMonthRepayment != null">
        customer_current_month_repayment,
      </if>
      <if test="customerPreviousDayRepayment != null">
        customer_previous_day_repayment,
      </if>
      <if test="caseGrabbingUser != null">
        case_grabbing_user,
      </if>
      <if test="specialProjectType != null">
        special_project_type,
      </if>
      <if test="retentionCount != null">
        retention_count,
      </if>
      <if test="personalizedInstallmentStatus != null">
        personalized_installment_status,
      </if>
      <if test="personalizedInstallmentFulfillmentStatus != null">
        personalized_installment_fulfillment_status,
      </if>
      <if test="complaintLabel != null">
        complaint_label,
      </if>
      <if test="personalizedFulfillmentLabel != null">
        personalized_fulfillment_label,
      </if>
      <if test="litigationLabel != null">
        litigation_label,
      </if>
      <if test="smartVoiceLabel != null">
        smart_voice_label,
      </if>
      <if test="specialProjectLabel != null">
        special_project_label,
      </if>
      <if test="isLitigationForCommission != null">
        is_litigation_for_commission,
      </if>
      <if test="caseAgency != null">
        case_agency,
      </if>
      <if test="followUpStatus != null">
        follow_up_status,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="customerName != null">
        #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="cardholderIdNumber != null">
        #{cardholderIdNumber,jdbcType=VARCHAR},
      </if>
      <if test="cardholderCode != null">
        #{cardholderCode,jdbcType=VARCHAR},
      </if>
      <if test="customerIndexNumber != null">
        #{customerIndexNumber,jdbcType=VARCHAR},
      </if>
      <if test="entrustAmount != null">
        #{entrustAmount,jdbcType=DECIMAL},
      </if>
      <if test="entrustTotalAmount != null">
        #{entrustTotalAmount,jdbcType=DECIMAL},
      </if>
      <if test="entrustPrincipal != null">
        #{entrustPrincipal,jdbcType=DECIMAL},
      </if>
      <if test="entrustPrincipalTotal != null">
        #{entrustPrincipalTotal,jdbcType=DECIMAL},
      </if>
      <if test="entrustOverduePeriod != null">
        #{entrustOverduePeriod,jdbcType=VARCHAR},
      </if>
      <if test="targetPeriod != null">
        #{targetPeriod,jdbcType=VARCHAR},
      </if>
      <if test="caseCity != null">
        #{caseCity,jdbcType=VARCHAR},
      </if>
      <if test="lastFollowUpDate != null">
        #{lastFollowUpDate,jdbcType=DATE},
      </if>
      <if test="entrustStartDate != null">
        #{entrustStartDate,jdbcType=DATE},
      </if>
      <if test="entrustEndDate != null">
        #{entrustEndDate,jdbcType=DATE},
      </if>
      <if test="caseType != null">
        #{caseType,jdbcType=VARCHAR},
      </if>
      <if test="newOldCaseFlag != null">
        #{newOldCaseFlag,jdbcType=CHAR},
      </if>
      <if test="batchNumber != null">
        #{batchNumber,jdbcType=VARCHAR},
      </if>
      <if test="balanceOps != null">
        #{balanceOps,jdbcType=DECIMAL},
      </if>
      <if test="principalOps != null">
        #{principalOps,jdbcType=DECIMAL},
      </if>
      <if test="currentOverduePeriod != null">
        #{currentOverduePeriod,jdbcType=VARCHAR},
      </if>
      <if test="householdCity != null">
        #{householdCity,jdbcType=VARCHAR},
      </if>
      <if test="customerAge != null">
        #{customerAge,jdbcType=INTEGER},
      </if>
      <if test="occupation != null">
        #{occupation,jdbcType=VARCHAR},
      </if>
      <if test="cardOpeningDate != null">
        #{cardOpeningDate,jdbcType=DATE},
      </if>
      <if test="creditLimit != null">
        #{creditLimit,jdbcType=DECIMAL},
      </if>
      <if test="lastPaymentDate != null">
        #{lastPaymentDate,jdbcType=DATE},
      </if>
      <if test="skillGroup != null">
        #{skillGroup,jdbcType=VARCHAR},
      </if>
      <if test="accountNumberLast7 != null">
        #{accountNumberLast7,jdbcType=CHAR},
      </if>
      <if test="entrustAgeBracket != null">
        #{entrustAgeBracket,jdbcType=VARCHAR},
      </if>
      <if test="entrustAmountBracket != null">
        #{entrustAmountBracket,jdbcType=VARCHAR},
      </if>
      <if test="entrustCommissionBracket != null">
        #{entrustCommissionBracket,jdbcType=VARCHAR},
      </if>
      <if test="ratingBracket != null">
        #{ratingBracket,jdbcType=VARCHAR},
      </if>
      <if test="isLitigation != null">
        #{isLitigation,jdbcType=CHAR},
      </if>
      <if test="customerCurrentMonthRepayment != null">
        #{customerCurrentMonthRepayment,jdbcType=DECIMAL},
      </if>
      <if test="customerPreviousDayRepayment != null">
        #{customerPreviousDayRepayment,jdbcType=DECIMAL},
      </if>
      <if test="caseGrabbingUser != null">
        #{caseGrabbingUser,jdbcType=VARCHAR},
      </if>
      <if test="specialProjectType != null">
        #{specialProjectType,jdbcType=VARCHAR},
      </if>
      <if test="retentionCount != null">
        #{retentionCount,jdbcType=INTEGER},
      </if>
      <if test="personalizedInstallmentStatus != null">
        #{personalizedInstallmentStatus,jdbcType=VARCHAR},
      </if>
      <if test="personalizedInstallmentFulfillmentStatus != null">
        #{personalizedInstallmentFulfillmentStatus,jdbcType=VARCHAR},
      </if>
      <if test="complaintLabel != null">
        #{complaintLabel,jdbcType=VARCHAR},
      </if>
      <if test="personalizedFulfillmentLabel != null">
        #{personalizedFulfillmentLabel,jdbcType=VARCHAR},
      </if>
      <if test="litigationLabel != null">
        #{litigationLabel,jdbcType=VARCHAR},
      </if>
      <if test="smartVoiceLabel != null">
        #{smartVoiceLabel,jdbcType=VARCHAR},
      </if>
      <if test="specialProjectLabel != null">
        #{specialProjectLabel,jdbcType=VARCHAR},
      </if>
      <if test="isLitigationForCommission != null">
        #{isLitigationForCommission,jdbcType=CHAR},
      </if>
      <if test="caseAgency != null">
        #{caseAgency,jdbcType=VARCHAR},
      </if>
      <if test="followUpStatus != null">
        #{followUpStatus,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.linhong.boot.management.model.entity.CcUserExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Nov 20 11:23:04 HKT 2024.
    -->
    select count(*) from cc_user
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <!-- 
  <update id="updateBatchByIdNumber">
  	<foreach collection="ccUsers" item="u" separator=";">
  		UPDATE CC_USER
  		  SET
		  customer_name = #{u.customerName,jdbcType=VARCHAR},
	      cardholder_id_number = #{u.cardholderIdNumber,jdbcType=VARCHAR},
	      cardholder_code = #{u.cardholderCode,jdbcType=VARCHAR},
	      customer_index_number = #{u.customerIndexNumber,jdbcType=VARCHAR},
	      entrust_amount = #{u.entrustAmount,jdbcType=DECIMAL},
	      entrust_total_amount = #{u.entrustTotalAmount,jdbcType=DECIMAL},
	      entrust_principal = #{u.entrustPrincipal,jdbcType=DECIMAL},
	      entrust_principal_total = #{u.entrustPrincipalTotal,jdbcType=DECIMAL},
	      entrust_overdue_period = #{u.entrustOverduePeriod,jdbcType=VARCHAR},
	      target_period = #{u.targetPeriod,jdbcType=VARCHAR},
	      case_city = #{u.caseCity,jdbcType=VARCHAR},
	      last_follow_up_date = #{u.lastFollowUpDate,jdbcType=DATE},
	      entrust_start_date = #{u.entrustStartDate,jdbcType=DATE},
	      entrust_end_date = #{u.entrustEndDate,jdbcType=DATE},
	      case_type = #{u.caseType,jdbcType=VARCHAR},
	      new_old_case_flag = #{u.newOldCaseFlag,jdbcType=VARCHAR},
	      batch_number = #{u.batchNumber,jdbcType=VARCHAR},
	      balance_ops = #{u.balanceOps,jdbcType=DECIMAL},
	      principal_ops = #{u.principalOps,jdbcType=DECIMAL},
	      current_overdue_period = #{u.currentOverduePeriod,jdbcType=VARCHAR},
	      household_city = #{u.householdCity,jdbcType=VARCHAR},
	      customer_age = #{u.customerAge,jdbcType=INTEGER},
	      occupation = #{u.occupation,jdbcType=VARCHAR},
	      card_opening_date = #{u.cardOpeningDate,jdbcType=DATE},
	      credit_limit = #{u.creditLimit,jdbcType=DECIMAL},
	      last_payment_date = #{u.lastPaymentDate,jdbcType=DATE},
	      skill_group = #{u.skillGroup,jdbcType=VARCHAR},
	      account_number_last7 = #{u.accountNumberLast7,jdbcType=VARCHAR},
	      entrust_age_bracket = #{u.entrustAgeBracket,jdbcType=VARCHAR},
	      entrust_amount_bracket = #{u.entrustAmountBracket,jdbcType=VARCHAR},
	      entrust_commission_bracket = #{u.entrustCommissionBracket,jdbcType=VARCHAR},
	      rating_bracket = #{u.ratingBracket,jdbcType=VARCHAR},
	      is_litigation = #{u.isLitigation,jdbcType=VARCHAR},
	      customer_current_month_repayment = #{u.customerCurrentMonthRepayment,jdbcType=DECIMAL},
	      customer_previous_day_repayment = #{u.customerPreviousDayRepayment,jdbcType=DECIMAL},
	      case_grabbing_user = #{u.caseGrabbingUser,jdbcType=VARCHAR},
	      special_project_type = #{u.specialProjectType,jdbcType=VARCHAR},
	      retention_count = #{u.retentionCount,jdbcType=INTEGER},
	      personalized_installment_status = #{u.personalizedInstallmentStatus,jdbcType=VARCHAR},
	      personalized_installment_fulfillment_status = #{u.personalizedInstallmentFulfillmentStatus,jdbcType=VARCHAR},
	      complaint_label = #{u.complaintLabel,jdbcType=VARCHAR},
	      personalized_fulfillment_label = #{u.personalizedFulfillmentLabel,jdbcType=VARCHAR},
	      litigation_label = #{u.litigationLabel,jdbcType=VARCHAR},
	      smart_voice_label = #{u.smartVoiceLabel,jdbcType=VARCHAR},
	      special_project_label = #{u.specialProjectLabel,jdbcType=VARCHAR},
	      is_litigation_for_commission = #{u.isLitigationForCommission,jdbcType=VARCHAR},
	      case_agency = #{u.caseAgency,jdbcType=VARCHAR}
  		where customer_index_number = #{u.customerIndexNumber,jdbcType=VARCHAR}
  	</foreach>
  </update>
   -->
  <update id="updateBatchByIdNumber">
  	<foreach collection="ccUsers" item="u" separator=";">
  		update cc_user
  	<set>
	   <if test="u.customerName != null">
        customer_name = #{u.customerName,jdbcType=VARCHAR},
      </if>
      <if test="u.cardholderIdNumber != null">
        cardholder_id_number = #{u.cardholderIdNumber,jdbcType=VARCHAR},
      </if>
      <if test="u.cardholderCode != null">
        cardholder_code = #{u.cardholderCode,jdbcType=VARCHAR},
      </if>
      <if test="u.customerIndexNumber != null">
        customer_index_number = #{u.customerIndexNumber,jdbcType=VARCHAR},
      </if>
      <if test="u.entrustAmount != null">
        entrust_amount = #{u.entrustAmount,jdbcType=DECIMAL},
      </if>
      <if test="u.entrustTotalAmount != null">
        entrust_total_amount = #{u.entrustTotalAmount,jdbcType=DECIMAL},
      </if>
      <if test="u.entrustPrincipal != null">
        entrust_principal = #{u.entrustPrincipal,jdbcType=DECIMAL},
      </if>
      <if test="u.entrustPrincipalTotal != null">
        entrust_principal_total = #{u.entrustPrincipalTotal,jdbcType=DECIMAL},
      </if>
      <if test="u.entrustOverduePeriod != null">
        entrust_overdue_period = #{u.entrustOverduePeriod,jdbcType=VARCHAR},
      </if>
      <if test="u.targetPeriod != null">
        target_period = #{u.targetPeriod,jdbcType=VARCHAR},
      </if>
      <if test="u.caseCity != null">
        case_city = #{u.caseCity,jdbcType=VARCHAR},
      </if>
      <if test="u.lastFollowUpDate != null">
        last_follow_up_date = #{u.lastFollowUpDate,jdbcType=DATE},
      </if>
      <if test="u.entrustStartDate != null">
        entrust_start_date = #{u.entrustStartDate,jdbcType=DATE},
      </if>
      <if test="u.entrustEndDate != null">
        entrust_end_date = #{u.entrustEndDate,jdbcType=DATE},
      </if>
      <if test="u.caseType != null">
        case_type = #{u.caseType,jdbcType=VARCHAR},
      </if>
      <if test="u.newOldCaseFlag != null">
        new_old_case_flag = #{u.newOldCaseFlag,jdbcType=CHAR},
      </if>
      <if test="u.batchNumber != null">
        batch_number = #{u.batchNumber,jdbcType=VARCHAR},
      </if>
      <if test="u.balanceOps != null">
        balance_ops = #{u.balanceOps,jdbcType=DECIMAL},
      </if>
      <if test="u.principalOps != null">
        principal_ops = #{u.principalOps,jdbcType=DECIMAL},
      </if>
      <if test="u.currentOverduePeriod != null">
        current_overdue_period = #{u.currentOverduePeriod,jdbcType=VARCHAR},
      </if>
      <if test="u.householdCity != null">
        household_city = #{u.householdCity,jdbcType=VARCHAR},
      </if>
      <if test="u.customerAge != null">
        customer_age = #{u.customerAge,jdbcType=INTEGER},
      </if>
      <if test="u.occupation != null">
        occupation = #{u.occupation,jdbcType=VARCHAR},
      </if>
      <if test="u.cardOpeningDate != null">
        card_opening_date = #{u.cardOpeningDate,jdbcType=DATE},
      </if>
      <if test="u.creditLimit != null">
        credit_limit = #{u.creditLimit,jdbcType=DECIMAL},
      </if>
      <if test="u.lastPaymentDate != null">
        last_payment_date = #{u.lastPaymentDate,jdbcType=DATE},
      </if>
      <if test="u.skillGroup != null">
        skill_group = #{u.skillGroup,jdbcType=VARCHAR},
      </if>
      <if test="u.accountNumberLast7 != null">
        account_number_last7 = #{u.accountNumberLast7,jdbcType=CHAR},
      </if>
      <if test="u.entrustAgeBracket != null">
        entrust_age_bracket = #{u.entrustAgeBracket,jdbcType=VARCHAR},
      </if>
      <if test="u.entrustAmountBracket != null">
        entrust_amount_bracket = #{u.entrustAmountBracket,jdbcType=VARCHAR},
      </if>
      <if test="u.entrustCommissionBracket != null">
        entrust_commission_bracket = #{u.entrustCommissionBracket,jdbcType=VARCHAR},
      </if>
      <if test="u.ratingBracket != null">
        rating_bracket = #{u.ratingBracket,jdbcType=VARCHAR},
      </if>
      <if test="u.isLitigation != null">
        is_litigation = #{u.isLitigation,jdbcType=CHAR},
      </if>
      <if test="u.customerCurrentMonthRepayment != null">
        customer_current_month_repayment = #{u.customerCurrentMonthRepayment,jdbcType=DECIMAL},
      </if>
      <if test="u.customerPreviousDayRepayment != null">
        customer_previous_day_repayment = #{u.customerPreviousDayRepayment,jdbcType=DECIMAL},
      </if>
      <if test="u.caseGrabbingUser != null">
        case_grabbing_user = #{u.caseGrabbingUser,jdbcType=VARCHAR},
      </if>
      <if test="u.specialProjectType != null">
        special_project_type = #{u.specialProjectType,jdbcType=VARCHAR},
      </if>
      <if test="u.retentionCount != null">
        retention_count = #{u.retentionCount,jdbcType=INTEGER},
      </if>
      <if test="u.personalizedInstallmentStatus != null">
        personalized_installment_status = #{u.personalizedInstallmentStatus,jdbcType=VARCHAR},
      </if>
      <if test="u.personalizedInstallmentFulfillmentStatus != null">
        personalized_installment_fulfillment_status = #{u.personalizedInstallmentFulfillmentStatus,jdbcType=VARCHAR},
      </if>
      <if test="u.complaintLabel != null">
        complaint_label = #{u.complaintLabel,jdbcType=VARCHAR},
      </if>
      <if test="u.personalizedFulfillmentLabel != null">
        personalized_fulfillment_label = #{u.personalizedFulfillmentLabel,jdbcType=VARCHAR},
      </if>
      <if test="u.litigationLabel != null">
        litigation_label = #{u.litigationLabel,jdbcType=VARCHAR},
      </if>
      <if test="u.smartVoiceLabel != null">
        smart_voice_label = #{u.smartVoiceLabel,jdbcType=VARCHAR},
      </if>
      <if test="u.specialProjectLabel != null">
        special_project_label = #{u.specialProjectLabel,jdbcType=VARCHAR},
      </if>
      <if test="u.isLitigationForCommission != null">
        is_litigation_for_commission = #{u.isLitigationForCommission,jdbcType=CHAR},
      </if>
      <if test="u.caseAgency != null">
        case_agency = #{u.caseAgency,jdbcType=VARCHAR},
      </if>
      <if test="u.followUpStatus != null">
        follow_up_status = #{u.followUpStatus,jdbcType=CHAR},
      </if>
    </set>
  		where customer_index_number = #{u.customerIndexNumber,jdbcType=VARCHAR}
  	</foreach>
  </update>
  
  
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Nov 20 11:23:04 HKT 2024.
    -->
    update cc_user
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.customerName != null">
        customer_name = #{row.customerName,jdbcType=VARCHAR},
      </if>
      <if test="row.cardholderIdNumber != null">
        cardholder_id_number = #{row.cardholderIdNumber,jdbcType=VARCHAR},
      </if>
      <if test="row.cardholderCode != null">
        cardholder_code = #{row.cardholderCode,jdbcType=VARCHAR},
      </if>
      <if test="row.customerIndexNumber != null">
        customer_index_number = #{row.customerIndexNumber,jdbcType=VARCHAR},
      </if>
      <if test="row.entrustAmount != null">
        entrust_amount = #{row.entrustAmount,jdbcType=DECIMAL},
      </if>
      <if test="row.entrustTotalAmount != null">
        entrust_total_amount = #{row.entrustTotalAmount,jdbcType=DECIMAL},
      </if>
      <if test="row.entrustPrincipal != null">
        entrust_principal = #{row.entrustPrincipal,jdbcType=DECIMAL},
      </if>
      <if test="row.entrustPrincipalTotal != null">
        entrust_principal_total = #{row.entrustPrincipalTotal,jdbcType=DECIMAL},
      </if>
      <if test="row.entrustOverduePeriod != null">
        entrust_overdue_period = #{row.entrustOverduePeriod,jdbcType=VARCHAR},
      </if>
      <if test="row.targetPeriod != null">
        target_period = #{row.targetPeriod,jdbcType=VARCHAR},
      </if>
      <if test="row.caseCity != null">
        case_city = #{row.caseCity,jdbcType=VARCHAR},
      </if>
      <if test="row.lastFollowUpDate != null">
        last_follow_up_date = #{row.lastFollowUpDate,jdbcType=DATE},
      </if>
      <if test="row.entrustStartDate != null">
        entrust_start_date = #{row.entrustStartDate,jdbcType=DATE},
      </if>
      <if test="row.entrustEndDate != null">
        entrust_end_date = #{row.entrustEndDate,jdbcType=DATE},
      </if>
      <if test="row.caseType != null">
        case_type = #{row.caseType,jdbcType=VARCHAR},
      </if>
      <if test="row.newOldCaseFlag != null">
        new_old_case_flag = #{row.newOldCaseFlag,jdbcType=CHAR},
      </if>
      <if test="row.batchNumber != null">
        batch_number = #{row.batchNumber,jdbcType=VARCHAR},
      </if>
      <if test="row.balanceOps != null">
        balance_ops = #{row.balanceOps,jdbcType=DECIMAL},
      </if>
      <if test="row.principalOps != null">
        principal_ops = #{row.principalOps,jdbcType=DECIMAL},
      </if>
      <if test="row.currentOverduePeriod != null">
        current_overdue_period = #{row.currentOverduePeriod,jdbcType=VARCHAR},
      </if>
      <if test="row.householdCity != null">
        household_city = #{row.householdCity,jdbcType=VARCHAR},
      </if>
      <if test="row.customerAge != null">
        customer_age = #{row.customerAge,jdbcType=INTEGER},
      </if>
      <if test="row.occupation != null">
        occupation = #{row.occupation,jdbcType=VARCHAR},
      </if>
      <if test="row.cardOpeningDate != null">
        card_opening_date = #{row.cardOpeningDate,jdbcType=DATE},
      </if>
      <if test="row.creditLimit != null">
        credit_limit = #{row.creditLimit,jdbcType=DECIMAL},
      </if>
      <if test="row.lastPaymentDate != null">
        last_payment_date = #{row.lastPaymentDate,jdbcType=DATE},
      </if>
      <if test="row.skillGroup != null">
        skill_group = #{row.skillGroup,jdbcType=VARCHAR},
      </if>
      <if test="row.accountNumberLast7 != null">
        account_number_last7 = #{row.accountNumberLast7,jdbcType=CHAR},
      </if>
      <if test="row.entrustAgeBracket != null">
        entrust_age_bracket = #{row.entrustAgeBracket,jdbcType=VARCHAR},
      </if>
      <if test="row.entrustAmountBracket != null">
        entrust_amount_bracket = #{row.entrustAmountBracket,jdbcType=VARCHAR},
      </if>
      <if test="row.entrustCommissionBracket != null">
        entrust_commission_bracket = #{row.entrustCommissionBracket,jdbcType=VARCHAR},
      </if>
      <if test="row.ratingBracket != null">
        rating_bracket = #{row.ratingBracket,jdbcType=VARCHAR},
      </if>
      <if test="row.isLitigation != null">
        is_litigation = #{row.isLitigation,jdbcType=CHAR},
      </if>
      <if test="row.customerCurrentMonthRepayment != null">
        customer_current_month_repayment = #{row.customerCurrentMonthRepayment,jdbcType=DECIMAL},
      </if>
      <if test="row.customerPreviousDayRepayment != null">
        customer_previous_day_repayment = #{row.customerPreviousDayRepayment,jdbcType=DECIMAL},
      </if>
      <if test="row.caseGrabbingUser != null">
        case_grabbing_user = #{row.caseGrabbingUser,jdbcType=VARCHAR},
      </if>
      <if test="row.specialProjectType != null">
        special_project_type = #{row.specialProjectType,jdbcType=VARCHAR},
      </if>
      <if test="row.retentionCount != null">
        retention_count = #{row.retentionCount,jdbcType=INTEGER},
      </if>
      <if test="row.personalizedInstallmentStatus != null">
        personalized_installment_status = #{row.personalizedInstallmentStatus,jdbcType=VARCHAR},
      </if>
      <if test="row.personalizedInstallmentFulfillmentStatus != null">
        personalized_installment_fulfillment_status = #{row.personalizedInstallmentFulfillmentStatus,jdbcType=VARCHAR},
      </if>
      <if test="row.complaintLabel != null">
        complaint_label = #{row.complaintLabel,jdbcType=VARCHAR},
      </if>
      <if test="row.personalizedFulfillmentLabel != null">
        personalized_fulfillment_label = #{row.personalizedFulfillmentLabel,jdbcType=VARCHAR},
      </if>
      <if test="row.litigationLabel != null">
        litigation_label = #{row.litigationLabel,jdbcType=VARCHAR},
      </if>
      <if test="row.smartVoiceLabel != null">
        smart_voice_label = #{row.smartVoiceLabel,jdbcType=VARCHAR},
      </if>
      <if test="row.specialProjectLabel != null">
        special_project_label = #{row.specialProjectLabel,jdbcType=VARCHAR},
      </if>
      <if test="row.isLitigationForCommission != null">
        is_litigation_for_commission = #{row.isLitigationForCommission,jdbcType=CHAR},
      </if>
      <if test="row.caseAgency != null">
        case_agency = #{row.caseAgency,jdbcType=VARCHAR},
      </if>
      <if test="row.followUpStatus != null">
        follow_up_status = #{row.followUpStatus,jdbcType=CHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Nov 20 11:23:04 HKT 2024.
    -->
    update cc_user
    set id = #{row.id,jdbcType=BIGINT},
      customer_name = #{row.customerName,jdbcType=VARCHAR},
      cardholder_id_number = #{row.cardholderIdNumber,jdbcType=VARCHAR},
      cardholder_code = #{row.cardholderCode,jdbcType=VARCHAR},
      customer_index_number = #{row.customerIndexNumber,jdbcType=VARCHAR},
      entrust_amount = #{row.entrustAmount,jdbcType=DECIMAL},
      entrust_total_amount = #{row.entrustTotalAmount,jdbcType=DECIMAL},
      entrust_principal = #{row.entrustPrincipal,jdbcType=DECIMAL},
      entrust_principal_total = #{row.entrustPrincipalTotal,jdbcType=DECIMAL},
      entrust_overdue_period = #{row.entrustOverduePeriod,jdbcType=VARCHAR},
      target_period = #{row.targetPeriod,jdbcType=VARCHAR},
      case_city = #{row.caseCity,jdbcType=VARCHAR},
      last_follow_up_date = #{row.lastFollowUpDate,jdbcType=DATE},
      entrust_start_date = #{row.entrustStartDate,jdbcType=DATE},
      entrust_end_date = #{row.entrustEndDate,jdbcType=DATE},
      case_type = #{row.caseType,jdbcType=VARCHAR},
      new_old_case_flag = #{row.newOldCaseFlag,jdbcType=CHAR},
      batch_number = #{row.batchNumber,jdbcType=VARCHAR},
      balance_ops = #{row.balanceOps,jdbcType=DECIMAL},
      principal_ops = #{row.principalOps,jdbcType=DECIMAL},
      current_overdue_period = #{row.currentOverduePeriod,jdbcType=VARCHAR},
      household_city = #{row.householdCity,jdbcType=VARCHAR},
      customer_age = #{row.customerAge,jdbcType=INTEGER},
      occupation = #{row.occupation,jdbcType=VARCHAR},
      card_opening_date = #{row.cardOpeningDate,jdbcType=DATE},
      credit_limit = #{row.creditLimit,jdbcType=DECIMAL},
      last_payment_date = #{row.lastPaymentDate,jdbcType=DATE},
      skill_group = #{row.skillGroup,jdbcType=VARCHAR},
      account_number_last7 = #{row.accountNumberLast7,jdbcType=CHAR},
      entrust_age_bracket = #{row.entrustAgeBracket,jdbcType=VARCHAR},
      entrust_amount_bracket = #{row.entrustAmountBracket,jdbcType=VARCHAR},
      entrust_commission_bracket = #{row.entrustCommissionBracket,jdbcType=VARCHAR},
      rating_bracket = #{row.ratingBracket,jdbcType=VARCHAR},
      is_litigation = #{row.isLitigation,jdbcType=CHAR},
      customer_current_month_repayment = #{row.customerCurrentMonthRepayment,jdbcType=DECIMAL},
      customer_previous_day_repayment = #{row.customerPreviousDayRepayment,jdbcType=DECIMAL},
      case_grabbing_user = #{row.caseGrabbingUser,jdbcType=VARCHAR},
      special_project_type = #{row.specialProjectType,jdbcType=VARCHAR},
      retention_count = #{row.retentionCount,jdbcType=INTEGER},
      personalized_installment_status = #{row.personalizedInstallmentStatus,jdbcType=VARCHAR},
      personalized_installment_fulfillment_status = #{row.personalizedInstallmentFulfillmentStatus,jdbcType=VARCHAR},
      complaint_label = #{row.complaintLabel,jdbcType=VARCHAR},
      personalized_fulfillment_label = #{row.personalizedFulfillmentLabel,jdbcType=VARCHAR},
      litigation_label = #{row.litigationLabel,jdbcType=VARCHAR},
      smart_voice_label = #{row.smartVoiceLabel,jdbcType=VARCHAR},
      special_project_label = #{row.specialProjectLabel,jdbcType=VARCHAR},
      is_litigation_for_commission = #{row.isLitigationForCommission,jdbcType=CHAR},
      case_agency = #{row.caseAgency,jdbcType=VARCHAR},
      follow_up_status = #{row.followUpStatus,jdbcType=CHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.linhong.boot.management.model.entity.CcUser">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Nov 20 11:23:04 HKT 2024.
    -->
    update cc_user
    <set>
      <if test="customerName != null">
        customer_name = #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="cardholderIdNumber != null">
        cardholder_id_number = #{cardholderIdNumber,jdbcType=VARCHAR},
      </if>
      <if test="cardholderCode != null">
        cardholder_code = #{cardholderCode,jdbcType=VARCHAR},
      </if>
      <if test="customerIndexNumber != null">
        customer_index_number = #{customerIndexNumber,jdbcType=VARCHAR},
      </if>
      <if test="entrustAmount != null">
        entrust_amount = #{entrustAmount,jdbcType=DECIMAL},
      </if>
      <if test="entrustTotalAmount != null">
        entrust_total_amount = #{entrustTotalAmount,jdbcType=DECIMAL},
      </if>
      <if test="entrustPrincipal != null">
        entrust_principal = #{entrustPrincipal,jdbcType=DECIMAL},
      </if>
      <if test="entrustPrincipalTotal != null">
        entrust_principal_total = #{entrustPrincipalTotal,jdbcType=DECIMAL},
      </if>
      <if test="entrustOverduePeriod != null">
        entrust_overdue_period = #{entrustOverduePeriod,jdbcType=VARCHAR},
      </if>
      <if test="targetPeriod != null">
        target_period = #{targetPeriod,jdbcType=VARCHAR},
      </if>
      <if test="caseCity != null">
        case_city = #{caseCity,jdbcType=VARCHAR},
      </if>
      <if test="lastFollowUpDate != null">
        last_follow_up_date = #{lastFollowUpDate,jdbcType=DATE},
      </if>
      <if test="entrustStartDate != null">
        entrust_start_date = #{entrustStartDate,jdbcType=DATE},
      </if>
      <if test="entrustEndDate != null">
        entrust_end_date = #{entrustEndDate,jdbcType=DATE},
      </if>
      <if test="caseType != null">
        case_type = #{caseType,jdbcType=VARCHAR},
      </if>
      <if test="newOldCaseFlag != null">
        new_old_case_flag = #{newOldCaseFlag,jdbcType=CHAR},
      </if>
      <if test="batchNumber != null">
        batch_number = #{batchNumber,jdbcType=VARCHAR},
      </if>
      <if test="balanceOps != null">
        balance_ops = #{balanceOps,jdbcType=DECIMAL},
      </if>
      <if test="principalOps != null">
        principal_ops = #{principalOps,jdbcType=DECIMAL},
      </if>
      <if test="currentOverduePeriod != null">
        current_overdue_period = #{currentOverduePeriod,jdbcType=VARCHAR},
      </if>
      <if test="householdCity != null">
        household_city = #{householdCity,jdbcType=VARCHAR},
      </if>
      <if test="customerAge != null">
        customer_age = #{customerAge,jdbcType=INTEGER},
      </if>
      <if test="occupation != null">
        occupation = #{occupation,jdbcType=VARCHAR},
      </if>
      <if test="cardOpeningDate != null">
        card_opening_date = #{cardOpeningDate,jdbcType=DATE},
      </if>
      <if test="creditLimit != null">
        credit_limit = #{creditLimit,jdbcType=DECIMAL},
      </if>
      <if test="lastPaymentDate != null">
        last_payment_date = #{lastPaymentDate,jdbcType=DATE},
      </if>
      <if test="skillGroup != null">
        skill_group = #{skillGroup,jdbcType=VARCHAR},
      </if>
      <if test="accountNumberLast7 != null">
        account_number_last7 = #{accountNumberLast7,jdbcType=CHAR},
      </if>
      <if test="entrustAgeBracket != null">
        entrust_age_bracket = #{entrustAgeBracket,jdbcType=VARCHAR},
      </if>
      <if test="entrustAmountBracket != null">
        entrust_amount_bracket = #{entrustAmountBracket,jdbcType=VARCHAR},
      </if>
      <if test="entrustCommissionBracket != null">
        entrust_commission_bracket = #{entrustCommissionBracket,jdbcType=VARCHAR},
      </if>
      <if test="ratingBracket != null">
        rating_bracket = #{ratingBracket,jdbcType=VARCHAR},
      </if>
      <if test="isLitigation != null">
        is_litigation = #{isLitigation,jdbcType=CHAR},
      </if>
      <if test="customerCurrentMonthRepayment != null">
        customer_current_month_repayment = #{customerCurrentMonthRepayment,jdbcType=DECIMAL},
      </if>
      <if test="customerPreviousDayRepayment != null">
        customer_previous_day_repayment = #{customerPreviousDayRepayment,jdbcType=DECIMAL},
      </if>
      <if test="caseGrabbingUser != null">
        case_grabbing_user = #{caseGrabbingUser,jdbcType=VARCHAR},
      </if>
      <if test="specialProjectType != null">
        special_project_type = #{specialProjectType,jdbcType=VARCHAR},
      </if>
      <if test="retentionCount != null">
        retention_count = #{retentionCount,jdbcType=INTEGER},
      </if>
      <if test="personalizedInstallmentStatus != null">
        personalized_installment_status = #{personalizedInstallmentStatus,jdbcType=VARCHAR},
      </if>
      <if test="personalizedInstallmentFulfillmentStatus != null">
        personalized_installment_fulfillment_status = #{personalizedInstallmentFulfillmentStatus,jdbcType=VARCHAR},
      </if>
      <if test="complaintLabel != null">
        complaint_label = #{complaintLabel,jdbcType=VARCHAR},
      </if>
      <if test="personalizedFulfillmentLabel != null">
        personalized_fulfillment_label = #{personalizedFulfillmentLabel,jdbcType=VARCHAR},
      </if>
      <if test="litigationLabel != null">
        litigation_label = #{litigationLabel,jdbcType=VARCHAR},
      </if>
      <if test="smartVoiceLabel != null">
        smart_voice_label = #{smartVoiceLabel,jdbcType=VARCHAR},
      </if>
      <if test="specialProjectLabel != null">
        special_project_label = #{specialProjectLabel,jdbcType=VARCHAR},
      </if>
      <if test="isLitigationForCommission != null">
        is_litigation_for_commission = #{isLitigationForCommission,jdbcType=CHAR},
      </if>
      <if test="caseAgency != null">
        case_agency = #{caseAgency,jdbcType=VARCHAR},
      </if>
      <if test="followUpStatus != null">
        follow_up_status = #{followUpStatus,jdbcType=CHAR},
      </if>
      <if test="workUnit != null">
        work_unit = #{workUnit,jdbcType=VARCHAR},
      </if>
      <if test="bankCardNumber != null">
        bank_card_number = #{bankCardNumber,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.linhong.boot.management.model.entity.CcUser">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Nov 20 11:23:04 HKT 2024.
    -->
    update cc_user
    set customer_name = #{customerName,jdbcType=VARCHAR},
      cardholder_id_number = #{cardholderIdNumber,jdbcType=VARCHAR},
      cardholder_code = #{cardholderCode,jdbcType=VARCHAR},
      customer_index_number = #{customerIndexNumber,jdbcType=VARCHAR},
      entrust_amount = #{entrustAmount,jdbcType=DECIMAL},
      entrust_total_amount = #{entrustTotalAmount,jdbcType=DECIMAL},
      entrust_principal = #{entrustPrincipal,jdbcType=DECIMAL},
      entrust_principal_total = #{entrustPrincipalTotal,jdbcType=DECIMAL},
      entrust_overdue_period = #{entrustOverduePeriod,jdbcType=VARCHAR},
      target_period = #{targetPeriod,jdbcType=VARCHAR},
      case_city = #{caseCity,jdbcType=VARCHAR},
      last_follow_up_date = #{lastFollowUpDate,jdbcType=DATE},
      entrust_start_date = #{entrustStartDate,jdbcType=DATE},
      entrust_end_date = #{entrustEndDate,jdbcType=DATE},
      case_type = #{caseType,jdbcType=VARCHAR},
      new_old_case_flag = #{newOldCaseFlag,jdbcType=CHAR},
      batch_number = #{batchNumber,jdbcType=VARCHAR},
      balance_ops = #{balanceOps,jdbcType=DECIMAL},
      principal_ops = #{principalOps,jdbcType=DECIMAL},
      current_overdue_period = #{currentOverduePeriod,jdbcType=VARCHAR},
      household_city = #{householdCity,jdbcType=VARCHAR},
      customer_age = #{customerAge,jdbcType=INTEGER},
      occupation = #{occupation,jdbcType=VARCHAR},
      card_opening_date = #{cardOpeningDate,jdbcType=DATE},
      credit_limit = #{creditLimit,jdbcType=DECIMAL},
      last_payment_date = #{lastPaymentDate,jdbcType=DATE},
      skill_group = #{skillGroup,jdbcType=VARCHAR},
      account_number_last7 = #{accountNumberLast7,jdbcType=CHAR},
      entrust_age_bracket = #{entrustAgeBracket,jdbcType=VARCHAR},
      entrust_amount_bracket = #{entrustAmountBracket,jdbcType=VARCHAR},
      entrust_commission_bracket = #{entrustCommissionBracket,jdbcType=VARCHAR},
      rating_bracket = #{ratingBracket,jdbcType=VARCHAR},
      is_litigation = #{isLitigation,jdbcType=CHAR},
      customer_current_month_repayment = #{customerCurrentMonthRepayment,jdbcType=DECIMAL},
      customer_previous_day_repayment = #{customerPreviousDayRepayment,jdbcType=DECIMAL},
      case_grabbing_user = #{caseGrabbingUser,jdbcType=VARCHAR},
      special_project_type = #{specialProjectType,jdbcType=VARCHAR},
      retention_count = #{retentionCount,jdbcType=INTEGER},
      personalized_installment_status = #{personalizedInstallmentStatus,jdbcType=VARCHAR},
      personalized_installment_fulfillment_status = #{personalizedInstallmentFulfillmentStatus,jdbcType=VARCHAR},
      complaint_label = #{complaintLabel,jdbcType=VARCHAR},
      personalized_fulfillment_label = #{personalizedFulfillmentLabel,jdbcType=VARCHAR},
      litigation_label = #{litigationLabel,jdbcType=VARCHAR},
      smart_voice_label = #{smartVoiceLabel,jdbcType=VARCHAR},
      special_project_label = #{specialProjectLabel,jdbcType=VARCHAR},
      is_litigation_for_commission = #{isLitigationForCommission,jdbcType=CHAR},
      case_agency = #{caseAgency,jdbcType=VARCHAR},
      follow_up_status = #{followUpStatus,jdbcType=CHAR}
      work_unit = #{workUnit,jdbcType=VARCHAR}
      bank_card_number = #{bankCardNumber,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>