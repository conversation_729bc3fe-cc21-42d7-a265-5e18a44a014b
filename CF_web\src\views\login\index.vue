<template>
  <div class="login">
    <!-- 登录页头部 -->
    <div class="login-header">
      <div class="header-left">
        <div class="logo-section">
          <img v-if="pageConfig.logo" :src="pageConfig.logo" alt="Logo" class="logo-img" />
          <div v-else class="logo">LOGO</div>
          <h1 class="system-title">{{ pageConfig.systemTitle }}</h1>
        </div>
      </div>
      <div class="header-right">
        <!-- <el-switch
          v-model="isDark"
          inline-prompt
          active-icon="Moon"
          inactive-icon="Sunny"
          @change="toggleTheme"
        /> -->
        <!-- <lang-select class="ml-2 cursor-pointer" /> -->
      </div>
    </div>

    <!-- 登录页内容 -->
    <div class="login-content">
      <!-- 企业文化区域 -->
      <div class="culture-section">
        <div class="culture-title">{{ pageConfig.cultureTitle }}</div>
        <div
          v-for="(item, index) in pageConfig.cultureItems"
          :key="index"
          class="culture-item"
        >
          <span class="icon">{{ item.icon }}</span>
          <span class="text">{{ item.content }}</span>
        </div>
      </div>

      <div class="login-form">
        <el-form ref="loginFormRef" :model="loginData" :rules="loginRules">
          <div class="form-title">
            <h2>用户登录</h2>
            <!-- <el-dropdown style="position: absolute; right: 0">
              <div class="cursor-pointer">
                <el-icon>
                  <arrow-down />
                </el-icon>
              </div>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item>
                    版本号：
                    <el-tag type="success">
                      {{ defaultSettings.version }}
                    </el-tag>
                  </el-dropdown-item>

                  <el-dropdown-item @click="setLoginCredentials('root', '123456')">
                    超级管理员：root/123456
                  </el-dropdown-item>
                  <el-dropdown-item @click="setLoginCredentials('admin', '123456')">
                    系统管理员：admin/123456
                  </el-dropdown-item>
                  <el-dropdown-item @click="setLoginCredentials('test', '123456')">
                    测试小游客：test/123456
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
</el-dropdown> -->
          </div>

          <!-- 用户名 -->
          <el-form-item prop="username">
            <div class="input-wrapper">
              <el-icon class="mx-2">
                <User />
              </el-icon>
              <el-input
                ref="username"
                v-model="loginData.username"
                :placeholder="$t('login.username')"
                name="username"
                size="large"
                class="h-[48px]"
              />
            </div>
          </el-form-item>

          <!-- 密码 -->
          <el-form-item prop="password">
            <el-tooltip :visible="isCapslock" :content="$t('login.capsLock')" placement="right" class="w-full">
              <div class="input-wrapper">
                <el-icon class="mx-2">
                  <Lock />
                </el-icon>
                <el-input
                  v-model="loginData.password"
                  :placeholder="$t('login.password')"
                  type="password"
                  name="password"
                  size="large"
                  class="h-[48px]"
                  show-password
                  @keyup="checkCapslock"
                  @keyup.enter="handleLoginSubmit"
                />
              </div>
            </el-tooltip>
          </el-form-item>

          <!-- 验证码 -->
          <el-form-item prop="captchaCode">
            <div class="input-wrapper">
              <svg-icon icon-class="captcha" class="mx-2" />
              <el-input
                v-model="loginData.captchaCode"
                auto-complete="off"
                size="large"
                class="flex-1 h-[48px]"
                :placeholder="$t('login.captchaCode')"
                @keyup.enter="handleLoginSubmit"
              />

              <el-image :src="captchaBase64" class="captcha-img" @click="getCaptcha" />
            </div>
          </el-form-item>

          <div class="flex-x-between w-full py-1">
            <el-checkbox>
              {{ $t("login.rememberMe") }}
            </el-checkbox>

            <el-link type="primary" @click="showForgetPasswordDialog = true">
              忘记密码
            </el-link>
          </div>

          <!-- 登录按钮 -->
          <el-button
            :loading="loading"
            type="primary"
            size="large"
            class="w-full login-button"
            @click.prevent="handleLoginSubmit"
          >
            立即登录
          </el-button>

          <!-- 第三方登录 -->
          <!-- <el-divider>
            <el-text size="small">{{ $t("login.otherLoginMethods") }}</el-text>
          </el-divider>
          <div class="third-party-login">
            <svg-icon icon-class="wechat" class="icon" />
            <svg-icon icon-class="qq" class="icon" />
            <svg-icon icon-class="github" class="icon" />
            <svg-icon icon-class="gitee" class="icon" />
          </div> -->
        </el-form>
      </div>
    </div>

    <!-- 登录页底部 -->
    <div class="login-footer">
      <div class="footer-nav">
        <div
          v-for="(item, index) in pageConfig.catchword"
          :key="index"
          class="nav-item"
          :class="{ active: item.active }"
          @click="handleNavClick(index)"
        >
          {{ item.text }}
        </div>
      </div>
    </div>

    <!-- 忘记密码弹框 -->
    <el-dialog
      v-model="showForgetPasswordDialog"
      title="忘记密码"
      width="420px"
      :before-close="handleCloseForgetDialog"
      class="forget-password-dialog"
    >
      <div class="forget-dialog-content">
        <p class="forget-message">
          请输入您的用户名，我们将向人事部门发送重置密码请求
        </p>
        <el-form ref="forgetFormRef" :model="forgetForm" :rules="forgetRules">
          <el-form-item prop="username">
            <el-input
              v-model="forgetForm.username"
              placeholder="请输入用户名"
              size="large"
              clearable
            />
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCloseForgetDialog">取消</el-button>
          <el-button
            type="primary"
            :loading="forgetLoading"
          :disabled="forgetLoading"
            @click="handleSubmitForgetPassword"
          >
            发送请求
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { LocationQuery, useRoute } from "vue-router";

import AuthAPI, { type LoginData } from "@/api/auth";
import { getLoginPageConfig } from "@/api/system/login-config";
import ForgetPasswordAPI from "@/api/system/forget-password";
import router from "@/router";
import type { FormInstance } from "element-plus";
import { ElMessage, ElMessageBox } from "element-plus";

import defaultSettings from "@/settings";
// import { ThemeEnum } from "@/enums/ThemeEnum";

import { useUserStore, useDictStore, dataSystemStore } from "@/store";
import { useI18n } from "vue-i18n";

const userStore = useUserStore();

// const settingsStore = useSettingsStore();
const dictStore = useDictStore();

const route = useRoute();
const { t } = useI18n();
const loginFormRef = ref<FormInstance>();

// const isDark = ref(settingsStore.theme === ThemeEnum.DARK); // 是否暗黑模式
const loading = ref(false); // 按钮 loading 状态
const isCapslock = ref(false); // 是否大写锁定
const captchaBase64 = ref(); // 验证码图片Base64字符串

// 忘记密码相关
const showForgetPasswordDialog = ref(false);
const forgetFormRef = ref<FormInstance>();
const forgetLoading = ref(false);
const forgetSubmitted = ref(false); // 防止重复提交标志
const forgetForm = ref({
  username: ""
});

const forgetRules = {
  username: [
    { required: true, message: "请输入用户名", trigger: "blur" },
    { min: 2, message: "用户名至少2个字符", trigger: "blur" }
  ]
};
// 动态配置数据
const pageConfig = ref({
  logo: new URL("../../assets/logo.png", import.meta.url).href,
  systemTitle: "CF-作业系统",
  cultureTitle: "企业文化",
  cultureItems: [] as Array<{icon: string, content: string}>,
  catchword: [
    { text: '做大', active: true },
    { text: '做强', active: true },
    { text: '做尊敬', active: true },
    { text: '做永恒', active: true }
  ]
});

// 默认配置（加载失败时使用）
const defaultConfig = {
  logo: new URL("../../assets/logo.png", import.meta.url).href,
  systemTitle: 'CF-作业系统',
  cultureTitle: '企业文化',
  cultureItems: [
    { icon: "😊", content: "托底人生，共筑未来!" },
    { icon: "😊", content: "新百年基业，成万千大众!" },
    { icon: "�", content: "万千大众共生日，便民县业永同时!" },
    { icon: "�", content: "做大、做强、做优秀、做永恒!" },
    { icon: "⭐", content: "不受谈股权国家，而是通过为客户创造价值，推动行业健康发展，不辜负社会所托，以实际行动诠释企业的社会责任。" }
  ],
  catchword: [
    { text: '做大', active: true },
    { text: '做强', active: true },
    { text: '做尊敬', active: true },
    { text: '做永恒', active: true }
  ]
};
const loginImage = ref(new URL("../../assets/images/login-image.svg", import.meta.url).href);

const loginData = ref<LoginData>({
  // username: "admin",
  // password: "123456",
  username: "",
  password: "",
  captchaKey: "",
  captchaCode: "",
});

const loginRules = computed(() => {
  return {
    username: [
      {
        required: true,
        trigger: "blur",
        message: t("login.message.username.required"),
      },
    ],
    password: [
      {
        required: true,
        trigger: "blur",
        message: t("login.message.password.required"),
      },
      {
        min: 6,
        message: t("login.message.password.min"),
        trigger: "blur",
      },
    ],
    captchaCode: [
      {
        required: true,
        trigger: "blur",
        message: t("login.message.captchaCode.required"),
      },
    ],
  };
});

// 获取验证码
function getCaptcha() {
  AuthAPI.getCaptcha().then((data) => {
    loginData.value.captchaKey = data.captchaKey;
    captchaBase64.value = data.captchaBase64;
  });
}

// 登录
async function handleLoginSubmit() {
  loginFormRef.value?.validate((valid: boolean) => {
    if (valid) {
      loading.value = true;
      userStore
        .login(loginData.value)
        .then(async () => {
          await userStore.getUserInfo();
          await dataSystemStore().getArchitectureInfo();

          // 需要在路由跳转前加载字典数据，否则会出现字典数据未加载完成导致页面渲染异常
          await dictStore.loadDictionaries();
          // 跳转到登录前的页面
          const { path, queryParams } = parseRedirect();
          // router.push({ path: path, query: queryParams });
          // router.replace({ path: "/" });
          window.location.reload();
        })
        .catch(() => {
          getCaptcha();
        })
        .finally(() => {
          loading.value = false;
        });
    }
  });
}

/**
 * 解析 redirect 字符串 为 path 和  queryParams
 *
 * @returns { path: string, queryParams: Record<string, string> } 解析后的 path 和 queryParams
 */
function parseRedirect(): {
  path: string;
  queryParams: Record<string, string>;
} {
  const query: LocationQuery = route.query;
  const redirect = (query.redirect as string) ?? "/";

  const url = new URL(redirect, window.location.origin);
  const path = url.pathname;
  const queryParams: Record<string, string> = {};

  url.searchParams.forEach((value, key) => {
    queryParams[key] = value;
  });

  return { path, queryParams };
}

// 主题切换
// const toggleTheme = () => {
//   const newTheme = settingsStore.theme === ThemeEnum.DARK ? ThemeEnum.LIGHT : ThemeEnum.DARK;
//   settingsStore.changeTheme(newTheme);
// };

// 检查输入大小写
function checkCapslock(event: KeyboardEvent) {
  // 防止浏览器密码自动填充时报错
  if (event instanceof KeyboardEvent) {
    isCapslock.value = event.getModifierState("CapsLock");
  }
}

// 设置登录凭证
const setLoginCredentials = (username: string, password: string) => {
  loginData.value.username = username;
  loginData.value.password = password;
};

// 处理底部导航点击
const handleNavClick = (index: number) => {
  pageConfig.value.catchword.forEach((item, i) => {
    item.active = i === index;
  });
};

// 加载页面配置
const loadPageConfig = async () => {
  try {
    console.log("开始加载登录页配置...");
    const response = await getLoginPageConfig();
    console.log("登录页配置加载成功:", response);

    // 根据实际的响应结构处理数据
    const data = (response as any).data || response;

    pageConfig.value = {
      ...pageConfig.value,
      logo: data.logo || defaultConfig.logo,
      systemTitle: data.systemTitle || defaultConfig.systemTitle,
      cultureTitle: data.cultureTitle || defaultConfig.cultureTitle,
      cultureItems: data.cultureItems || defaultConfig.cultureItems,
    };
  } catch (error) {
    console.warn("加载登录页配置失败，使用默认配置:", error);
    useDefaultConfig();
  }
};

// 使用默认配置
const useDefaultConfig = () => {
  pageConfig.value = {
    ...pageConfig.value,
    logo: defaultConfig.logo,
    systemTitle: defaultConfig.systemTitle,
    cultureTitle: defaultConfig.cultureTitle,
    cultureItems: defaultConfig.cultureItems,
  };
};

// 关闭忘记密码弹框
const handleCloseForgetDialog = () => {
  showForgetPasswordDialog.value = false;
  forgetForm.value.username = "";
  forgetSubmitted.value = false; // 重置提交标志
  forgetFormRef.value?.resetFields();
};

// 提交忘记密码请求
const handleSubmitForgetPassword = async () => {
  if (!forgetFormRef.value || forgetLoading.value || forgetSubmitted.value) return;

  try {
    const valid = await forgetFormRef.value.validate();
    if (!valid) return;

    forgetLoading.value = true;
    forgetSubmitted.value = true; // 设置提交标志

    // 发送忘记密码请求
    await ForgetPasswordAPI.sendForgetPasswordRequest(forgetForm.value.username.trim());
    ElMessage.success("重置密码请求已发送，请联系人事部门处理");
    handleCloseForgetDialog();
  } catch (error: any) {
    console.error("忘记密码请求失败:", error);
    // request.ts 的响应拦截器已经处理了业务错误并显示了消息
    // 这里不需要再显示错误消息，避免重复
    forgetSubmitted.value = false; // 失败时重置标志，允许重试
  } finally {
    forgetLoading.value = false;
  }
};

onMounted(() => {
  getCaptcha();
  loadPageConfig();
});
</script>

<style lang="scss" scoped>
.login {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  width: 100%;
  height: 100%;
  overflow-y: auto;
  background: linear-gradient(135deg, #0f172a 0%, #1e3a8a 50%, #3730a3 100%);

  .login-header {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    width: 100%;
    height: 80px;
    padding: 0 80px;
    z-index: 10;

    .header-left {
      display: flex;
      align-items: center;
    }

    .logo-section {
      display: flex;
      align-items: center;
      gap: 20px;
    }

    .logo {
      background: rgba(255, 255, 255, 0.15);
      color: white;
      padding: 12px 20px;
      border-radius: 12px;
      font-size: 16px;
      font-weight: bold;
      border: 1px solid rgba(255, 255, 255, 0.2);
      backdrop-filter: blur(10px);
    }

    .logo-img {
      height: 48px;
      width: auto;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .system-title {
      color: #fbbf24;
      font-size: 32px;
      font-weight: bold;
      margin: 0;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
      letter-spacing: 2px;
    }

    .header-right {
      display: flex;
      align-items: center;
      margin-left: auto;
    }
  }

  .login-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    max-width: 1600px;
    height: calc(100vh - 160px); /* 减去头部和底部的高度 */
    margin: 80px auto 0; /* 居中并为头部留出空间 */
    padding: 0 80px;
    gap: 200px; /* 增加企业文化和登录框之间的间距 */

    @media (max-width: 1400px) {
      gap: 180px;
      padding: 0 60px;
    }

    @media (max-width: 1200px) {
      gap: 150px;
      padding: 0 40px;
    }

    @media (max-width: 1024px) {
      flex-direction: column;
      justify-content: center;
      padding: 0 40px;
      gap: 120px; /* 移动端也增加间距 */
      height: calc(100vh - 120px);
    }

    @media (max-width: 768px) {
      padding: 0 20px;
      gap: 80px;
      height: calc(100vh - 100px);
    }

    .culture-section {
      flex: 1;
      max-width: 550px;
      color: white;
      display: flex;
      flex-direction: column;
      justify-content: center;
      padding: 20px;

      @media (max-width: 1024px) {
        max-width: 100%;
        text-align: center;
        padding: 0;
      }

      .culture-title {
        font-size: 36px;
        font-weight: bold;
        margin-bottom: 50px;
        border-left: 6px solid #fbbf24;
        padding-left: 24px;
        letter-spacing: 2px;

        @media (max-width: 1024px) {
          border-left: none;
          border-bottom: 4px solid #fbbf24;
          padding-left: 0;
          padding-bottom: 12px;
          display: inline-block;
          font-size: 32px;
        }

        @media (max-width: 768px) {
          font-size: 28px;
          margin-bottom: 30px;
          letter-spacing: 1px;
        }
      }

      .culture-item {
        display: flex;
        align-items: center;
        margin-bottom: 32px;
        font-size: 22px;
        line-height: 1.8;
        padding: 8px 0;

        @media (max-width: 1024px) {
          justify-content: center;
          text-align: left;
          max-width: 600px;
          margin: 0 auto 28px;
        }

        @media (max-width: 768px) {
          font-size: 19px;
          margin-bottom: 24px;
          line-height: 1.7;
        }

        .icon {
          font-size: 26px;
          margin-right: 16px;
          flex-shrink: 0;

          @media (max-width: 768px) {
            font-size: 20px;
            margin-right: 12px;
          }
        }

        .text {
          flex: 1;
          font-weight: 400;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }

    .login-form {
      width: 360px;
      max-width: 360px;
      padding: 28px;
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(12px);
      border-radius: 16px;
      box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
      border: 1px solid rgba(255, 255, 255, 0.2);
      display: flex;
      flex-direction: column;
      justify-content: center;

      @media (width <=768px) {
        width: 100%;
        max-width: 100%;
        padding: 28px;
        min-height: auto;
      }

      .form-title {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 0 24px;
        text-align: center;

        h2 {
          color: white;
          font-size: 20px;
          font-weight: bold;
          margin: 0;
        }
      }

      :deep(.el-form-item) {
        margin-bottom: 20px;

        .el-form-item__content {
          line-height: normal;
        }
      }

      :deep(.el-tooltip) {
        width: 100%;
        display: block;
      }

      .input-wrapper {
        display: flex;
        align-items: center;
        width: 100%;
        margin-bottom: 8px;

        :deep(.el-input) {
          width: 100%;
        }

        :deep(.el-input__wrapper) {
          background-color: rgba(255, 255, 255, 0.1);
          border: 1px solid rgba(255, 255, 255, 0.2);
          border-radius: 8px;
          padding: 12px 14px;
          min-height: 44px;
          display: flex;
          align-items: center;

          &:hover {
            border-color: rgba(255, 255, 255, 0.3);
            background-color: rgba(255, 255, 255, 0.15);
          }

          &.is-focus {
            border-color: rgba(255, 215, 0, 0.5);
            background-color: rgba(255, 255, 255, 0.15);
            box-shadow: 0 0 0 2px rgba(255, 215, 0, 0.2);
          }
        }

        :deep(.el-input__inner) {
          color: white;
          font-size: 15px;
          line-height: 1.5;
          background: transparent;

          &::placeholder {
            color: rgba(255, 255, 255, 0.4);
          }
        }

        :deep(.el-input__prefix-inner) {
          color: rgba(255, 255, 255, 0.5);
          margin-right: 12px;
        }

        :deep(.el-input__suffix-inner) {
          color: rgba(255, 255, 255, 0.7);
          margin-left: 12px;
        }

        .mx-2 {
          color: rgba(255, 255, 255, 0.7);
          margin: 0 6px;
          font-size: 14px;
        }
      }

      .captcha-img {
        height: 44px;
        width: 110px;
        cursor: pointer;
        border-radius: 8px;
        margin-left: 12px;
        background-color: rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
        transition: all 0.3s ease;

        &:hover {
          background-color: rgba(255, 255, 255, 0.3);
        }
      }

      .third-party-login {
        display: flex;
        justify-content: center;
        width: 100%;
        color: var(--el-text-color-secondary);

        *:not(:first-child) {
          margin-left: 20px;
        }

        .icon {
          cursor: pointer;
        }
      }

      .login-button {
        background-color: #ffd700 !important;
        border-color: #ffd700 !important;
        color: #002b5c !important;
        font-weight: 600 !important;
        height: 44px !important;
        font-size: 15px !important;
        border-radius: 8px !important;
        margin-top: 16px;
        width: 100%;
        transition: all 0.3s ease;

        &:hover {
          background-color: rgba(255, 215, 0, 0.9) !important;
          border-color: rgba(255, 215, 0, 0.9) !important;
          transform: translateY(-2px);
        }

        &:active {
          transform: translateY(0);
        }

        &:focus {
          background-color: #ffd700 !important;
          border-color: #fbbf24 !important;
        }

        &:active {
          transform: translateY(0);
        }
      }

      :deep(.el-checkbox) {
        .el-checkbox__label {
          color: rgba(255, 255, 255, 0.7);
          font-size: 14px;
        }

        .el-checkbox__input.is-checked .el-checkbox__inner {
          background-color: #ffd700;
          border-color: #ffd700;
        }

        .el-checkbox__inner {
          border-color: rgba(255, 255, 255, 0.2);
          background-color: rgba(255, 255, 255, 0.1);

          &:hover {
            border-color: #ffd700;
          }
        }
      }

      .flex-x-between {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: 16px 0;
        padding: 0;

        :deep(.el-link) {
          color: rgba(255, 255, 255, 0.7) !important;
          font-size: 14px;

          &:hover {
            color: white !important;
          }
        }
      }
    }
  }

  .login-footer {
    position: absolute;
    bottom: 40px;
    width: 100%;
    display: flex;
    justify-content: center;

    @media (max-width: 768px) {
      bottom: 20px;
    }

    .footer-nav {
      display: flex;
      gap: 80px;

      @media (max-width: 768px) {
        gap: 40px;
      }

      @media (max-width: 480px) {
        gap: 20px;
        flex-wrap: wrap;
        justify-content: center;
      }

      .nav-item {
        color: #fbbf24;
        font-size: 28px;
        font-weight: bold;
        cursor: pointer;
        transition: all 0.3s ease;

        @media (max-width: 768px) {
          font-size: 24px;
        }

        @media (max-width: 480px) {
          font-size: 20px;
        }

        &:hover {
          color: #f59e0b;
          transform: scale(1.05);
        }

        &.active {
          color: #fbbf24;
          text-shadow: 0 0 10px rgba(251, 191, 36, 0.5);
        }
      }
    }
  }
}

/* 忘记密码弹框样式 */
:deep(.forget-password-dialog) {
  .el-dialog {
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    overflow: hidden;
  }

  .el-dialog__header {
    padding: 24px 24px 16px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    margin: 0;

    .el-dialog__title {
      font-size: 18px;
      font-weight: 600;
      color: white;
    }

    .el-dialog__headerbtn {
      .el-dialog__close {
        color: white;
        font-size: 18px;

        &:hover {
          color: #f0f0f0;
        }
      }
    }
  }

  .el-dialog__body {
    padding: 24px;

    .forget-dialog-content {
      .forget-message {
        font-size: 14px;
        color: #606266;
        line-height: 1.6;
        margin-bottom: 20px;
        text-align: center;
      }

      .el-form-item {
        margin-bottom: 0;

        .el-input {
          .el-input__wrapper {
            border-radius: 8px;
            border: 2px solid #e4e7ed;
            transition: all 0.3s ease;

            &:hover {
              border-color: #c0c4cc;
            }

            &.is-focus {
              border-color: #409eff;
              box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
            }
          }

          .el-input__inner {
            height: 44px;
            font-size: 14px;
            padding: 0 16px;
          }
        }
      }
    }
  }

  .el-dialog__footer {
    padding: 16px 24px 24px;
    background: #f8f9fa;

    .dialog-footer {
      text-align: center;

      .el-button {
        margin: 0 8px;
        padding: 10px 24px;
        font-size: 14px;
        border-radius: 8px;
        font-weight: 500;

        &.el-button--primary {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          border: none;

          &:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
          }
        }

        &.el-button--default {
          border: 1px solid #dcdfe6;
          color: #606266;

          &:hover {
            color: #409eff;
            border-color: #c6e2ff;
            background-color: #ecf5ff;
          }
        }
      }
    }
  }
}
</style>
