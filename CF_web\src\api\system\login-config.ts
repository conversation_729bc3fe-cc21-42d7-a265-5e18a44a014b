import request from '@/utils/request'

// 登录页配置相关接口

/**
 * 登录页配置数据类型
 */
export interface LoginPageConfig {
  logo: string
  systemTitle: string
  cultureTitle: string
  cultureItems: CultureItem[]
}

export interface CultureItem {
  icon: string
  content: string
}

export interface BasicConfigDTO {
  systemTitle: string
  cultureTitle: string
  logo: string
}

export interface LoginConfigEditVO {
  basicConfig: BasicConfigDTO
  cultureItems: CultureItem[]
}

/**
 * 获取登录页配置（公开接口，前端登录页调用）
 */
export function getLoginPageConfig() {
  return request<LoginPageConfig>({
    url: '/api/login/config/all',
    method: 'get'
  })
}

/**
 * 获取可编辑的登录页配置（管理后台调用）
 */
export function getEditableConfig() {
  return request<LoginConfigEditVO>({
    url: '/api/v1/login-config',
    method: 'get'
  })
}

/**
 * 更新基础配置
 */
export function updateBasicConfig(data: BasicConfigDTO) {
  return request({
    url: '/api/v1/login-config/basic',
    method: 'put',
    data
  })
}

/**
 * 更新企业文化配置
 */
export function updateCultureConfig(data: CultureItem[]) {
  return request({
    url: '/api/v1/login-config/culture',
    method: 'put',
    data
  })
}

/**
 * 上传LOGO的URL（用于el-upload组件的action属性）
 */
export const uploadLogoUrl = import.meta.env.VITE_APP_BASE_API + "/api/v1/login-config/upload/logo"

/**
 * 上传LOGO
 */
export function uploadLogo(file: File) {
  const formData = new FormData()
  formData.append('file', file)
  
  return request<string>({
    url: '/api/v1/login-config/upload/logo',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 恢复默认配置
 */
export function resetToDefault() {
  return request({
    url: '/api/v1/login-config/reset',
    method: 'post'
  })
}
