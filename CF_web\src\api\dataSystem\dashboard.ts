import request from "@/utils/request";

const dashboardAPI = {
  /**
   * 获取仪表板统计卡片数据
   */
  getStatisticCards() {
    return request<any, StatisticCard[]>({
      url: "/api/v1/dashboard/statistic-cards",
      method: "get",
    });
  },

  /**
   * 获取案池户数统计
   */
  getCasepoolCount() {
    return request<any, PageResult<any>>({
      url: "/api/v1/casepool/page",
      method: "get",
      params: {
        pageNum: 1,
        pageSize: 999999
      }
    });
  },

  /**
   * 获取作业清单户数统计
   */
  getWorkPageCount(userOrDept?: string) {
    return request<any, PageResult<any>>({
      url: "/api/v1/casepool/workPage",
      method: "get",
      params: {
        pageNum: 1,
        pageSize: 999999,
        userOrDept: userOrDept
      }
    });
  },

  /**
   * 获取总案件数统计（案池 + 作业清单）
   */
  async getTotalCaseCount(userOrDept?: string): Promise<TotalCaseCountResult> {
    try {
      const [casepoolResponse, workPageResponse] = await Promise.all([
        this.getCasepoolCount(),
        this.getWorkPageCount(userOrDept)
      ]);

      const casepoolCount = casepoolResponse.total || 0;
      const workPageCount = workPageResponse.total || 0;
      const totalCount = casepoolCount + workPageCount;

      return {
        casepoolCount,
        workPageCount,
        totalCount,
        success: true
      };
    } catch (error) {
      console.error("获取总案件数失败:", error);
      return {
        casepoolCount: 0,
        workPageCount: 0,
        totalCount: 0,
        success: false,
        error: error as Error
      };
    }
  },

  /**
   * 获取总委托金额统计（案池 + 作业清单）
   */
  async getTotalEntrustAmount(userOrDept?: string): Promise<TotalEntrustAmountResult> {
    try {
      const [casepoolResponse, workPageResponse] = await Promise.all([
        this.getCasepoolCount(),
        this.getWorkPageCount(userOrDept)
      ]);

      // 从API响应中获取委托金额总和，确保转换为数字
      const casepoolAmount = (casepoolResponse.list && casepoolResponse.list.length > 0)
        ? parseFloat(casepoolResponse.list[0].sumEntrustTotalAmount || 0)
        : 0;

      const workPageAmount = (workPageResponse.list && workPageResponse.list.length > 0)
        ? parseFloat(workPageResponse.list[0].sumEntrustTotalAmount || 0)
        : 0;

      const totalAmount = casepoolAmount + workPageAmount;

      return {
        casepoolAmount,
        workPageAmount,
        totalAmount,
        success: true
      };
    } catch (error) {
      console.error("获取总委托金额失败:", error);
      return {
        casepoolAmount: 0,
        workPageAmount: 0,
        totalAmount: 0,
        success: false,
        error: error as Error
      };
    }
  },

  /**
   * 获取还款数据统计
   */
  getRepaymentData(operatorId?: string) {
    return request<any, PageResult<any>>({
      url: "/api/v1/casepool/repaymentPage",
      method: "get",
      params: {
        pageNum: 1,
        pageSize: 999999,
        operatorId: operatorId
      }
    });
  },

  /**
   * 获取总还款金额统计
   */
  async getTotalRepaymentAmount(operatorId?: string): Promise<TotalRepaymentAmountResult> {
    try {
      const repaymentResponse = await this.getRepaymentData(operatorId);

      // 从API响应中获取还款金额总和，字段名为 sumRepaymentAmount
      const totalRepaymentAmount = (repaymentResponse.list && repaymentResponse.list.length > 0)
        ? parseFloat(repaymentResponse.list[0].sumRepaymentAmount || 0)
        : 0;

      return {
        totalRepaymentAmount,
        success: true
      };
    } catch (error) {
      console.error("获取总还款金额失败:", error);
      return {
        totalRepaymentAmount: 0,
        success: false,
        error: error as Error
      };
    }
  },

  /**
   * 获取员工排行榜
   */
  getEmployeeRanking(queryParams?: { period?: 'month' | 'quarter' }) {
    return request<any, RankingItem[]>({
      url: "/api/v1/dashboard/employee-ranking",
      method: "get",
      params: queryParams,
    });
  },

  /**
   * 获取团队级别分布
   */
  getTeamLevelDistribution() {
    return request<any, TeamLevelItem[]>({
      url: "/api/v1/dashboard/team-level-distribution",
      method: "get",
    });
  },

  /**
   * 获取回款趋势数据
   */
  getRepaymentTrend(queryParams: {
    startDate?: string;
    endDate?: string;
    period?: 'amount' | 'count' | 'recent';
    operatorId?: string;
  }) {
    return request<any, RepaymentTrendData>({
      url: "/api/v1/dashboard/repayment-trend",
      method: "get",
      params: queryParams,
    });
  },

  /**
   * 获取案件分布数据
   */
  getCaseDistribution(type: 'department' | 'personal' = 'department') {
    return request<any, CaseDistributionItem[]>({
      url: "/api/v1/dashboard/case-distribution",
      method: "get",
      params: { type },
    });
  },
};

export default dashboardAPI;

// 数据类型定义
export interface StatisticCard {
  title: string;
  value: number | string;
  unit?: string;
  icon: string;
  color: string;
  trend?: {
    value: number;
    type: 'up' | 'down';
  };
}

export interface RankingItem {
  rank: number;
  userName: string;
  deptName: string;
  amount: number;
  avatar?: string;
  recoveryRate?: number;
  entrustTotalAmount?: number;
}

export interface TeamLevelItem {
  level: string;
  count: number;
  percentage: number;
  color: string;
}

export interface ChartData {
  xAxis?: string[];
  dates?: string[];
  series: {
    name: string;
    data: number[];
    type?: string;
    color?: string;
  }[];
}

export interface CaseDistributionItem {
  category: string;
  value: number;
  amount?: number;
  progress?: number;
  color: string;
}

export interface TotalCaseCountResult {
  casepoolCount: number;
  workPageCount: number;
  totalCount: number;
  success: boolean;
  error?: Error;
}

export interface TotalEntrustAmountResult {
  casepoolAmount: number;
  workPageAmount: number;
  totalAmount: number;
  success: boolean;
  error?: Error;
}

export interface TotalRepaymentAmountResult {
  totalRepaymentAmount: number;
  success: boolean;
  error?: Error;
}

export interface RepaymentTrendData {
  xAxis?: string[];  // 前端使用的字段名
  xaxis?: string[];  // 后端返回的字段名
  dates?: string[];
  series: {
    name: string;
    data: number[];
    type: string;
    color?: string;
  }[];
}

export interface RepaymentTrendItem {
  date: string;
  amount: number;
  count: number;
}
