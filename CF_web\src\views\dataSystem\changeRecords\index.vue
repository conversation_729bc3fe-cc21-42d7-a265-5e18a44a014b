<template>
  <div class="app-container">
    <!-- 筛选条件卡片 -->
    <el-card shadow="never" class="changerecords-filter-card">
      <div class="filter-header">
        <div class="filter-title">
          <el-icon class="filter-icon">
            <Filter />
          </el-icon>
          <span class="title-text">接单记录筛选条件</span>
        </div>
        <el-divider class="filter-divider" />
      </div>
      <eleForm
        id="eleForm"
        ref="eleFormRef"
        :formValue="screenValue"
        :formItemList="eleFormAllList"
        :formBox="{
          spanItem: 6,
          formContent: 24,
          operate: 24,
          operateItem: 24,
          operateGrid: 'end',
        }"
        label-position="left"
        label-width="80px"
        :operateOpen="true"
        defineTxt="搜索"
        cancelTxt="重置"
        @handleSubmit="handleQuery"
        @handleCancel="handleReset"
      />
    </el-card>

    <!-- 统计卡片区域 -->
    <div class="statistics-container">
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-number">{{ total }}</div>
            <div class="stat-label">筛选户数</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-number">{{ changeOutCount }}</div>
            <div class="stat-label">换出</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-number">{{ changeInCount }}</div>
            <div class="stat-label">换入</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-number">¥{{ formatAmount(sumEntrustTotalAmount) }}</div>
            <div class="stat-label">涉及金额</div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 数据表格区域 -->
    <el-card shadow="never" class="table-card">
      <el-table
        ref="dataTableRef"
        v-loading="loading"
        :data="roleList"
        highlight-current-row
        border
        style="width: 100%"
        :height="contentHeader"
        :row-class-name="tableRowClassName"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" :selectable="checkSelectable" width="55" align="center" />
        <el-table-column
          v-for="item in jobListFields"
          :key="item.prop"
          :fixed="item.fixed || false"
          :label="item.label"
          :prop="item.prop"
          min-width="120"
          :width="item.width || ''"
          sortable
        >
          <template v-if="item.prop == 'customerIndexNumber'" #default="scope">
            <el-button type="primary" size="small" link @click="handleOpenDialog(scope.row.id)">
              {{ scope.row.customerIndexNumber || "——" }}
            </el-button>
          </template>
          <template v-else-if="item.prop === 'customerName'" #default="scope">
            <span class="customer-name">{{ scope.row.customerName || "——" }}</span>
          </template>
          <template v-else-if="item.prop === 'huanType'" #default="scope">
            <span
              :class="[
                'status-tag',
                getTransferTypeClass(scope.row.huanType)
              ]"
            >
              <i :class="getTransferTypeIcon(scope.row.huanType)"></i>
              {{ scope.row.huanType || "——" }}
            </span>
          </template>
          <template v-else-if="item.option" #default="scope">
            {{ item.option[scope.row[item.prop]] }}
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="100">
          <template #default="scope">
            <el-button
              type="primary"
              size="small"
              :icon="View"
              @click="handleOpenDialog(scope.row.id)"
            >
              查看
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="handleQuery"
      />
    </el-card>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: "ChangeRecords",
  inheritAttrs: false,
});
import eleForm from "@/components/EleComponents/ele-form.vue";
import { userOrDept, screenList, jobListFields } from "./fieldsSys";
import { ElMessage, ElMessageBox, ElButton, ElLoading } from "element-plus";
import { Warning, Document, View, Filter } from "@element-plus/icons-vue";
import RoleAPI, { RolePageVO, RoleForm, RolePageQuery } from "@/api/system/role";
import changeRecordsAPI from "@/api/dataSystem/changeRecords";
import { $add } from "@/utils/calculate";
import { removeEmptyValues } from "@/utils/commit";
import { useUserStore, useArchitectureStoreHook } from "@/store";
import { deriveExcel } from "@/utils/tools";

const router = useRouter();
const eleFormRef = ref(null);
const loading = ref(false);
const total = ref(0);

const eleFormAllList = ref([userOrDept, ...screenList]);
const architectureStore = useArchitectureStoreHook();
const userStore = useUserStore();
const queryParams = reactive<RolePageQuery>({
  pageNum: 1,
  pageSize: 10,
});

// 表格数据
const roleList = ref<[]>();

const dataTableRef = ref(null);
//筛选字段默认值
const screenValue: any = ref({
  userOrDept: [],
  // lostContactQueryResult: 1,
});
// 选中的角色
const ids = ref([]);
//选中的总金额;
const checkTotal = ref(0);
//总委托金额
const sumEntrustTotalAmount = ref(0);
//总委托本金额
const sumentrustPrincipalTotal = ref(0);
// 换进换出统计数据
const changeInCount = ref(0);
const changeOutCount = ref(0);

// 格式化金额显示
const formatAmount = (amount: number) => {
  return new Intl.NumberFormat("zh-CN").format(amount || 0);
};
watch(ids, (val) => {
  let arr = dataTableRef.value.getSelectionRows();
  //计算勾选总金额;
  checkTotal.value = arr.reduce((sum, e) => $add(sum, e.entrustTotalAmount || 0), 0);
});
//标志新案
const tableRowClassName = ({ row, rowIndex }: any) => {
  if (row.followUpStatus === "新案") {
    return "res-nova";
  }
  return "";
};
const checkSelectable = ({ row, rowIndex }: any) => {
  console.log(row);
  return row?.followUpStatus !== "继续跟进";
};
// 获取换进换出统计数据
function getChangeStatistics() {
  let obj: any = {};
  let search: any = (eleFormRef.value as any)?.getFormVlaue() || {};
  for (let key in search) {
    if (search[key]) {
      if (key == "userOrDept") search[key] = search[key][search[key].length - 1] || "";
      obj[key] = search[key];
    }
  }

  changeRecordsAPI
    .casepoolHdjlPage({
      pageNum: 1,
      pageSize: 9999,
      ...obj,
    })
    .then((data: any) => {
      if (data.list && data.list.length > 0) {
        // 筛选出换进和换出的个数
        // 换进的值是 1，换出的值是 2
        changeInCount.value = data.list.filter(
          (item: any) => item.huanType === 1 || item.huanType === "1" || item.huanType === "换进"
        ).length;
        changeOutCount.value = data.list.filter(
          (item: any) => item.huanType === 2 || item.huanType === "2" || item.huanType === "换出"
        ).length;

        // 调试信息：打印数据查看实际的huanType值
        console.log("换单数据总数:", data.list.length);
        console.log(
          "huanType值分布:",
          data.list.reduce((acc: any, item: any) => {
            const type = item.huanType;
            acc[type] = (acc[type] || 0) + 1;
            return acc;
          }, {})
        );
        console.log("换进数量:", changeInCount.value, "换出数量:", changeOutCount.value);
      } else {
        changeInCount.value = 0;
        changeOutCount.value = 0;
      }
    })
    .catch(() => {
      changeInCount.value = 0;
      changeOutCount.value = 0;
    });
}

// 查询
function handleQuery() {
  loading.value = true;
  let obj: any = {};
  sumEntrustTotalAmount.value = 0;
  let search: any = (eleFormRef.value as any)?.getFormVlaue() || {};
  for (let key in search) {
    if (search[key]) {
      if (key == "userOrDept") search[key] = search[key][search[key].length - 1] || "";
      obj[key] = search[key];
    }
  }

  // 同时获取换进换出统计数据
  getChangeStatistics();

  changeRecordsAPI
    .casepoolHdjlPage({
      ...queryParams,
      ...obj,
    })
    .then((data: any) => {
      roleList.value = data.list;
      total.value = data.total;

      if (data.list && data.list.length > 0) {
        sumEntrustTotalAmount.value = data.list[0].sumEntrustTotalAmount || 0;
        sumentrustPrincipalTotal.value = data.list[0].sumentrustPrincipalTotal || 0;
      }
    })
    .finally(() => {
      loading.value = false;
    });
}

// 重置筛选条件
function handleReset() {
  // 重置筛选条件到初始值
  const { userId, deptId, roles } = userStore.userInfo;
  if (deptId && roles[0] != "YG") {
    screenValue.value = { userOrDept: [deptId, userId] };
  } else {
    screenValue.value = { userOrDept: [userId] };
  }
  // 重新查询
  handleQuery();
}

// 详情
const handleOpenDialog = (id?: any) => {
  let search = removeEmptyValues(eleFormRef.value.getFormVlaue());
  if (search["userOrDept"])
    search["userOrDept"] = search["userOrDept"][search["userOrDept"].length - 1] || "";

  router.push({
    path: "/dataSystem/pool/poolDetails",
    query: {
      id,
      pagesType: 4,
      search: JSON.stringify({
        ...search,
        ...queryParams,
      }),
    },
  });
};

// 行复选框选中
function handleSelectionChange(selection: any) {
  ids.value = selection.map((item: any) => item.id);
}

// 获取换单类型的CSS类名
function getTransferTypeClass(type: string) {
  if (type === '换进') {
    return 'success';
  } else if (type === '换出') {
    return 'danger';
  }
  return '';
}

// 获取换单类型的图标
function getTransferTypeIcon(type: string) {
  if (type === '换进') {
    return 'fas fa-arrow-down';
  } else if (type === '换出') {
    return 'fas fa-arrow-up';
  }
  return '';
}

// 失联查询结果转换
const lostContactQueryResultName = computed(() => {
  return (type: any) => {
    const { options } = eleFormAllList.value.find((e: any) => e.label == "失联查询结果");
    let name = false;
    if (options && options.length > 0) {
      name = options.find((t: any) => t.value == type)?.label || "-";
    }
    return name;
  };
});
// 案件类型
const caseTypeName = computed(() => {
  return (type: any) => {
    const { options } = eleFormAllList.value.find((e: any) => e.label == "案件类型");
    let name = false;
    if (options && options.length > 0) {
      name = options.find((t: any) => t.value == type)?.label || "-";
    }
    return name;
  };
});
const initOptions = async () => {
  // casepoolKeyValueList("work_status");
  eleFormAllList.value.find((e: any) => e.field == "followUpStatus").options =
    await architectureStore.casepoolKeyValueList("work_status");
  eleFormAllList.value.find((e: any) => e.field == "lostContactQueryResult").options =
    await architectureStore.casepoolKeyValueList("lost_status");
};
/**
 * 获取表格内容可用高度
 * **/
const contentHeader = ref(0);

// 获取表格高度
const getTableHeight = () => {
  const appMainH = (document.querySelectorAll(".app-main")[0] as HTMLElement)?.offsetHeight;
  const searchHeight = (document.querySelectorAll("#eleForm")[0] as HTMLElement)?.offsetHeight;
  const statisticsHeight = (document.querySelectorAll(".statistics-container")[0] as HTMLElement)
    ?.offsetHeight;
  console.log(appMainH, searchHeight, statisticsHeight);
  // 调整高度计算，考虑新增的统计卡片区域
  contentHeader.value = appMainH - (searchHeight || 0) - (statisticsHeight || 0) - 200;
};
onUpdated(async () => {
  initOptions();
});
onMounted(async () => {
  const { userId, deptId, roles } = userStore.userInfo;
  if (deptId && roles[0] != "YG") {
    screenValue.value["userOrDept"] = [deptId, userId];
  } else {
    screenValue.value["userOrDept"] = [userId];
  }
  initOptions();

  setTimeout(() => {
    handleQuery();
    getTableHeight();
  }, 0);
});
</script>
<style scoped>
/* 接单记录筛选条件样式 - 与还款管理保持一致 */
.changerecords-filter-card {
  margin-bottom: 6px;
}

.changerecords-filter-card .el-card__body {
  padding: 20px;
}

.filter-header {
  margin-bottom: 16px;
}

.filter-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 12px;
}

.filter-icon {
  margin-right: 8px;
  color: #409eff;
  font-size: 18px;
}

.title-text {
  color: #2c3e50;
}

.filter-divider {
  margin: 0;
  border-color: #e4e7ed;
}

/* 接单记录筛选条件样式 - 强制label和输入框左右排列 */
.changerecords-filter-card :deep(.el-form-item) {
  display: flex !important;
  flex-direction: row !important;
  align-items: center !important;
  margin-bottom: 15px !important;
}

.changerecords-filter-card :deep(.el-form-item__label) {
  width: 80px !important;
  min-width: 80px !important;
  max-width: 80px !important;
  text-align: left !important;
  font-size: 12px !important;
  font-weight: 600 !important;
  color: #2c3e50 !important;
  white-space: nowrap !important;
  margin-right: 8px !important;
  margin-bottom: 0 !important;
  padding: 0 !important;
  line-height: 32px !important;
  position: relative !important;
}

/* 为没有星号的标签添加统一的左边距，确保对齐 */
.changerecords-filter-card :deep(.el-form-item__label):not(.is-required) {
  padding-left: 11px !important; /* 11px = 星号宽度 + 间距，确保与有星号的标签对齐 */
}

/* 确保有星号的标签正常显示 */
.changerecords-filter-card :deep(.el-form-item__label.is-required) {
  padding-left: 0 !important;
}

.changerecords-filter-card :deep(.el-form-item__content) {
  flex: 1 !important;
  max-width: none !important;
  margin-left: 0 !important;
  width: auto !important;
}

/* 使用与还款管理一致的简洁样式 */
.changerecords-filter-card :deep(.el-input__wrapper) {
  height: 32px !important;
  font-size: 12px !important;
  border: 1px solid #d1d5db !important;
  border-radius: 4px !important;
  background-color: #ffffff !important;
}

.changerecords-filter-card :deep(.el-select .el-input__wrapper) {
  height: 32px !important;
  font-size: 12px !important;
  border: 1px solid #d1d5db !important;
  border-radius: 4px !important;
  background-color: #ffffff !important;
}

.changerecords-filter-card :deep(.el-date-editor .el-input__wrapper) {
  height: 32px !important;
  font-size: 12px !important;
  border: 1px solid #d1d5db !important;
  border-radius: 4px !important;
  background-color: #ffffff !important;
}

.changerecords-filter-card :deep(.el-cascader .el-input__wrapper) {
  height: 32px !important;
  font-size: 12px !important;
  border: 1px solid #d1d5db !important;
  border-radius: 4px !important;
  background-color: #ffffff !important;
}

/* 操作按钮样式 - 与还款管理保持一致 */
.changerecords-filter-card :deep(.operate-box) {
  display: flex !important;
  gap: 12px !important;
  align-items: center !important;
  justify-content: flex-end !important;
  width: 100% !important;
  padding: 0 !important;
}

.changerecords-filter-card :deep(.operate-box .el-button) {
  height: 32px !important;
  font-size: 12px !important;
  padding: 8px 20px !important;
  border-radius: 4px !important;
  min-width: 70px !important;
  font-weight: 500 !important;
}

.changerecords-filter-card :deep(.operate-box .el-button--primary) {
  background-color: #409eff !important;
  border-color: #409eff !important;
  color: #ffffff !important;
}

.changerecords-filter-card :deep(.operate-box .el-button--default) {
  background-color: #ffffff !important;
  border-color: #dcdfe6 !important;
  color: #606266 !important;
}

/* 统计卡片容器 */
.statistics-container {
  margin-bottom: 16px;
}

.stat-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  padding: 10px 20px;  /* 上下padding减少，左右保持不变 */
  text-align: center;
  color: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
  height: 60px;  /* 设置固定高度，约为原来的一半 */
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-number {
  font-size: 20px;  /* 字体稍微调小以适应更小的高度 */
  font-weight: bold;
  margin-bottom: 4px;  /* 减少间距 */
  line-height: 1.2;
}

.stat-label {
  font-size: 12px;  /* 标签字体调小以适应更小的高度 */
  opacity: 0.9;
  line-height: 1.2;
}

/* 不同统计卡片的颜色 */
.stat-card:nth-child(1) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-card:nth-child(2) {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-card:nth-child(3) {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-card:nth-child(4) {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

/* 换单类型状态标签样式 - 参考HTML样式 */
.status-tag {
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 10px;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  gap: 2px;
}

.status-tag.success {
  background: #f0f9ff;
  color: #059669;
  border: 1px solid #a7f3d0;
}

.status-tag.danger {
  background: #fef2f2;
  color: #dc2626;
  border: 1px solid #fecaca;
}

.status-tag i {
  font-size: 8px;
}

/* 表格卡片 */
.table-card {
  border-radius: 8px;
}

.flex-between {
  display: flex;
  justify-content: space-between;
}

.flex-center {
  display: flex;
  justify-content: center;
}

.flex-start {
  display: flex;
  align-items: center;
}

.tips {
  span {
    margin-right: 2px;
  }

  text {
    font-weight: bold;
    color: red;
  }
}

.el-table .res-nova {
  --el-table-tr-bg-color: var(--el-color-danger-light-9);
}

/* 表格表头颜色和居中 */
:deep(.el-table th.el-table__cell) {
  background-color: #f2f3f5 !important;
  text-align: center !important;
}

/* 表格数据居中显示 */
:deep(.el-table td.el-table__cell) {
  text-align: center !important;
}

/* 客户姓名列蓝色字体 */
.customer-name {
  color: #409eff;
  font-weight: 500;
}
</style>
