package com.linhong.boot.management.controller;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.linhong.boot.common.annotation.Log;
import com.linhong.boot.common.annotation.RepeatSubmit;
import com.linhong.boot.common.enums.CaseEnum;
import com.linhong.boot.common.enums.LogModuleEnum;
import com.linhong.boot.common.enums.TransferRecordEnum;
import com.linhong.boot.common.result.PageResult;
import com.linhong.boot.common.result.Result;
import com.linhong.boot.core.security.util.SecurityUtils;
import com.linhong.boot.management.model.entity.CcUser;
import com.linhong.boot.management.model.entity.CcUserWork;
import com.linhong.boot.management.model.entity.FollowUpRecords;
import com.linhong.boot.management.model.entity.RepaymentRecords;
import com.linhong.boot.management.model.entity.TransferRecords;
import com.linhong.boot.management.model.query.RecordsQuery;
import com.linhong.boot.management.service.CasePoolService;
import com.linhong.boot.management.service.CcWorksService;
import com.linhong.boot.management.service.FollowUpRecordsService;
import com.linhong.boot.management.service.RepaymentRecordService;
import com.linhong.boot.management.service.TransferRecordsService;
import com.linhong.boot.system.model.entity.User;
import com.linhong.boot.system.model.vo.UserProfileVO;
import com.linhong.boot.system.service.UserService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;

@Tag(name = "3.跟进记录")
@RestController
@RequestMapping("/api/v1/followUpRecords")
@RequiredArgsConstructor
public class FollowUpRecordsController {
	
	
	private final FollowUpRecordsService recordsService;
	private final CasePoolService casePoolService;
	private final UserService userService;
	private final RepaymentRecordService repaymentRecordService;
	private final CcWorksService worksService;
	/* 分配记录 */
	private final TransferRecordsService transferRecordsService;
	
	@Operation(summary = "跟进列表")
	@GetMapping("/page")
	@Log(value = "查询跟进记录", module = LogModuleEnum.CASEPOOL)
	public PageResult<FollowUpRecords> getPoolPage(RecordsQuery query) {
		IPage<FollowUpRecords> result = recordsService.getRecordsPage(query);
		return PageResult.success(result);
	}
	
	@Operation(summary = "新增跟进记录")
	@PostMapping("/save")
	@RepeatSubmit
	@Log(value = "新增跟进记录", module = LogModuleEnum.CASEPOOL)
	@Transactional
	public Result<?> saveRrecord(@RequestBody FollowUpRecords records) {
		Long userId = records.getUserId(); // 案件id 不是 用户id
		/*案件状态*/
		String collectionResult = records.getCollectionResult();
		if(userId==null)
			return Result.failed("数据异常:(莫得案件ID)");
		CcUser casePool = casePoolService.getById(userId);
		if(casePool == null)
			return Result.failed("数据异常:(该案件不存在,id:"+userId+")");
		if(records.getRepaymentAmount()!=null && records.getRepaymentDate()==null) {
			return Result.failed("请填写还款日期");
		}
		
		List<String> followWhiteList = new ArrayList<>();
		followWhiteList.add(CaseEnum.CASE_POOL.getValue().toString());
		followWhiteList.add(CaseEnum.TUI_AN.getValue().toString());
		followWhiteList.add(CaseEnum.TUI_HUI.getValue().toString());
		if(followWhiteList.contains(casePool.getFollowUpStatus())) {
			return Result.failed("异常操作,案件已归属案池");
		}
		/*查询 当前操作员*/
		Long uId = SecurityUtils.getUserId();
		UserProfileVO u = userService.getUserProfile(uId);
		records.setOperationId(uId);
		records.setOperationName(u.getNickname()==null?"未知":u.getNickname()); 
		/*退回案池状态.解除工单,案池可查*/
		casePool.setFollowUpStatus(collectionResult); // 跟进状态
		if(collectionResult.equals(CaseEnum.TUI_HUI.getValue().toString())) {
			tuihuiAnchi(userId);
			/*保存分配记录*/
			String oldUserId = "案池";
			String oldUserDeptId = "案池";
			if(casePool.getUserOrDept()!=null) {
				User oldUser = userService.getBaseMapper().selectById(casePool.getUserOrDept());
				oldUserId = casePool.getUserOrDept();
				oldUserDeptId = oldUser.getDeptId();
			}
			
			TransferRecords transferRecords = transferRecordsService.saveTransFerRecord(
					userId,
					oldUserId,
					oldUserDeptId,
					"案池",
					"案池",
					uId.toString(),
					TransferRecordEnum.TUI_HUI_AN_CHI.getValue().toString()
			);
			transferRecordsService.getBaseMapper().insert(transferRecords);
			casePool.setUserOrDept("");
			casePool.setFollowUpStatus(CaseEnum.TUI_HUI.getValue().toString());
		}else {
			casePool.setLostContactQueryResult(records.getMissingContactResult()); //失联查询结果
			casePool.setLastFollowUpDate(new Date());
			/*只有继续跟进状态,可以决定是否换单*/
			casePool.setIsMarkChange(records.getIsMarkedForChange().equals("0")?"-1":"1"); //是否开启换单 -1:否  1:是
			
			/*新增还款记录*/
			if(records.getRepaymentAmount() != null && records.getRepaymentAmount().compareTo(BigDecimal.ZERO) > 0) {
				/* 还款金额大于0 , 新增还款记录 */
				RepaymentRecords repayment = new RepaymentRecords();
				repayment.setCcId(casePool.getId());
				repayment.setBatchNumber(casePool.getBatchNumber());
				repayment.setCardholderCode(casePool.getCardholderCode());
				repayment.setCaseType(casePool.getCaseType());
				repayment.setCustomerIndexNumber(casePool.getCustomerIndexNumber());
				repayment.setCustomerName(casePool.getCustomerName());
				repayment.setDeptName(u.getDeptName());
				repayment.setOperatorId(uId.toString());
				repayment.setRepaymentAmount(records.getRepaymentAmount());
				repayment.setRepaymentDate(records.getRepaymentDate());
				repayment.setOutsourceSerialNumber(casePool.getCaseAgency());
				repayment.setIsDelete("0");
				repaymentRecordService.save(repayment);
			}
		}
		
		recordsService.save(records);
		casePoolService.updateUserOrDept(casePool, userId);
		return Result.judge(true);
	}
	
	/**
	 * 跟进状态退回案池
	 */
	public void tuihuiAnchi(Long ccId) {
		LambdaQueryWrapper<CcUserWork> q = new LambdaQueryWrapper<>();
		q.eq(CcUserWork::getCcId, ccId);
		worksService.getBaseMapper().delete(q);
	}

	@Operation(summary = "跟进记录详情")
	@GetMapping("/detail")
	public Result<FollowUpRecords> recordDetail(@RequestParam Long id) {
		FollowUpRecords contacts = recordsService.getById(id);
		return Result.success(contacts);
	}

	@Operation(summary = "修改跟进记录")
	@PutMapping(value = "/modify")
	public Result<Void> emergencyModify(@RequestBody FollowUpRecords contacts) {
		if(contacts.getId()==null)
			return Result.failed("数据异常,跟进记录ID为空");
		boolean result = recordsService.saveOrUpdate(contacts);
		return Result.judge(result);
	}

	@Operation(summary = "删除跟进记录")
	@DeleteMapping("/delete")
	public Result<Void> emergencyDelete(@RequestParam Long id) {
		if(id == null)
			Result.failed("数据异常,跟进记录ID为空");
		boolean result = recordsService.removeById(id);
		return Result.judge(result);
	}
	
	@Operation(summary = "根据案件ID查询跟进记录")
	@GetMapping("/findByPoolId")
	public Result<List<FollowUpRecords>> findByPoolId(@RequestParam Long id) {
		if(id == null)
			Result.failed("查询数据异常,跟进记录ID为空");
		QueryWrapper<FollowUpRecords> emQueryWrapper = new QueryWrapper<>();
		emQueryWrapper.eq("user_id", id);
		List<FollowUpRecords> contacts = recordsService.getBaseMapper().selectList(emQueryWrapper);
		return Result.success(contacts);
	}
	
}
