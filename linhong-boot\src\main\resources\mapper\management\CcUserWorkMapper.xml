<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.linhong.boot.management.mapper.CcUserWorkMapper">
  <resultMap id="BaseResultMap" type="com.linhong.boot.management.model.entity.CcUserWork">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Nov 25 22:00:16 HKT 2024.
    -->
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="cc_id" jdbcType="BIGINT" property="ccId" />
    <result column="work_status" jdbcType="CHAR" property="workStatus" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Nov 25 22:00:16 HKT 2024.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Nov 25 22:00:16 HKT 2024.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Nov 25 22:00:16 HKT 2024.
    -->
    user_id, cc_id, work_status
  </sql>
  <select id="selectByExample" parameterType="com.linhong.boot.management.model.entity.CcUserWorkExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Nov 25 22:00:16 HKT 2024.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from cc_user_work
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.linhong.boot.management.model.entity.CcUserWorkExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Nov 25 22:00:16 HKT 2024.
    -->
    delete from cc_user_work
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.linhong.boot.management.model.entity.CcUserWork">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Nov 25 22:00:16 HKT 2024.
    -->
    insert into cc_user_work (user_id, cc_id, work_status
      )
    values (#{userId,jdbcType=BIGINT}, #{ccId,jdbcType=BIGINT}, #{workStatus,jdbcType=CHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.linhong.boot.management.model.entity.CcUserWork">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Nov 25 22:00:16 HKT 2024.
    -->
    insert into cc_user_work
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        user_id,
      </if>
      <if test="ccId != null">
        cc_id,
      </if>
      <if test="workStatus != null">
        work_status,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="ccId != null">
        #{ccId,jdbcType=BIGINT},
      </if>
      <if test="workStatus != null">
        #{workStatus,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.linhong.boot.management.model.entity.CcUserWorkExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Nov 25 22:00:16 HKT 2024.
    -->
    select count(*) from cc_user_work
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Nov 25 22:00:16 HKT 2024.
    -->
    update cc_user_work
    <set>
      <if test="row.userId != null">
        user_id = #{row.userId,jdbcType=BIGINT},
      </if>
      <if test="row.ccId != null">
        cc_id = #{row.ccId,jdbcType=BIGINT},
      </if>
      <if test="row.workStatus != null">
        work_status = #{row.workStatus,jdbcType=CHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Nov 25 22:00:16 HKT 2024.
    -->
    update cc_user_work
    set user_id = #{row.userId,jdbcType=BIGINT},
      cc_id = #{row.ccId,jdbcType=BIGINT},
      work_status = #{row.workStatus,jdbcType=CHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
</mapper>