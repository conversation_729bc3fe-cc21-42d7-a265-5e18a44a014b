package com.linhong.boot.management.service;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.linhong.boot.management.model.entity.FollowUpRecordsHistory;
import com.linhong.boot.management.model.query.RecordsQuery;

/**
 * 跟进功能接口
 */
public interface FollowUpRecordsHistoryService extends IService<FollowUpRecordsHistory>{

		
	/**
	 * 跟进分页查询
	 * @return
	 */
	public IPage<FollowUpRecordsHistory> getRecordsPage(RecordsQuery query);
	
	/**
	 * 批量新增历史记录
	 * @param historyRecords
	 * @return
	 */
	public boolean saveBatchHistory(List<FollowUpRecordsHistory> historyRecords);
	
}
