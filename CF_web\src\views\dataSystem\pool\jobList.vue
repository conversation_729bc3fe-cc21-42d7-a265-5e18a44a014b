<template>
  <div class="app-container job-list-container">
    <!-- 筛选条件卡片 -->
    <el-card shadow="never" class="job-filter-card">
      <div class="filter-header">
        <div class="filter-title">
          <el-icon class="filter-icon">
            <Filter />
          </el-icon>
          <span class="title-text">作业筛选条件</span>
        </div>
        <el-divider class="filter-divider" />
      </div>
      <eleForm
        id="eleForm"
        ref="eleFormRef"
        :formValue="screenValue"
        :formItemList="eleFormAllList"
        :formBox="{
          spanItem: 4.8,
          formContent: 24,
          operate: 24,
          operateItem: 24,
          operateGrid: 'end',
        }"
        label-position="left"
        label-width="80px"
        :operateOpen="true"
        defineTxt="搜索"
        cancelTxt="重置"
        @handleSubmit="handleQuery"
        @handleCancel="handleReset"
      />
    </el-card>

    <!-- 操作按钮区域 -->
    <div class="action-section">
      <div class="stats-info">
        <div class="tips flex-start">
          <el-icon size="16px" color="#ff0000">
            <Warning />
          </el-icon>
          <span class="ml-2">
            总条数：
            <text class="Danger">{{ total }}</text>
            ；
          </span>
          <span>
            总委托金额：
            <text>{{ sumEntrustTotalAmount }}</text>
            ；
          </span>
          <span>
            勾选条数：
            <text>{{ ids.length }}</text>
            ；
          </span>
          <span>
            勾选总金额：
            <text>{{ checkTotal }}</text>
            ；
          </span>
        </div>
      </div>

      <div class="action-buttons">
        <el-button
          type="success"
          icon="RefreshLeft"
          :disabled="ids.length === 0"
          @click="batchReturn"
        >
          批量退回
        </el-button>
        <el-button
          v-hasPerm="['sys:jobList:switch']"
          type="success"
          :disabled="ids.length === 0"
          icon="Refresh"
          @click="casepoolChangeFun(1)"
        >
          批量换单
        </el-button>
        <el-button
          type="success"
          icon="Position"
          :disabled="ids.length === 0"
          @click="automaticList"
        >
          任务分派
        </el-button>
        <el-button
          v-hasPerm="['sys:jobList:export']"
          type="primary"
          icon="Download"
          @click="exportShow = true"
        >
          导出
        </el-button>
        <el-button type="warning" icon="PriceTag" :disabled="ids.length === 0" @click="batchMark">
          批量标注
        </el-button>
      </div>
    </div>

    <!-- 数据表格卡片 -->
    <el-card shadow="never" class="table-card">
      <div class="data-header">
        <div class="data-title">
          <el-icon class="data-icon">
            <Grid />
          </el-icon>
          <span class="title-text">作业数据列表</span>
        </div>
        <el-divider class="data-divider" />
      </div>
      <el-table
        ref="dataTableRef"
        v-loading="loading"
        :data="roleList"
        highlight-current-row
        border
        style="width: 100%; flex: 1;"
        height="100%"
        :row-class-name="tableRowClassName"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" :selectable="checkSelectable" width="55" align="center" />
        <el-table-column
          v-for="item in jobListFields"
          :key="item.prop"
          :fixed="item.fixed || false"
          :label="item.label"
          :prop="item.prop"
          min-width="120"
          :width="item.width || ''"
          sortable
        >
          <template v-if="item.prop == 'customerIndexNumber'" #default="scope">
            <el-button type="primary" size="small" link @click="handleOpenDialog(scope.row.id)">
              {{ scope.row.customerIndexNumber || "——" }}
            </el-button>
          </template>
          <template v-if="item.prop == 'lostContactQueryResult'" #default="scope">
            {{ lostContactQueryResultName(scope.row.lostContactQueryResult) || "——" }}
          </template>
          <template v-if="item.prop == 'caseType'" #default="scope">
            {{ caseTypeName(scope.row.caseType) || "——" }}
          </template>
          <template v-else-if="item.prop == 'isLock'" #default="scope">
            <span
              :class="[
                'config-status-tag',
                getConfigStatusClass(scope.row.isLock)
              ]"
            >
              <i :class="getConfigStatusIcon(scope.row.isLock)"></i>
              {{ item.option[scope.row[item.prop]] || "——" }}
            </span>
          </template>
          <template v-else-if="item.option" #default="scope">
            {{ item.option[scope.row[item.prop]] }}
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="100">
          <template #default="scope">
            <el-button
              type="primary"
              size="small"
              link
              icon="user"
              @click="handleOpenDialog(scope.row.id)"
            >
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          :page-sizes="[10, 20, 50, 100, 200, 500]"
          @change="handleQuery"
          @pagination="handleQuery"
        />
      </div>
    </el-card>
    <!-- 分配案件 -->
    <allocation
      v-model:show="allocationShow"
      v-model:ids="allocationIds"
      :type="2"
      @refresh="handleQuery"
    />
    <!-- 导出 -->
    <el-dialog
      v-model="exportShow"
      title="导出案池"
      width="450"
      align-center
      @close="exportHandleCloseDialog"
    >
      <el-form ref="exportFormRef" :model="exportForm" label-width="100px">
        <el-row gutter="24">
          <el-col span="24">
            <el-form-item label="导出方式" prop="derivedValue">
              <el-segmented v-model="exportForm.derivedValue" :options="derivedOptions" />
            </el-form-item>
            <el-form-item label="是否退案" prop="isTuiAn">
              <el-switch v-model="exportForm.isTuiAn" />
            </el-form-item>
            <el-form-item label="退案文件名" prop="fileName">
              <el-input v-model="exportForm.fileName" placeholder="请输入文件名称" type="string" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" :loading="exportLoading" @click="exportHandleSubmit">
            确 定
          </el-button>
          <el-button :loading="exportLoading" @click="exportHandleCloseDialog">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 批量标注对话框 -->
    <el-dialog v-model="batchMarkShow" title="批量标注" width="500px" draggable>
      <el-form ref="batchMarkFormRef" :model="batchMarkForm" label-width="120px">
        <el-form-item
          label="跟进状态"
          prop="followUpStatus"
          :rules="[{ required: true, message: '请选择跟进状态', trigger: 'change' }]"
        >
          <el-select
            v-model="batchMarkForm.followUpStatus"
            placeholder="请选择跟进状态"
            style="width: 100%"
          >
            <el-option
              v-for="item in followUpStatusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <div class="batch-mark-info">
            <el-icon><InfoFilled /></el-icon>
            <span>将对选中的 {{ ids.length }} 个案件进行批量标注</span>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" :loading="batchMarkLoading" @click="batchMarkSubmit">
            确 定
          </el-button>
          <el-button :loading="batchMarkLoading" @click="batchMarkShow = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: "Pool",
  inheritAttrs: false,
});
import eleForm from "@/components/EleComponents/ele-form.vue";
import {
  userOrDept,
  jobScreenList,
  jobListFields,
  isMarkChange,
  createBatchNumberField,
} from "./fieldsSys";
import { ElMessage, ElMessageBox, ElButton, ElLoading } from "element-plus";
import {
  Filter,
  Grid,
  Warning,
  RefreshLeft,
  Download,
  PriceTag,
  InfoFilled,
} from "@element-plus/icons-vue";
import RoleAPI, { RolePageVO, RoleForm, RolePageQuery } from "@/api/system/role";
import dataSystemAPI from "@/api/dataSystem/pool";
import { $add } from "@/utils/calculate";
import { removeEmptyValues } from "@/utils/commit";
import { useUserStore, useArchitectureStoreHook } from "@/store";
import allocation from "./components/allocation.vue";
import { deriveExcel } from "@/utils/tools";
import { nextTick } from "vue";

const router = useRouter();
const eleFormRef = ref(null);
const loading = ref(false);
const total = ref(0);

// 为作业清单页面创建独立的批次号实例
const jobListBatchNumberInstance = createBatchNumberField();
const {
  batchNumber: jobListBatchNumber,
  getDynamicBatchNumber: jobListGetDynamicBatchNumber,
  clearBatchNumberCache: jobListClearBatchNumberCache,
} = jobListBatchNumberInstance;

// 替换筛选配置中的批次号字段
const jobListScreenList = jobScreenList.map((field) => {
  if (field.field === "batchNumber") {
    return jobListBatchNumber;
  }
  return field;
});

const eleFormAllList = ref([userOrDept, ...jobListScreenList, isMarkChange]);
const architectureStore = useArchitectureStoreHook();
const userStore = useUserStore();
const queryParams = reactive<RolePageQuery>({
  pageNum: 1,
  pageSize: 10,
});

// 表格数据
const roleList = ref<any[]>([]);

const dataTableRef = ref(null);
//筛选字段默认值
const screenValue: any = ref({
  userOrDept: [],
  // lostContactQueryResult: 1,
});
// 选中的角色
const ids = ref([]);
//选中的总金额;
const checkTotal = ref(0);
//总委托金额
const sumEntrustTotalAmount = ref(0);
//总委托本金额
const sumentrustPrincipalTotal = ref(0);
watch(ids, (val) => {
  let arr = dataTableRef.value.getSelectionRows();
  //计算勾选总金额;
  checkTotal.value = arr.reduce((sum, e) => $add(sum, e.entrustTotalAmount || 0), 0);
});
//标志新案
const tableRowClassName = ({ row, rowIndex }: any) => {
  if (row.followUpStatus === "新案") {
    return "res-nova";
  }
  return "";
};
const checkSelectable = ({ row, rowIndex }: any) => {
  console.log(row);
  return row?.followUpStatus !== "继续跟进";
};
// 查询
function handleQuery() {
  loading.value = true;
  let obj = {};
  sumEntrustTotalAmount.value = 0;
  let search = eleFormRef.value.getFormVlaue();
  for (let key in search) {
    if (search[key]) {
      if (key == "userOrDept") search[key] = search[key][search[key].length - 1] || "";
      obj[key] = search[key];
    }
  }
  dataSystemAPI
    .getCasepoolWorkPage({
      ...queryParams,
      ...obj,
    })
    .then((data: any) => {
      console.log("作业清单API返回数据:", data); // 添加调试日志
      roleList.value = data.list || [];
      if (data.list && data.list.length > 0) {
        sumEntrustTotalAmount.value = data.list[0].sumEntrustTotalAmount || 0;
        sumentrustPrincipalTotal.value = data.list[0].sumentrustPrincipalTotal || 0;
      }
      total.value = data.total || 0;
      console.log("roleList.value:", roleList.value); // 添加调试日志
    })
    .catch((error: any) => {
      console.error("作业清单API调用错误:", error); // 添加错误日志
      roleList.value = [];
      total.value = 0;
    })
    .finally(() => {
      loading.value = false;
    });
}

// 防抖标志，避免短时间内多次重置
let isResetting = false;

// 重置表单
const handleReset = () => {
  // 防抖保护，避免短时间内多次调用
  if (isResetting) return;
  isResetting = true;

  // 手动重置screenValue为初始值，避免触发eleForm的连锁反应
  const { userId, deptId, roles } = userStore.userInfo;
  if (deptId && roles[0] != "YG") {
    screenValue.value = { userOrDept: [deptId, userId] };
  } else {
    screenValue.value = { userOrDept: [userId] };
  }

  // 使用nextTick确保DOM更新完成后再执行
  nextTick(() => {
    // 延迟执行，确保表单重置完成后再重新获取批次号和查询
    setTimeout(() => {
      console.log("作业清单重置：开始重新获取批次号");

      // 构建筛选参数（重置后的初始值）
      const { userId, deptId, roles } = userStore.userInfo;
      let filterParams = {};
      if (deptId && roles[0] != "YG") {
        filterParams = { userOrDept: userId };
      } else {
        filterParams = { userOrDept: userId };
      }

      // 重新获取批次号选项
      jobListGetDynamicBatchNumber(filterParams, 'work').then(() => {
        console.log("作业清单重置：批次号获取成功");
      }).catch((error) => {
        console.warn("重置时批次号更新失败:", error);
      });

      // 执行查询
      handleQuery();
      isResetting = false;
    }, 300);
  });
};

// 批量退回
const batchReturn = () => {
  if (!ids.value.length) {
    return ElMessage({
      message: "请选择需要退回的案件",
      type: "warning",
    });
  }

  ElMessageBox.confirm(`确认批量退回勾选的${ids.value.length}条案件？`, "提示", {
    confirmButtonText: "确认",
    cancelButtonText: "取消",
    type: "warning",
    draggable: true,
  }).then(() => {
    // 调用批量退案API
    dataSystemAPI
      .casepoolCaseTuiAn({
        ids: ids.value,
      })
      .then((res: any) => {
        ElMessage({
          message: res.msg || "批量退回成功",
          type: "success",
        });
        // 清空选中的案件
        ids.value = [];
        // 刷新列表
        handleQuery();
      })
      .catch((error: any) => {
        ElMessage({
          message: error.msg || "批量退回失败",
          type: "error",
        });
      });
  });
};

// 批量标注
const batchMark = () => {
  if (!ids.value.length) {
    return ElMessage({
      message: "请选择需要标注的案件",
      type: "warning",
    });
  }

  // 重置表单并显示对话框
  batchMarkForm.followUpStatus = "";
  batchMarkShow.value = true;
};

// 批量标注提交
const batchMarkSubmit = () => {
  batchMarkFormRef.value?.validate((valid: boolean) => {
    if (valid) {
      batchMarkLoading.value = true;
      dataSystemAPI
        .casepoolBatchUpdateStatus({
          ids: ids.value,
          followUpStatus: batchMarkForm.followUpStatus,
        })
        .then((res: any) => {
          ElMessage({
            message: res.data || "批量标注成功",
            type: "success",
          });
          // 关闭对话框
          batchMarkShow.value = false;
          // 清空选中的案件
          ids.value = [];
          // 刷新列表
          handleQuery();
        })
        .catch((error: any) => {
          ElMessage({
            message: error.msg || "批量标注失败",
            type: "error",
          });
        })
        .finally(() => {
          batchMarkLoading.value = false;
        });
    }
  });
};

// 详情
const handleOpenDialog = (id?: any) => {
  let search = removeEmptyValues(eleFormRef.value.getFormVlaue());
  if (search["userOrDept"])
    search["userOrDept"] = search["userOrDept"][search["userOrDept"].length - 1] || "";

  router.push({
    path: "/dataSystem/pool/poolDetails",
    query: {
      id,
      pagesType: 2,
      search: JSON.stringify({
        ...search,
        ...queryParams,
      }),
    },
  });
};

// 行复选框选中
function handleSelectionChange(selection: any) {
  ids.value = selection.map((item: any) => item.id);
}

//换单确认弹窗
const casepoolChangeFun = (type: any) => {
  let list = ids.value;
  if (!list.length) {
    return ElMessage({
      message: "请选择需要换单的数据",
      type: "warning",
    });
  }
  let str = "";
  if (type == 1) str = "确认要把这些数据进行换单?";
  ElMessageBox.confirm(str, "提示", {
    confirmButtonText: "确认",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    if (type == 1) casepoolChange();
  });
};
// 换单
const casepoolChange = () => {
  let list = ids.value;
  let search = eleFormRef.value.getFormVlaue();
  let obj = {
    // userId: userStore.userInfo.userId,
    userId: search["userOrDept"][search["userOrDept"].length - 1] || "",
    ccIds: list,
  };
  dataSystemAPI.casepoolChange(obj).then((e) => {
    ElMessage({
      message: "换单成功",
      type: "success",
    });
    handleQuery();
  });
};

// 分配案件
const allocationShow = ref(false);
//分配案件的id集合
const allocationIds = ref([]);
//自动分配
const automaticAllocation = () => {
  ElMessageBox({
    title: "自动分配案池",
    draggable: true,
    showCancelButton: true,
    showConfirmButton: false,
    message: h("div", null, [
      // 这里用到了h函数
      h(ElButton, {
        text: true,
        type: "primary",
        innerHTML: "分配选中数据",
        onClick: automaticList,
      }),
      h(ElButton, {
        text: true,
        type: "success",
        innerHTML: "分配指定数据",
        onClick: () => (allocationShow.value = true),
      }),
    ]),
  })
    .then((res) => {})
    .catch((res) => {});
};
//分配选中的案池
const automaticList = async () => {
  let list = ids.value;
  if (!list.length) {
    return ElMessage({
      message: "请选择需要分配的数据",
      type: "warning",
    });
  }
  allocationIds.value = list;
  allocationShow.value = true;
  // automaticAll(list);
};
/**导出相关**/
const exportFormRef = ref(null);
const exportLoading = ref(false);
const exportShow = ref(false);
const exportForm = reactive({
  derivedValue: "导出选中数据",
  isTuiAn: false,
  fileName: "",
});

const derivedOptions = ["导出选中数据", "导出指定数据"];

/**批量标注相关**/
const batchMarkShow = ref(false);
const batchMarkLoading = ref(false);
const batchMarkFormRef = ref(null);
const batchMarkForm = reactive({
  followUpStatus: "",
});
const followUpStatusOptions = ref([]);
//取消 重置
const exportHandleCloseDialog = () => {
  exportShow.value = false;
  exportFormRef.value.resetFields();
};
//确认
const exportHandleSubmit = () => {
  const { derivedValue, isTuiAn, fileName } = exportForm;
  //导出全部
  let search = removeEmptyValues(eleFormRef.value.getFormVlaue());
  search["userOrDept"] = search["userOrDept"][search["userOrDept"].length - 1] || "";
  let obj = {
    userOrDept: search["userOrDept"],
  };

  // 导出部分
  if (derivedValue == "导出选中数据") {
    if (ids.value.length == 0) {
      return ElMessage({
        message: "请勾选需要导出的内容",
        type: "warning",
      });
    } else {
      obj.ids = ids.value;
    }
  } else {
    obj = search;
  }
  if (!fileName || fileName.trim() == "") {
    return ElMessage({
      message: "请输入文件名",
      type: "warning",
    });
  } else {
    obj.fileName = fileName;
  }
  // 退案
  if (isTuiAn) {
    obj.isTuiAn = 1;
  }

  exportLoading.value = true;
  dataSystemAPI.workCaseExport(obj).then((data: any) => {
    console.log(data);
    deriveExcel(data.titleList, data.list, data.fileName);
    nextTick(() => {
      exportLoading.value = false;
      if (isTuiAn) handleQuery();
      exportHandleCloseDialog();
    });
  });
};

// 失联查询结果转换
const lostContactQueryResultName = computed(() => {
  return (type: any) => {
    const field = eleFormAllList.value.find((e: any) => e.field == "lostContactQueryResult");
    if (!field || !field.options) {
      return "-";
    }
    const { options } = field;
    let name = false;
    if (options && options.length > 0) {
      name = options.find((t: any) => t.value == type)?.label || "-";
    }
    return name;
  };
});
// 案件类型
const caseTypeName = computed(() => {
  return (type: any) => {
    const { options } = eleFormAllList.value.find((e: any) => e.label == "案件类型");
    let name = false;
    if (options && options.length > 0) {
      name = options.find((t: any) => t.value == type)?.label || "-";
    }
    return name;
  };
});

// 获取配置状态的CSS类名
function getConfigStatusClass(status: string) {
  if (status === "0") {
    return "unlocked"; // 解锁状态
  } else if (status === "1") {
    return "locked"; // 锁定状态
  }
  return "";
}

// 获取配置状态的图标
function getConfigStatusIcon(status: string) {
  if (status === "0") {
    return "fas fa-unlock"; // 解锁图标
  } else if (status === "1") {
    return "fas fa-lock"; // 锁定图标
  }
  return "";
}

const initOptions = async () => {
  console.log("初始化选项数据...");

  // 并行加载所有选项数据，提高性能
  const [workStatusOptions, lostStatusOptions, followStatusOptions] = await Promise.all([
    architectureStore.casepoolKeyValueList("work_status"),
    architectureStore.casepoolKeyValueList("lost_status"),
    architectureStore.casepoolKeyValueList("follow_status"),
  ]);

  // 设置表单选项
  const followUpStatusField = eleFormAllList.value.find((e: any) => e.field == "followUpStatus");
  if (followUpStatusField) {
    followUpStatusField.options = workStatusOptions;
  }

  const lostContactField = eleFormAllList.value.find(
    (e: any) => e.field == "lostContactQueryResult"
  );
  if (lostContactField) {
    lostContactField.options = lostStatusOptions;
  }

  // 设置批量标注的跟进状态选项
  followUpStatusOptions.value = followStatusOptions;

  // 初始化作业清单页面的批次号选项，使用作业清单接口，传递userOrDept参数
  // 按照handleQuery的逻辑处理userOrDept数组
  const userOrDeptArray = screenValue.value.userOrDept;
  const workFilterParams = {
    userOrDept: userOrDeptArray[userOrDeptArray.length - 1] || ""
  };
  await jobListGetDynamicBatchNumber(workFilterParams, 'work');

  console.log("作业清单页面选项数据初始化完成");
};
/**
 * 获取表格内容可用高度
 * **/
const contentHeader = ref(600); // 设置初始高度

// 获取表格高度
const getTableHeight = () => {
  const appMainH = document.querySelectorAll(".app-main")[0]?.offsetHeight;
  const filterCardH = document.querySelectorAll(".job-filter-card")[0]?.offsetHeight;
  const actionSectionH = document.querySelectorAll(".action-section")[0]?.offsetHeight;
  const dataHeaderH = document.querySelectorAll(".data-header")[0]?.offsetHeight;
  console.log("高度计算:", { appMainH, filterCardH, actionSectionH, dataHeaderH });

  // 计算可用高度：总高度 - 筛选卡片 - 操作区域 - 数据标题 - 分页 - 边距
  const usableHeight =
    appMainH - (filterCardH || 0) - (actionSectionH || 0) - (dataHeaderH || 0) - 100 - 60;
  contentHeader.value = Math.max(usableHeight, 400); // 最小高度400px
  console.log("表格高度:", contentHeader.value);
};
// 移除 onUpdated 中的 initOptions 调用，避免重复请求
// onUpdated(async () => {
//   initOptions();
// });
onMounted(async () => {
  const { userId, deptId, roles } = userStore.userInfo;

  console.log("onMounted");
  if (deptId && roles[0] != "YG") {
    screenValue.value["userOrDept"] = [deptId, userId];
  } else {
    screenValue.value["userOrDept"] = [userId];
  }
  initOptions();

  setTimeout(() => {
    handleQuery();
    getTableHeight();
  }, 0);
});
</script>
<style>
.flex-between {
  display: flex;
  justify-content: space-between;
}

.flex-center {
  display: flex;
  justify-content: center;
}

.flex-start {
  display: flex;
  align-items: center;
}

.tips {
  span {
    margin-right: 2px;
  }

  text {
    font-weight: bold;
    color: red;
  }
}

.el-table .res-nova {
  --el-table-tr-bg-color: var(--el-color-danger-light-9);
}

/* 作业清单容器样式 */
.job-list-container {
  background-color: #19275d;
  height: calc(100vh - 60px);
  padding: 16px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-sizing: border-box;
}

/* 作业清单筛选条件卡片样式 */
.job-filter-card {
  margin-bottom: 16px;
  border-radius: 12px;
  background: #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
}

.job-filter-card .el-card__body {
  padding: 20px;
}

.filter-header {
  margin-bottom: 16px;
}

.filter-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 12px;
}

.filter-icon {
  margin-right: 8px;
  color: #409eff;
  font-size: 18px;
}

.title-text {
  color: #2c3e50;
}

.filter-divider {
  margin: 0;
  border-color: #e4e7ed;
}

/* 操作区域样式 */
.action-section {
  margin-bottom: 12px;
  padding: 6px 0;
  color: #ffffff;
  height: 45px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
}

.stats-info {
  display: flex;
  align-items: center;
}

.action-buttons {
  display: flex;
  gap: 12px;
  align-items: center;
}

/* 按钮基础样式 */
.action-section .el-button {
  height: 36px;
  padding: 0 16px;
  font-size: 14px;
  font-weight: bold;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-right: 8px;
}

/* 批量退回按钮 - 蓝色 */
.action-section .el-button--success:nth-child(1) {
  background-color: #409eff;
  border-color: #409eff;
  color: #ffffff;
}

.action-section .el-button--success:nth-child(1):hover {
  background-color: #66b1ff;
  border-color: #66b1ff;
}

/* 批量换单按钮 - 蓝色 */
.action-section .el-button--success:nth-child(2) {
  background-color: #409eff;
  border-color: #409eff;
  color: #ffffff;
}

.action-section .el-button--success:nth-child(2):hover {
  background-color: #66b1ff;
  border-color: #66b1ff;
}

/* 任务分派按钮 - 蓝色 */
.action-section .el-button--success:nth-child(3) {
  background-color: #409eff;
  border-color: #409eff;
  color: #ffffff;
}

.action-section .el-button--success:nth-child(3):hover {
  background-color: #66b1ff;
  border-color: #66b1ff;
}

/* 导出按钮 - 蓝色 */
.action-section .el-button--primary {
  background-color: #409eff;
  border-color: #409eff;
  color: #ffffff;
}

.action-section .el-button--primary:hover {
  background-color: #66b1ff;
  border-color: #66b1ff;
}

/* 批量标注按钮 - 橙色 */
.action-section .el-button--warning {
  background-color: #e6a23c;
  border-color: #e6a23c;
  color: #ffffff;
}

/* 批量标注对话框样式 */
.batch-mark-info {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  background-color: #f0f9ff;
  border: 1px solid #bfdbfe;
  border-radius: 6px;
  color: #1e40af;
  font-size: 14px;
}

.action-section .el-button--warning:hover {
  background-color: #ebb563;
  border-color: #ebb563;
}

/* 禁用状态 */
.action-section .el-button:disabled {
  background-color: #6c757d !important;
  border-color: #6c757d !important;
  color: #ffffff !important;
  opacity: 0.6;
  box-shadow: none;
}

/* 点击效果 */
.action-section .el-button:active {
  transform: translateY(1px);
}

.action-section .tips span {
  color: #ffffff;
  font-weight: bold;
}

/* 数据表格卡片样式 */
.table-card {
  border-radius: 12px;
  background: #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-height: 0;
}

.table-card .el-card__body {
  padding: 12px;
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-height: 0;
}

.data-header {
  margin-bottom: 12px;
  flex-shrink: 0;
}

.data-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 12px;
}

.data-icon {
  margin-right: 8px;
  color: #409eff;
  font-size: 18px;
}

.data-divider {
  margin: 0;
  border-color: #e4e7ed;
}

/* 表格容器样式 */
.table-card :deep(.el-table) {
  flex: 1;
  height: 100%;
}

.table-card :deep(.el-table .el-table__body-wrapper) {
  overflow-y: auto;
  max-height: calc(100vh - 400px);
}

/* 分页容器样式 */
.pagination-container {
  margin-top: 12px;
  padding: 8px 0;
  flex-shrink: 0;
  display: flex;
  justify-content: center;
}

/* 筛选条件表单项间距 */
.filter-card .el-form-item {
  margin-bottom: 16px;
}

/* 筛选条件标签样式 */
.filter-card .el-form-item__label {
  color: #2c3e50 !important;
  font-weight: bold !important;
  font-size: 14px;
}

/* 筛选条件内容区域 */
.filter-card .el-form-item__content {
  max-width: 200px;
}

/* 筛选条件输入框样式 */
.filter-card .el-input__wrapper {
  border-radius: 12px;
  height: 40px;
  width: 100%;
  max-width: 200px;
  border: 1px solid #e4e7ed;
  box-shadow: none;
  transition: all 0.3s;
  background-color: #f8f9fa;
}

.filter-card .el-input__wrapper:hover {
  border-color: #c0c4cc;
  background-color: #ffffff;
}

.filter-card .el-input__wrapper.is-focus {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
  background-color: #ffffff;
}

/* 筛选条件下拉框样式 */
.filter-card .el-select .el-input__wrapper {
  border-radius: 12px;
  height: 40px;
  width: 100%;
  max-width: 200px;
  border: 1px solid #e4e7ed;
  box-shadow: none;
  background-color: #f8f9fa;
  transition: all 0.3s;
}

.filter-card .el-select .el-input__wrapper:hover {
  border-color: #c0c4cc;
  background-color: #ffffff;
}

.filter-card .el-select .el-input__wrapper.is-focus {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
  background-color: #ffffff;
}

/* 筛选条件级联选择器样式 */
.filter-card .el-cascader .el-input__wrapper {
  border-radius: 12px;
  height: 40px;
  width: 100%;
  max-width: 200px;
  border: 1px solid #e4e7ed;
  box-shadow: none;
  background-color: #f8f9fa;
  transition: all 0.3s;
}

.filter-card .el-cascader .el-input__wrapper:hover {
  border-color: #c0c4cc;
  background-color: #ffffff;
}

.filter-card .el-cascader .el-input__wrapper.is-focus {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
  background-color: #ffffff;
}

/* 筛选条件日期选择器样式 */
.filter-card .el-date-editor.el-input {
  border-radius: 12px;
  height: 40px;
  width: 100%;
  max-width: 200px;
}

.filter-card .el-date-editor .el-input__wrapper {
  border-radius: 12px;
  height: 40px;
  border: 1px solid #e4e7ed;
  box-shadow: none;
  background-color: #f8f9fa;
  transition: all 0.3s;
}

.filter-card .el-date-editor .el-input__wrapper:hover {
  border-color: #c0c4cc;
  background-color: #ffffff;
}

.filter-card .el-date-editor .el-input__wrapper.is-focus {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
  background-color: #ffffff;
}

/* 筛选条件输入框内部文字样式 */
.filter-card .el-input__inner {
  height: 38px;
  line-height: 38px;
  border-radius: 12px;
  font-size: 14px;
  color: #606266;
  background-color: transparent;
}

/* 筛选条件占位符样式 */
.filter-card .el-input__wrapper input::placeholder {
  color: #c0c4cc;
  font-size: 14px;
}

/* 筛选条件下拉箭头样式 */
.filter-card .el-select .el-input__suffix,
.filter-card .el-cascader .el-input__suffix {
  height: 40px;
  display: flex;
  align-items: center;
}

/* 筛选条件按钮样式 */
.filter-card .el-button {
  height: 40px;
  border-radius: 12px;
  padding: 0 20px;
  font-weight: 500;
  font-size: 14px;
  border: 1px solid #e4e7ed;
  background: #f8f9fa;
  color: #606266;
  min-width: 80px;
}

.filter-card .el-button--primary {
  background: #409eff;
  border-color: #409eff;
  color: #ffffff;
}

.filter-card .el-button:hover {
  border-color: #c0c4cc;
  background: #ffffff;
}

.filter-card .el-button--primary:hover {
  background: #66b1ff;
  border-color: #66b1ff;
}

/* 表单按钮容器样式 */
.filter-card .formBox_button {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 12px;
  margin-top: 8px;
}

/* 表单标签样式 */
.job-list-container .el-form-item__label {
  color: #2c3e50 !important;
  font-weight: bold !important;
}

/* 表格和其他文本内容样式 */
.job-list-container .el-table,
.job-list-container .el-table th,
.job-list-container .el-table td,
.job-list-container .el-button {
  color: #2c3e50;
  font-weight: bold;
}

/* 表头样式 - 单行不换行，居中对齐 */
.job-list-container .el-table th {
  text-align: center !important;
}

.job-list-container .el-table th .cell {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  text-align: center !important;
  justify-content: center !important;
  display: flex !important;
  align-items: center !important;
  font-weight: bold !important;
  color: #2c3e50 !important;
}

/* 表格内容居中对齐 */
.job-list-container .el-table td {
  text-align: center !important;
}

.job-list-container .el-table td .cell {
  text-align: center !important;
  justify-content: center !important;
  display: flex !important;
  align-items: center !important;
}
</style>

<style scoped>
/* 作业清单页面专用紧凑样式 */
.action-section {
  margin-bottom: 12px;
  padding: 6px 0;
  color: #ffffff;
  height: 45px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
}

.job-filter-card {
  margin-bottom: 6px;
  flex-shrink: 0;
}

/* 作业清单筛选条件样式 - 强制label和输入框左右排列 */
.job-filter-card :deep(.el-form-item) {
  display: flex !important;
  flex-direction: row !important;
  align-items: center !important;
  margin-bottom: 15px !important;
}

.job-filter-card :deep(.el-form-item__label) {
  width: 80px !important;
  min-width: 80px !important;
  max-width: 80px !important;
  text-align: left !important;
  font-size: 12px !important;
  font-weight: 600 !important;
  color: #2c3e50 !important;
  white-space: nowrap !important;
  margin-right: 8px !important;
  margin-bottom: 0 !important;
  padding: 0 !important;
  line-height: 32px !important;
}

.job-filter-card :deep(.el-form-item__content) {
  flex: 1 !important;
  max-width: none !important;
  margin-left: 0 !important;
  width: auto !important;
}

/* 使用与案池管理一致的简洁样式 */
.job-filter-card :deep(.el-input__wrapper) {
  height: 32px !important;
  font-size: 12px !important;
  border: 1px solid #d1d5db !important;
  border-radius: 4px !important;
  background-color: #ffffff !important;
}

.job-filter-card :deep(.el-select .el-input__wrapper) {
  height: 32px !important;
  font-size: 12px !important;
  border: 1px solid #d1d5db !important;
  border-radius: 4px !important;
  background-color: #ffffff !important;
}

.job-filter-card :deep(.el-date-editor .el-input__wrapper) {
  height: 32px !important;
  font-size: 12px !important;
  border: 1px solid #d1d5db !important;
  border-radius: 4px !important;
  background-color: #ffffff !important;
}

.job-filter-card :deep(.el-cascader .el-input__wrapper) {
  height: 32px !important;
  font-size: 12px !important;
  border: 1px solid #d1d5db !important;
  border-radius: 4px !important;
  background-color: #ffffff !important;
}

/* 操作按钮样式 - 与案池管理保持一致 */
.job-filter-card :deep(.operate-box) {
  display: flex !important;
  gap: 12px !important;
  align-items: center !important;
  justify-content: flex-end !important;
  width: 100% !important;
  padding: 0 !important;
}

/* 强制表单项一行显示5个 - 只针对表单项容器 */
.job-filter-card :deep(.el-form .el-row .el-col .el-row > .el-col) {
  width: 20% !important;
  max-width: 20% !important;
  flex: 0 0 20% !important;
}

.job-filter-card :deep(.operate-box .el-button) {
  height: 32px !important;
  font-size: 12px !important;
  padding: 8px 20px !important;
  border-radius: 4px !important;
  min-width: 70px !important;
  font-weight: 500 !important;
}

.job-filter-card :deep(.operate-box .el-button--primary) {
  background-color: #409eff !important;
  border-color: #409eff !important;
  color: #ffffff !important;
}

.job-filter-card :deep(.operate-box .el-button--default) {
  background-color: #ffffff !important;
  border-color: #dcdfe6 !important;
  color: #606266 !important;
}

/* 配置状态标签样式 */
.config-status-tag {
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 10px;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  gap: 2px;
}

/* 解锁状态样式 */
.config-status-tag.unlocked {
  background: #f0f9ff;
  color: #059669;
  border: 1px solid #a7f3d0;
}

/* 锁定状态样式 */
.config-status-tag.locked {
  background: #fef2f2;
  color: #dc2626;
  border: 1px solid #fecaca;
}

.config-status-tag i {
  font-size: 8px;
}
</style>
