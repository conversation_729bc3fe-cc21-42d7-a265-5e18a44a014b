<template>
  <div class="navbar__right">
    <!-- 消息通知 -->
    <Notification />

    <!-- 问候语 -->
    <span class="greeting-text">{{ greetingText }}👋</span>

    <!-- 用户信息 -->
    <UserProfile />
  </div>
</template>

<script setup lang="ts">
import { getGreeting } from "@/utils";

import UserProfile from "./UserProfile.vue";
import Notification from "./Notification.vue";

// 问候语
const greetingText = ref(getGreeting());

// 定时更新问候语（每分钟检查一次）
onMounted(() => {
  const timer = setInterval(() => {
    greetingText.value = getGreeting();
  }, 60000); // 60秒检查一次

  onUnmounted(() => {
    clearInterval(timer);
  });
});
</script>

<style lang="scss" scoped>
.navbar__right {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 16px;
  height: auto;
  min-height: 40px;
  background-color: #2c3e50;
  border-radius: 20px;
  margin-right: 16px;

  .greeting-text {
    color: #fff;
    font-size: 14px;
    padding: 0 8px;
    white-space: nowrap;
  }
}

// 通知组件样式覆盖
:deep(.notification-icon),
:deep(.el-icon) {
  color: #fff !important;
  font-size: 18px !important;

  &:hover {
    color: #3498db !important;
  }
}

:deep(.el-badge__content) {
  background-color: #ff4757 !important;
  border: none !important;
  font-size: 10px !important;
  min-width: 16px !important;
  height: 16px !important;
  line-height: 16px !important;
}

// 通知组件容器样式
:deep(.el-badge) {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  padding: 6px !important;
}

// 响应式布局
@media (max-width: 768px) {
  .navbar__right {
    gap: 12px;
    padding: 0 12px;
    margin-right: 8px;

    .greeting-text {
      font-size: 12px;
    }
  }
}
</style>
