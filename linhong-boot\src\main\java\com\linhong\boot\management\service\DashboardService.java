package com.linhong.boot.management.service;

import com.linhong.boot.management.model.vo.CaseDistributionVO;
import com.linhong.boot.management.model.vo.RepaymentTrendVO;

import java.time.LocalDate;
import java.util.List;

/**
 * 仪表板服务接口
 */
public interface DashboardService {

    /**
     * 获取回款趋势数据
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param period    分析类型 (amount: 金额, count: 户数, recent: 近期)
     * @param operatorId 操作员ID
     * @return 回款趋势数据
     */
    RepaymentTrendVO getRepaymentTrend(LocalDate startDate, LocalDate endDate, String period, String operatorId);

    /**
     * 获取案件分布数据
     *
     * @param type 类型：department-部门，personal-个人
     * @return 案件分布数据列表
     */
    List<CaseDistributionVO> getCaseDistribution(String type);
}
