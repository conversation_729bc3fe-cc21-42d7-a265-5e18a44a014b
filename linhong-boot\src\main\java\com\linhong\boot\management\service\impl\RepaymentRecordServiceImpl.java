package com.linhong.boot.management.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.linhong.boot.management.mapper.RepaymentRecordsMapper;
import com.linhong.boot.management.model.entity.RepaymentRecords;
import com.linhong.boot.management.model.entity.UserMonthlyRanking;
import com.linhong.boot.management.model.query.RepaymentQuery;
import com.linhong.boot.management.service.RepaymentRecordService;
import com.linhong.boot.system.model.entity.Dept;
import com.linhong.boot.system.model.query.UserPageQuery;
import com.linhong.boot.system.model.vo.UserPageVO;
import com.linhong.boot.system.model.vo.UserProfileVO;
import com.linhong.boot.system.service.DeptService;
import com.linhong.boot.system.service.UserService;

import lombok.RequiredArgsConstructor;

/**
 * 还款记录
 */
@Service
@RequiredArgsConstructor
public class RepaymentRecordServiceImpl extends ServiceImpl<RepaymentRecordsMapper, RepaymentRecords> implements RepaymentRecordService {
	
	private final UserService userService;
	private final DeptService deptService;
	
	@Override
	public List<UserMonthlyRanking> userRank() {
		List<RepaymentRecords> reList = this.baseMapper.userRank();
		List<UserMonthlyRanking> rankList = new ArrayList<>();
		for (RepaymentRecords r : reList) {
			UserMonthlyRanking rank = new UserMonthlyRanking();
			rank.setUserId(Long.valueOf(r.getOperatorId()));
			UserProfileVO u = userService.getUserProfile(Long.valueOf(r.getOperatorId()));
			rank.setUserName(u.getNickname()==null?"未知姓名":u.getNickname());
			rank.setUserPoints(r.getRepaymentAmount().longValue());
			rank.setDeptName(u.getDeptName());
			// 设置回收率和委托总金额
			if (r.getRecoveryRate() != null) {
				rank.setRecoveryRate(r.getRecoveryRate().doubleValue());
			}
			if (r.getEntrustTotalAmount() != null) {
				rank.setEntrustTotalAmount(r.getEntrustTotalAmount().longValue());
			}
			rankList.add(rank);
		}

		return rankList;
	}

	@Override
	public List<UserMonthlyRanking> userRankNow() {
		List<UserMonthlyRanking> rankList = new ArrayList<>();
		List<RepaymentRecords> list = this.baseMapper.userRankNow();
        for (RepaymentRecords r : list) {
           UserProfileVO user = userService.getUserProfile(Long.valueOf(r.getOperatorId()));
     	   Long rId = Long.valueOf(r.getOperatorId());
     	   UserMonthlyRanking rank = new UserMonthlyRanking();
 		   rank.setUserId(rId);
 		   rank.setUserPoints(r.getRepaymentAmount().longValue());
 		   rank.setUserName(user.getNickname());
 		   rank.setDeptName(user.getDeptName());
 		   // 设置回收率和委托总金额
 		   if (r.getRecoveryRate() != null) {
 			   rank.setRecoveryRate(r.getRecoveryRate().doubleValue());
 		   }
 		   if (r.getEntrustTotalAmount() != null) {
 			   rank.setEntrustTotalAmount(r.getEntrustTotalAmount().longValue());
 		   }
     	   rankList.add(rank);
	    }
		return rankList;
	}

	@Override
	public List<UserMonthlyRanking> userRankByTeam() {
		List<RepaymentRecords> reList = this.baseMapper.userRankByTeam();
		List<UserMonthlyRanking> rankList = new ArrayList<>();
		for (RepaymentRecords r : reList) {
			UserMonthlyRanking rank = new UserMonthlyRanking();
			// 对于团队排名，operatorId存储的是部门名称
			rank.setUserId(0L); // 团队排名没有具体的用户ID
			rank.setUserName(r.getOperatorId()); // 部门名称作为用户名显示
			rank.setUserPoints(r.getRepaymentAmount().longValue());
			rank.setDeptName(r.getOperatorId()); // 部门名称
			// 设置回收率和委托总金额
			if (r.getRecoveryRate() != null) {
				rank.setRecoveryRate(r.getRecoveryRate().doubleValue());
			}
			if (r.getEntrustTotalAmount() != null) {
				rank.setEntrustTotalAmount(r.getEntrustTotalAmount().longValue());
			}
			rankList.add(rank);
		}
		return rankList;
	}

	@Override
	public List<UserMonthlyRanking> userRankNowByTeam() {
		List<RepaymentRecords> reList = this.baseMapper.userRankNowByTeam();
		List<UserMonthlyRanking> rankList = new ArrayList<>();
		for (RepaymentRecords r : reList) {
			UserMonthlyRanking rank = new UserMonthlyRanking();
			// 对于团队排名，operatorId存储的是部门名称
			rank.setUserId(0L); // 团队排名没有具体的用户ID
			rank.setUserName(r.getOperatorId()); // 部门名称作为用户名显示
			rank.setUserPoints(r.getRepaymentAmount().longValue());
			rank.setDeptName(r.getOperatorId()); // 部门名称
			// 设置回收率和委托总金额
			if (r.getRecoveryRate() != null) {
				rank.setRecoveryRate(r.getRecoveryRate().doubleValue());
			}
			if (r.getEntrustTotalAmount() != null) {
				rank.setEntrustTotalAmount(r.getEntrustTotalAmount().longValue());
			}
			rankList.add(rank);
		}
		return rankList;
	}



	@Override
	public BigDecimal repaySumAmount(RepaymentQuery queryParams) {
		if(queryParams.getOperatorId()!=null) {
			/*判断是部门 还是 员工*/
			String uod = queryParams.getOperatorId();
			Dept d = deptService.getBaseMapper().selectById(uod);
			List<Long> ids = new ArrayList<>();
			if(d!=null) {
				/*为部门查询 , 查询该部门下所有员工*/
				UserPageQuery q = new UserPageQuery();
				q.setPageSize(10000);
				q.setDeptId(uod);
				IPage<UserPageVO> u = userService.getUserPage(q);
				ids = u.getRecords().stream().map(UserPageVO::getId).collect(Collectors.toList());
			}else {
				/*指定的员工查询*/
				ids.add(Long.valueOf(uod));
				queryParams.setUIds(ids);
			}
			queryParams.setUIds(ids);
		}
		LambdaQueryWrapper<RepaymentRecords> query = new LambdaQueryWrapper<>();
		query.eq(queryParams.getCustomerIndexNumber()!=null,RepaymentRecords::getCustomerIndexNumber, queryParams.getCustomerIndexNumber())
		.eq(queryParams.getOutsourceSerialNumber()!=null,RepaymentRecords::getOutsourceSerialNumber, queryParams.getOutsourceSerialNumber())
		.in(queryParams.getBatchNumber()!=null && !queryParams.getBatchNumber().isEmpty(),RepaymentRecords::getBatchNumber, queryParams.getBatchNumber())
		.like(queryParams.getCustomerName()!=null,RepaymentRecords::getCustomerName, queryParams.getCustomerName())
		.eq(queryParams.getCardholderCode()!=null,RepaymentRecords::getCardholderCode, queryParams.getCardholderCode())
		.in(RepaymentRecords::getOperatorId, queryParams.getUIds())
		.eq(RepaymentRecords::getIsDelete, "0");
		if(queryParams.getEntrustStartDate()!=null) {
			query.between(RepaymentRecords::getRepaymentDate, queryParams.getEntrustStartDate()[0], queryParams.getEntrustStartDate()[1]);
		}
		// 少一个 案件类型,需求未明确
		List<RepaymentRecords> recordsList = this.getBaseMapper().selectList(query);
		BigDecimal sum = recordsList.stream().map(RepaymentRecords::getRepaymentAmount).reduce(BigDecimal.ZERO,BigDecimal::add);
		return sum;
	}
	
	
	


}
