<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.linhong.boot.system.mapper.RoleMapper">

  <resultMap id="BaseResultMap" type="com.linhong.boot.system.model.entity.Role">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Dec 02 12:16:44 CST 2024.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="status" jdbcType="BIT" property="status" />
    <result column="data_scope" jdbcType="TINYINT" property="dataScope" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="is_deleted" jdbcType="BIT" property="isDeleted" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Dec 02 12:16:44 CST 2024.
    -->
    id, name, code, sort, status, data_scope, create_by, create_time, update_by, update_time, 
    is_deleted
  </sql>
  <sql id="Base_ROLE_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Dec 02 12:16:44 CST 2024.
    -->
    r.id, name, r.code, r.sort, r.status, r.data_scope, r.create_by, r.create_time, r.update_by, r.update_time, 
    r.is_deleted
  </sql>
    <!-- 获取最大范围的数据权限 -->
    <select id="getMaximumDataScope" resultType="java.lang.Integer">
        SELECT
            min(data_scope)
        FROM
            sys_role
        <where>
            <choose>
                <when test="roles!=null and roles.size>0">
                    AND code IN
                    <foreach collection="roles" item="role" separator="," open="(" close=")">
                        #{role}
                    </foreach>
                </when>
                <otherwise>
                    id = -1
                </otherwise>
            </choose>
        </where>
    </select>
    
   <select id="selectByUserId" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_ROLE_Column_List" />
    from sys_role r left join sys_user_role ru on r.id = ru.role_id	left join sys_user u on u.id = ru.user_id
    where u.id = #{id} 
  </select>
    
</mapper>
