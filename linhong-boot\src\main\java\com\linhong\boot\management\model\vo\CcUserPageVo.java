package com.linhong.boot.management.model.vo;

import lombok.Data;

/**
 * 案件分页对象
 */
@Data
public class CcUserPageVo {
	
	/*是否标记换单 未明确*/
	/*外包序号  未明确*/
	/**
	 * 催收人或者组织
	 * 如果选择的用户为组长,部门负责人,则他下面所有人的案件都要可见
	 */
    private String userOrDept;
    /**
     * 客户姓名
     */
    private String customerName;
    /**
     * 批次号
     */
    private String batch_number;
    /**
     * 委案开始日期
     */
    private String entrustStartDate;
    /**
     * 客户索引号
     */
    private String customerIndexNumber;
    /**
     * 案件类型
     */
    private String caseType;
    /**
     * 委案机构
     */
    private String caseAgency;
    /**
     * 跟进状态
     */
    private int followUpStatus;
    /**
     * 委案结束日期
     */
    private String entrustEndDate;
    /**
     * 委托金额段 xxx-xxx
     */
    private String entrustTotalAmount;
    
    /**
     * 身份证
     */
    private String cardholderIdNumber;
    
    /**
     * 持卡人代码
     */
    private String cardholderCode;
    /**
     * 委托时月龄分档
     */
    private String entrustSgeBracket;
    /**
     * 委托时佣金分档
     */
    private String entrustCommissionBracket;

}
