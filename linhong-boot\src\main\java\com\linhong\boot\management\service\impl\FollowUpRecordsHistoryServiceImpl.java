package com.linhong.boot.management.service.impl;

import java.util.List;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.linhong.boot.common.enums.CaseEnum;
import com.linhong.boot.management.mapper.FollowUpRecordsHistoryMapper;
import com.linhong.boot.management.model.entity.FollowUpRecordsHistory;
import com.linhong.boot.management.model.query.RecordsQuery;
import com.linhong.boot.management.service.FollowUpRecordsHistoryService;

import lombok.RequiredArgsConstructor;

/**
 * 历史跟进功能业务
 */
@Service
@RequiredArgsConstructor
public class FollowUpRecordsHistoryServiceImpl extends ServiceImpl<FollowUpRecordsHistoryMapper, FollowUpRecordsHistory> implements FollowUpRecordsHistoryService {
	

	@Override
	public IPage<FollowUpRecordsHistory> getRecordsPage(RecordsQuery query) {
		int pageNum = query.getPageNum();
		int pageSize = query.getPageSize();
		Page<FollowUpRecordsHistory> page = new Page<>(pageNum, pageSize);
		QueryWrapper<FollowUpRecordsHistory> records = new QueryWrapper<>();
		records.eq("user_id", query.getId());
		records.orderByDesc("updated_at");
		IPage<FollowUpRecordsHistory> list = this.getBaseMapper().selectPage(page, records);
		List<FollowUpRecordsHistory> followUpRecords = list.getRecords();
		int startIndex = (pageNum - 1) * pageSize; // 计算当前页的起始序号
	    for(int i = 0; i < followUpRecords.size(); i++) {
	    	FollowUpRecordsHistory f = followUpRecords.get(i);
	    	f.setSort(startIndex + i + 1);
	    	f.setCollectionResult(CaseEnum.fromValue(Integer.valueOf(f.getCollectionResult())));
	    	f.setIsMarkedForChange(f.getIsMarkedForChange().equals("0")?"否":"是");
	    }
	    return list;
	}

	@Override
	public boolean saveBatchHistory(List<FollowUpRecordsHistory> historyRecords) {
		
		return this.saveBatch(historyRecords);
	}


}
