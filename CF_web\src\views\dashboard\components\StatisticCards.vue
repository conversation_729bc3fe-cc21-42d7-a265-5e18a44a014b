<template>
  <div class="statistic-cards">
    <!-- 顶部操作栏 -->
    <div class="header-toolbar">
      <div class="toolbar-left">
        <!-- 左侧可以放其他按钮 -->
      </div>
      <div class="toolbar-right">
        <el-button class="refresh-btn" size="small" @click="handleRefresh">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
        <div class="date-display">
          <el-icon><Calendar /></el-icon>
          <span>{{ currentDate }}</span>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="20">
      <el-col v-for="(card, index) in cards" :key="index" :xs="24" :sm="12" :lg="6">
        <div class="dashboard-card" :class="`card-${index + 1}`">
          <div class="card-content">
            <div class="card-left">
              <div class="card-title">{{ card.title }}</div>
              <div class="card-value">
                {{ card.value }}
                <span v-if="card.unit" class="card-unit">{{ card.unit }}</span>
              </div>
              <div v-if="card.trend" class="card-trend">
                <span
                  class="trend-value"
                  :class="card.trend.type === 'up' ? 'trend-up' : 'trend-down'"
                >
                  {{ card.trend.type === "up" ? "+" : "-" }}{{ card.trend.value }}%
                </span>
              </div>
            </div>
            <div class="card-right">
              <div class="card-icon">
                <img v-if="card.icon === 'users'" :src="UsersIconSvg" alt="用户图标" />
                <img v-else-if="card.icon === 'folder'" :src="WalletIconSvg" alt="钱包图标" />
                <img v-else-if="card.icon === 'trending-up'" :src="TrendingUpIconSvg" alt="趋势图标" />
                <img v-else-if="card.icon === 'chart'" :src="PercentageIconSvg" alt="百分比图标" />
              </div>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { Refresh, Calendar } from "@element-plus/icons-vue";
import UsersIconSvg from "@/assets/icons/users.svg";
import WalletIconSvg from "@/assets/icons/wallet.svg";
import TrendingUpIconSvg from "@/assets/icons/trending-up.svg";
import PercentageIconSvg from "@/assets/icons/percentage.svg";

interface StatisticCard {
  title: string;
  value: string | number;
  unit?: string;
  icon: string;
  color: string;
  trend?: {
    type: "up" | "down";
    value: number;
  };
}

interface Props {
  cards: StatisticCard[];
  loading?: boolean;
}

defineProps<Props>();

const emit = defineEmits<{
  refresh: [];
}>();

const handleRefresh = () => {
  emit('refresh');
};

// 获取当前日期和星期
const currentDate = computed(() => {
  const now = new Date();
  const year = now.getFullYear();
  const month = now.getMonth() + 1;
  const day = now.getDate();
  const weekdays = ["日", "一", "二", "三", "四", "五", "六"];
  const weekday = weekdays[now.getDay()];
  return `${year}年${month}月${day}日 | 星期${weekday}`;
});
</script>

<style lang="scss" scoped>
.statistic-cards {
  margin-bottom: 24px;
}

.header-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 0 4px;

  .toolbar-left {
    flex: 1;
  }

  .toolbar-right {
    display: flex;
    align-items: center;
    gap: 16px;

    .refresh-btn {
      background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
      border: none;
      color: white;
      font-weight: 500;
      padding: 8px 16px;
      border-radius: 6px;
      box-shadow: 0 2px 8px rgba(251, 191, 36, 0.3);
      transition: all 0.3s ease;

      &:hover {
        background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(251, 191, 36, 0.4);
      }

      .el-icon {
        margin-right: 4px;
      }
    }

    .date-display {
      display: flex;
      align-items: center;
      gap: 8px;
      color: rgba(255, 255, 255, 0.95);
      font-size: 14px;
      font-weight: 500;
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
      padding: 10px 20px;
      border-radius: 12px;
      border: 1px solid rgba(255, 255, 255, 0.3);
      backdrop-filter: blur(15px);
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;

      &:hover {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.25), rgba(255, 255, 255, 0.15));
        border-color: rgba(255, 255, 255, 0.4);
        transform: translateY(-1px);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
      }

      .el-icon {
        font-size: 16px;
        color: rgba(255, 255, 255, 0.9);
      }

      span {
        white-space: nowrap;
        letter-spacing: 0.5px;
      }
    }
  }
}

.dashboard-card {
  border-radius: 12px;
  padding: 24px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  height: 120px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.25);
    border-color: rgba(255, 255, 255, 0.2);
  }

  // 第一个卡片 - 深紫灰色渐变（总案件数）
  &.card-1 {
    background: linear-gradient(135deg, #4c5b7a 0%, #5a6b8a 100%);
  }

  // 第二个卡片 - 深蓝色渐变（总金额）
  &.card-2 {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  }

  // 第三个卡片 - 青蓝色渐变（今日回款）
  &.card-3 {
    background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
  }

  // 第四个卡片 - 深紫色渐变（平均回收率）
  &.card-4 {
    background: linear-gradient(135deg, #7c3aed 0%, #6d28d9 100%);
  }

  .card-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;

    .card-left {
      flex: 1;

      .card-title {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.9);
        margin-bottom: 8px;
        font-weight: 500;
      }

      .card-value {
        font-size: 32px;
        font-weight: 700;
        color: white;
        line-height: 1.1;
        margin-bottom: 6px;

        .card-unit {
          font-size: 18px;
          font-weight: 400;
          color: rgba(255, 255, 255, 0.9);
          margin-left: 4px;
        }
      }

      .card-trend {
        .trend-value {
          font-size: 12px;
          font-weight: 600;
          padding: 2px 8px;
          border-radius: 12px;
          background: rgba(255, 255, 255, 0.15);
          backdrop-filter: blur(5px);

          &.trend-up {
            color: #10b981;
            background: rgba(16, 185, 129, 0.15);
          }

          &.trend-down {
            color: #ef4444;
            background: rgba(239, 68, 68, 0.15);
          }
        }
      }
    }

    .card-right {
      .card-icon {
        width: 64px;
        height: 64px;
        display: flex;
        align-items: center;
        justify-content: center;

        img {
          width: 40px;
          height: 40px;
          filter: brightness(0) invert(1);
        }
      }
    }
  }
}

// 响应式适配
@media (max-width: 768px) {
  .header-toolbar {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;

    .toolbar-right {
      flex-direction: column;
      gap: 12px;
      align-items: center;

      .refresh-btn {
        align-self: center;
      }

      .date-display {
        text-align: center;
      }
    }
  }

  .dashboard-card {
    height: auto;
    padding: 20px;

    .card-content {
      .card-left {
        .card-value {
          font-size: 28px;
        }
      }

      .card-right {
        .card-icon {
          width: 56px;
          height: 56px;

          img {
            width: 36px;
            height: 36px;
          }
        }
      }
    }
  }
}
</style>
