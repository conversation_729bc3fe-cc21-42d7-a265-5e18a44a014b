import { queryMode } from "@/enums/optionsConfig/pool";
import { useUserStore, useArchitectureStoreHook } from "@/store";
import dataSystemAPI from "@/api/dataSystem/pool";
import taskPredictionApi from "@/api/dataSystem/taskPrediction";
const userStore = useUserStore();
const architectureStore = useArchitectureStoreHook();
architectureStore.getArchitectureInfo();
/**-------------------------动态获取筛选内容---------------------------**/

//催收人/组织
const userOrDept = reactive({
  label: "催收人/组织",
  type: "cascader",
  options: JSON.parse(JSON.stringify(architectureStore.architectureInfoTissue)),
  field: "userOrDept",
  placeholder: "请选择",
  disabled: userStore.userInfo.roles[0] == "YG" ? true : false,
  props: {
    value: "value",
    label: "label",
    checkStrictly: true,
  },
  rules: [{ required: true, message: "请选择催收人/组织", trigger: "blur" }],
});
/**批次号**/
const batchNumber = reactive({
  label: "批次号",
  type: "select",
  options: [],
  field: "batchNumber",
  placeholder: "请输入工号内有的批次号",
  multiple: true,
  rules: "",
});
const getBatchNumber = () => {
  // 使用与案池管理一致的逻辑：请求page接口，传递pageSize=9999，然后筛选出批次号
  taskPredictionApi.taskRepayTaskPage({
    pageNum: 1,
    pageSize: 9999
  }).then((data: any) => {
    const records = data.list || [];
    const batchNumbers = records
      .map((record: any) => record.batchNumber)
      .filter((batchNumber: string) => batchNumber && batchNumber.trim() !== '')
      .filter((value: string, index: number, self: string[]) => self.indexOf(value) === index) // 去重
      .sort(); // 排序

    batchNumber.options = batchNumbers.map((batchNumber: string) => ({
      value: batchNumber,
      label: batchNumber,
    }));
    console.log(`任务预测批次号选项已更新，共 ${batchNumber.options.length} 个选项`);
  }).catch((error: any) => {
    console.error("获取任务预测批次号失败:", error);
    batchNumber.options = [];
  });
};
getBatchNumber();

/**-----------------------页面呈现列表相关内容-----------------------------**/

// 表头
const jobListFields = [
  { label: "批次号", prop: "batchNumber" },
  { label: "客户姓名", prop: "customerName" },
  { label: "客户索引号", prop: "customerIndexNumber", width: "180px" },
  { label: "次月还款金额", prop: "nextMonthRepayment" },
  { label: "预计还款时间", prop: "expectedRepaymentDate" },
  { label: "备注", prop: "remarks" },
];

//筛选字段
const screenList: any = [
  batchNumber, // 添加批次号筛选字段
  {
    label: "客户索引号",
    type: "input",
    options: queryMode,
    fieldKey: "key13-key",
    field: "customerIndexNumber",
    placeholder: "请输入",
    rules: "",
  },
  {
    label: "预测还款时间",
    type: "date-picker",
    options: [],
    field: "huanTime",
    placeholder: "请选择",
    rules: "",
    formType: "daterange",
    size: "default",
  },
  {
    label: "跟进状态",
    type: "select",
    options: [],
    field: "followUpStatus",
    placeholder: "请选择跟进状态",
    rules: "",
    size: "default",
  },
  {
    label: "失联查询结果",
    type: "select",
    options: [],
    field: "lostContactQueryResult",
    placeholder: "请选择失联查询结果",
    rules: "",
    size: "default",
  },
];

export { userOrDept, batchNumber, jobListFields, screenList, getBatchNumber };
