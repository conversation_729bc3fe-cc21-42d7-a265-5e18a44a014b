import { queryMode } from "@/enums/optionsConfig/pool";
import { useUserStore, useArchitectureStoreHook } from "@/store";
import dataSystemAPI from "@/api/dataSystem/pool";
const userStore = useUserStore();
const architectureStore = useArchitectureStoreHook();
architectureStore.getArchitectureInfo();
/**-------------------------动态获取筛选内容---------------------------**/

//催收人/组织
const userOrDept = reactive({
  label: "催收人/组织",
  type: "cascader",
  options: JSON.parse(JSON.stringify(architectureStore.architectureInfoTissue)),
  field: "userOrDept",
  placeholder: "请选择",
  disabled: userStore.userInfo.roles[0] == "YG" ? true : false,
  props: {
    value: "value",
    label: "label",
    checkStrictly: true,
  },
  rules: [{ required: true, message: "请选择催收人/组织", trigger: "blur" }],
});
/**批次号**/
const batchNumber = reactive({
  label: "批次号",
  type: "select",
  options: [],
  field: "batchNumber",
  placeholder: "请输入工号内有的批次号",
  multiple: true,
  rules: "",
});
const getBatchNumber = () => {
  dataSystemAPI.casepoolGetChangeList({ batchNumber: 1 }).then((data: any) => {
    batchNumber.options = data.map((e: any) => {
      return {
        value: e.batchNumber,
        label: e.batchNumber,
      };
    });
  });
};
getBatchNumber();

/**-----------------------页面呈现列表相关内容-----------------------------**/

// 表头
const jobListFields = [
  { label: "批次号", prop: "batchNumber" },
  { label: "客户姓名", prop: "customerName" },
  { label: "客户索引号", prop: "customerIndexNumber", width: "180px" },
  { label: "次月还款金额", prop: "nextMonthRepayment" },
  { label: "预计还款时间", prop: "expectedRepaymentDate" },
  { label: "备注", prop: "remarks" },
];

//筛选字段
const screenList: any = [
  {
    label: "客户索引号",
    type: "input",
    options: queryMode,
    fieldKey: "key13-key",
    field: "customerIndexNumber",
    placeholder: "请输入",
    rules: "",
  },
  {
    label: "预测还款时间",
    type: "date-picker",
    options: [],
    field: "huanTime",
    placeholder: "请选择",
    rules: "",
    formType: "daterange",
    size: "default",
  },
  {
    label: "跟进状态",
    type: "select",
    options: [],
    field: "followUpStatus",
    placeholder: "请选择跟进状态",
    rules: "",
    size: "default",
  },
  {
    label: "失联查询结果",
    type: "select",
    options: [],
    field: "lostContactQueryResult",
    placeholder: "请选择失联查询结果",
    rules: "",
    size: "default",
  },
];

export { userOrDept, batchNumber, jobListFields, screenList };
