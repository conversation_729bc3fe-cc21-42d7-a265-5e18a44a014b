package com.linhong.boot.management.model.entity;

public class FollowUpRecordsWithBLOBs extends FollowUpRecords {

	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column follow_up_records.task_prediction
	 * @mbg.generated  Tue Nov 26 16:39:14 HKT 2024
	 */
	private String taskPrediction;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column follow_up_records.collection_notes
	 * @mbg.generated  Tue Nov 26 16:39:14 HKT 2024
	 */
	private String collectionNotes;

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column follow_up_records.task_prediction
	 * @return  the value of follow_up_records.task_prediction
	 * @mbg.generated  Tue Nov 26 16:39:14 HKT 2024
	 */
	public String getTaskPrediction() {
		return taskPrediction;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column follow_up_records.task_prediction
	 * @param taskPrediction  the value for follow_up_records.task_prediction
	 * @mbg.generated  Tue Nov 26 16:39:14 HKT 2024
	 */
	public void setTaskPrediction(String taskPrediction) {
		this.taskPrediction = taskPrediction == null ? null : taskPrediction.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column follow_up_records.collection_notes
	 * @return  the value of follow_up_records.collection_notes
	 * @mbg.generated  Tue Nov 26 16:39:14 HKT 2024
	 */
	public String getCollectionNotes() {
		return collectionNotes;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column follow_up_records.collection_notes
	 * @param collectionNotes  the value for follow_up_records.collection_notes
	 * @mbg.generated  Tue Nov 26 16:39:14 HKT 2024
	 */
	public void setCollectionNotes(String collectionNotes) {
		this.collectionNotes = collectionNotes == null ? null : collectionNotes.trim();
	}
}