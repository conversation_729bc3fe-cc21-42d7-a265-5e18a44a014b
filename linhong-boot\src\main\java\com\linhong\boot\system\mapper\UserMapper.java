package com.linhong.boot.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linhong.boot.common.annotation.DataPermission;
import com.linhong.boot.system.model.bo.UserBO;
import com.linhong.boot.system.model.dto.UserAuthInfo;
import com.linhong.boot.system.model.dto.UserExportDTO;
import com.linhong.boot.system.model.entity.User;
import com.linhong.boot.system.model.form.UserForm;
import com.linhong.boot.system.model.query.UserPageQuery;

import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 用户持久层
 *
 */
@Mapper
public interface UserMapper extends BaseMapper<User> {

    /**
     * 获取用户分页列表
     *
     * @param page
     * @param queryParams 查询参数
     * @return
     */
    @DataPermission(deptAlias = "u",userAlias = "u")
    Page<UserBO> getUserPage(Page<UserBO> page, UserPageQuery queryParams);

    /**
     * 获取用户表单详情
     *
     * @param userId 用户ID
     * @return
     */
    UserForm getUserFormData(Long userId);

    /**
     * 根据用户名获取认证信息
     *
     * @param username
     * @return
     */
    UserAuthInfo getUserAuthInfo(String username);

    /**
     * 获取导出用户列表
     *
     * @param queryParams
     * @return
     */
    @DataPermission(deptAlias = "u")
    List<UserExportDTO> listExportUsers(UserPageQuery queryParams);

    /**
     * 获取用户个人中心信息
     *
     * @param userId 用户ID
     * @return
     */
    UserBO getUserProfile(Long userId);
    /**
     * 根据部门ID获取用户id
     * @param deptId
     * @return
     */
    List<User> getUserOfDeptId(String deptId);
}
