package com.linhong.boot.management.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linhong.boot.management.model.entity.TransferRecords;
import com.linhong.boot.management.model.entity.TransferRecordsExample;
import com.linhong.boot.management.model.query.CcUserQuery;
import com.linhong.boot.management.model.vo.TransferRecordsVO;

@Mapper
public interface TransferRecordsMapper extends BaseMapper<TransferRecords> {
	
	/**
	 * 导出- 查询案件最近一条记录
	 * @param list
	 * @return
	 */
	List<TransferRecords> selectUserOrDept(List<String> list);
	
	/**
	 * 查询换单记录
	 * @param query
	 * @return
	 */
	Page<TransferRecordsVO> selectTransPage(Page<TransferRecordsVO> page, CcUserQuery query);
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table transfer_records
     *
     * @mbg.generated Mon Dec 02 17:42:49 CST 2024
     */
    long countByExample(TransferRecordsExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table transfer_records
     *
     * @mbg.generated Mon Dec 02 17:42:49 CST 2024
     */
    int deleteByExample(TransferRecordsExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table transfer_records
     *
     * @mbg.generated Mon Dec 02 17:42:49 CST 2024
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table transfer_records
     *
     * @mbg.generated Mon Dec 02 17:42:49 CST 2024
     */
//    int insert(TransferRecords row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table transfer_records
     *
     * @mbg.generated Mon Dec 02 17:42:49 CST 2024
     */
    int insertSelective(TransferRecords row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table transfer_records
     *
     * @mbg.generated Mon Dec 02 17:42:49 CST 2024
     */
    List<TransferRecords> selectByExample(TransferRecordsExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table transfer_records
     *
     * @mbg.generated Mon Dec 02 17:42:49 CST 2024
     */
    TransferRecords selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table transfer_records
     *
     * @mbg.generated Mon Dec 02 17:42:49 CST 2024
     */
    int updateByExampleSelective(@Param("row") TransferRecords row, @Param("example") TransferRecordsExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table transfer_records
     *
     * @mbg.generated Mon Dec 02 17:42:49 CST 2024
     */
    int updateByExample(@Param("row") TransferRecords row, @Param("example") TransferRecordsExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table transfer_records
     *
     * @mbg.generated Mon Dec 02 17:42:49 CST 2024
     */
    int updateByPrimaryKeySelective(TransferRecords row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table transfer_records
     *
     * @mbg.generated Mon Dec 02 17:42:49 CST 2024
     */
    int updateByPrimaryKey(TransferRecords row);
}