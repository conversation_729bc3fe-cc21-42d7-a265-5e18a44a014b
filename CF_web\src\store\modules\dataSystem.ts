import { store } from "@/store";

import dataSystemAPI from "@/api/dataSystem/pool";

//递归重组组织
function mergeUsersToChildren(data: any) {
  data.forEach((node: any) => {
    if (node.users && node.users.length > 0) {
      if (!node.children) {
        node.children = [];
      }
      node.users.forEach((user: any) => {
        if (!node.label && !node.value) {
          node.value = user.id;
          node.label = user.nickname;
          node.isUser = true;
          node.children = [];
        } else {
          node.children.push({
            value: user.id,
            label: user.nickname,
            isUser: true,
          });
        }
      });
      delete node.users;
    }
    if (node.children && node.children.length > 0) {
      mergeUsersToChildren(node.children);
    }
  });
  return data;
}

export const dataSystemStore = defineStore("dataSystem", () => {
  const architectureInfo = useStorage<any>("architectureInfo", []);
  const architectureInfoTissue = useStorage<any>("architectureInfoTissue", []);
  // 添加缓存存储
  const keyValueCache = useStorage<Record<string, any[]>>("keyValueCache", {});

  /**
   * 获取组织列表
   */
  function getArchitectureInfo() {
    console.log("获取组织列表");
    return new Promise<void>((resolve, reject) => {
      dataSystemAPI
        .optionsAndUsers()
        .then(async (data: any) => {
          architectureInfo.value = data;
          architectureInfoTissue.value = await mergeUsersToChildren(data);

          resolve(architectureInfoTissue.value);
        })
        .catch((error) => {
          reject(error);
        });
    });
  }
  /**
   *
   * case_status 案池-失联查询结果
   * work_status 作业清单-失联查询结果
   * lost_status 添加跟进-失联查询结果
   * follow_status 添加跟进-催收结果
   * lock_status 锁定状态
   */
  function casepoolKeyValueList(code: any) {
    return new Promise<any[]>((resolve, reject) => {
      // 检查缓存
      if (keyValueCache.value[code]) {
        console.log(`从缓存获取 ${code} 数据`);
        resolve(keyValueCache.value[code]);
        return;
      }

      console.log(`从API获取 ${code} 数据`);
      dataSystemAPI
        .casepoolKeyValueList({ caseGroup: code })
        .then(async (data: any) => {
          let arr = data.map((e: any) => {
            return {
              value: e?.caseKey || "",
              label: e?.caseValue || "",
            };
          });
          // 存储到缓存
          keyValueCache.value[code] = arr;
          resolve(arr);
        })
        .catch((error) => {
          reject(error);
        });
    });
  }
  /**
   * 清理组织列表
   *
   * @returns
   */
  function clearArchitectureInfo() {
    return new Promise<void>((resolve) => {
      Object.assign(architectureInfo.value, {});
      Object.assign(architectureInfoTissue.value, {});
      resolve();
    });
  }

  /**
   * 清理键值对缓存
   */
  function clearKeyValueCache() {
    keyValueCache.value = {};
    console.log("键值对缓存已清除");
  }

  /**
   * 刷新指定类型的键值对数据
   */
  function refreshKeyValueList(code: string) {
    if (keyValueCache.value[code]) {
      delete keyValueCache.value[code];
      console.log(`${code} 缓存已清除，下次调用将重新获取`);
    }
  }

  return {
    architectureInfo,
    architectureInfoTissue,
    keyValueCache,
    getArchitectureInfo,
    clearArchitectureInfo,
    casepoolKeyValueList,
    clearKeyValueCache,
    refreshKeyValueList,
  };
});

export function useArchitectureStoreHook() {
  return dataSystemStore(store);
}
