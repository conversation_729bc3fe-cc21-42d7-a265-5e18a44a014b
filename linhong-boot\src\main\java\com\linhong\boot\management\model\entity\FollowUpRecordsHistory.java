package com.linhong.boot.management.model.entity;

import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;

public class FollowUpRecordsHistory {
	/**
	 *
	 * This field was generated by MyBatis Generator. This field corresponds to the
	 * database column follow_up_records_history.id
	 *
	 * @mbg.generated Thu Dec 26 15:31:06 CST 2024
	 */
	private Long id;

	/**
	 * 案件id
	 */
	private Long userId;

	/**
	 *
	 * This field was generated by MyBatis Generator. This field corresponds to the
	 * database column follow_up_records_history.called_number
	 *
	 * @mbg.generated Thu Dec 26 15:31:06 CST 2024
	 */
	private String calledNumber;

	/**
	 *
	 * This field was generated by MyBatis Generator. This field corresponds to the
	 * database column follow_up_records_history.caller_number
	 *
	 * @mbg.generated Thu Dec 26 15:31:06 CST 2024
	 */
	private String callerNumber;

	/**
	 *
	 * This field was generated by MyBatis Generator. This field corresponds to the
	 * database column follow_up_records_history.collection_result
	 *
	 * @mbg.generated Thu Dec 26 15:31:06 CST 2024
	 */
	private String collectionResult;

	/**
	 *
	 * This field was generated by MyBatis Generator. This field corresponds to the
	 * database column follow_up_records_history.missing_contact_result
	 *
	 * @mbg.generated Thu Dec 26 15:31:06 CST 2024
	 */
	private String missingContactResult;

	/**
	 *
	 * This field was generated by MyBatis Generator. This field corresponds to the
	 * database column follow_up_records_history.repayment_amount
	 *
	 * @mbg.generated Thu Dec 26 15:31:06 CST 2024
	 */
	private BigDecimal repaymentAmount;

	/**
	 *
	 * This field was generated by MyBatis Generator. This field corresponds to the
	 * database column follow_up_records_history.repayment_date
	 *
	 * @mbg.generated Thu Dec 26 15:31:06 CST 2024
	 */
	@JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
	private Date repaymentDate;
	
	private String collectionNotes;
	/**
	 *
	 * This field was generated by MyBatis Generator. This field corresponds to the
	 * database column follow_up_records_history.is_marked_for_change
	 *
	 * @mbg.generated Thu Dec 26 15:31:06 CST 2024
	 */
	private String isMarkedForChange;

	/**
	 *
	 * This field was generated by MyBatis Generator. This field corresponds to the
	 * database column follow_up_records_history.created_at
	 *
	 * @mbg.generated Thu Dec 26 15:31:06 CST 2024
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" , timezone = "GMT+8")
	private Date createdAt;

	/**
	 *
	 * This field was generated by MyBatis Generator. This field corresponds to the
	 * database column follow_up_records_history.updated_at
	 *
	 * @mbg.generated Thu Dec 26 15:31:06 CST 2024
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" , timezone = "GMT+8")
	private Date updatedAt;

	/**
	 *
	 * This field was generated by MyBatis Generator. This field corresponds to the
	 * database column follow_up_records_history.operation_id
	 *
	 * @mbg.generated Thu Dec 26 15:31:06 CST 2024
	 */
	private Long operationId;

	/**
	 *
	 * This field was generated by MyBatis Generator. This field corresponds to the
	 * database column follow_up_records_history.operation_name
	 *
	 * @mbg.generated Thu Dec 26 15:31:06 CST 2024
	 */
	private String operationName;

	/**
	 *
	 * This field was generated by MyBatis Generator. This field corresponds to the
	 * database column follow_up_records_history.unfollowed_days
	 *
	 * @mbg.generated Thu Dec 26 15:31:06 CST 2024
	 */
	private Integer unfollowedDays;

	@TableField(exist = false)
	private Integer sort;

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public Integer getSort() {
		return sort;
	}

	public void setSort(Integer sort) {
		this.sort = sort;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value
	 * of the database column follow_up_records_history.id
	 *
	 * @return the value of follow_up_records_history.id
	 *
	 * @mbg.generated Thu Dec 26 15:31:06 CST 2024
	 */
	public Long getId() {
		return id;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of
	 * the database column follow_up_records_history.id
	 *
	 * @param id the value for follow_up_records_history.id
	 *
	 * @mbg.generated Thu Dec 26 15:31:06 CST 2024
	 */
	public void setId(Long id) {
		this.id = id;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value
	 * of the database column follow_up_records_history.called_number
	 *
	 * @return the value of follow_up_records_history.called_number
	 *
	 * @mbg.generated Thu Dec 26 15:31:06 CST 2024
	 */
	public String getCalledNumber() {
		return calledNumber;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of
	 * the database column follow_up_records_history.called_number
	 *
	 * @param calledNumber the value for follow_up_records_history.called_number
	 *
	 * @mbg.generated Thu Dec 26 15:31:06 CST 2024
	 */
	public void setCalledNumber(String calledNumber) {
		this.calledNumber = calledNumber == null ? null : calledNumber.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value
	 * of the database column follow_up_records_history.caller_number
	 *
	 * @return the value of follow_up_records_history.caller_number
	 *
	 * @mbg.generated Thu Dec 26 15:31:06 CST 2024
	 */
	public String getCallerNumber() {
		return callerNumber;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of
	 * the database column follow_up_records_history.caller_number
	 *
	 * @param callerNumber the value for follow_up_records_history.caller_number
	 *
	 * @mbg.generated Thu Dec 26 15:31:06 CST 2024
	 */
	public void setCallerNumber(String callerNumber) {
		this.callerNumber = callerNumber == null ? null : callerNumber.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value
	 * of the database column follow_up_records_history.collection_result
	 *
	 * @return the value of follow_up_records_history.collection_result
	 *
	 * @mbg.generated Thu Dec 26 15:31:06 CST 2024
	 */
	public String getCollectionResult() {
		return collectionResult;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of
	 * the database column follow_up_records_history.collection_result
	 *
	 * @param collectionResult the value for
	 *                         follow_up_records_history.collection_result
	 *
	 * @mbg.generated Thu Dec 26 15:31:06 CST 2024
	 */
	public void setCollectionResult(String collectionResult) {
		this.collectionResult = collectionResult == null ? null : collectionResult.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value
	 * of the database column follow_up_records_history.missing_contact_result
	 *
	 * @return the value of follow_up_records_history.missing_contact_result
	 *
	 * @mbg.generated Thu Dec 26 15:31:06 CST 2024
	 */
	public String getMissingContactResult() {
		return missingContactResult;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of
	 * the database column follow_up_records_history.missing_contact_result
	 *
	 * @param missingContactResult the value for
	 *                             follow_up_records_history.missing_contact_result
	 *
	 * @mbg.generated Thu Dec 26 15:31:06 CST 2024
	 */
	public void setMissingContactResult(String missingContactResult) {
		this.missingContactResult = missingContactResult == null ? null : missingContactResult.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value
	 * of the database column follow_up_records_history.repayment_amount
	 *
	 * @return the value of follow_up_records_history.repayment_amount
	 *
	 * @mbg.generated Thu Dec 26 15:31:06 CST 2024
	 */
	public BigDecimal getRepaymentAmount() {
		return repaymentAmount;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of
	 * the database column follow_up_records_history.repayment_amount
	 *
	 * @param repaymentAmount the value for
	 *                        follow_up_records_history.repayment_amount
	 *
	 * @mbg.generated Thu Dec 26 15:31:06 CST 2024
	 */
	public void setRepaymentAmount(BigDecimal repaymentAmount) {
		this.repaymentAmount = repaymentAmount;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value
	 * of the database column follow_up_records_history.repayment_date
	 *
	 * @return the value of follow_up_records_history.repayment_date
	 *
	 * @mbg.generated Thu Dec 26 15:31:06 CST 2024
	 */
	public Date getRepaymentDate() {
		return repaymentDate;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of
	 * the database column follow_up_records_history.repayment_date
	 *
	 * @param repaymentDate the value for follow_up_records_history.repayment_date
	 *
	 * @mbg.generated Thu Dec 26 15:31:06 CST 2024
	 */
	public void setRepaymentDate(Date repaymentDate) {
		this.repaymentDate = repaymentDate;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value
	 * of the database column follow_up_records_history.is_marked_for_change
	 *
	 * @return the value of follow_up_records_history.is_marked_for_change
	 *
	 * @mbg.generated Thu Dec 26 15:31:06 CST 2024
	 */
	public String getIsMarkedForChange() {
		return isMarkedForChange;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of
	 * the database column follow_up_records_history.is_marked_for_change
	 *
	 * @param isMarkedForChange the value for
	 *                          follow_up_records_history.is_marked_for_change
	 *
	 * @mbg.generated Thu Dec 26 15:31:06 CST 2024
	 */
	public void setIsMarkedForChange(String isMarkedForChange) {
		this.isMarkedForChange = isMarkedForChange;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value
	 * of the database column follow_up_records_history.created_at
	 *
	 * @return the value of follow_up_records_history.created_at
	 *
	 * @mbg.generated Thu Dec 26 15:31:06 CST 2024
	 */
	public Date getCreatedAt() {
		return createdAt;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of
	 * the database column follow_up_records_history.created_at
	 *
	 * @param createdAt the value for follow_up_records_history.created_at
	 *
	 * @mbg.generated Thu Dec 26 15:31:06 CST 2024
	 */
	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value
	 * of the database column follow_up_records_history.updated_at
	 *
	 * @return the value of follow_up_records_history.updated_at
	 *
	 * @mbg.generated Thu Dec 26 15:31:06 CST 2024
	 */
	public Date getUpdatedAt() {
		return updatedAt;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of
	 * the database column follow_up_records_history.updated_at
	 *
	 * @param updatedAt the value for follow_up_records_history.updated_at
	 *
	 * @mbg.generated Thu Dec 26 15:31:06 CST 2024
	 */
	public void setUpdatedAt(Date updatedAt) {
		this.updatedAt = updatedAt;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value
	 * of the database column follow_up_records_history.operation_id
	 *
	 * @return the value of follow_up_records_history.operation_id
	 *
	 * @mbg.generated Thu Dec 26 15:31:06 CST 2024
	 */
	public Long getOperationId() {
		return operationId;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of
	 * the database column follow_up_records_history.operation_id
	 *
	 * @param operationId the value for follow_up_records_history.operation_id
	 *
	 * @mbg.generated Thu Dec 26 15:31:06 CST 2024
	 */
	public void setOperationId(Long operationId) {
		this.operationId = operationId;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value
	 * of the database column follow_up_records_history.operation_name
	 *
	 * @return the value of follow_up_records_history.operation_name
	 *
	 * @mbg.generated Thu Dec 26 15:31:06 CST 2024
	 */
	public String getOperationName() {
		return operationName;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of
	 * the database column follow_up_records_history.operation_name
	 *
	 * @param operationName the value for follow_up_records_history.operation_name
	 *
	 * @mbg.generated Thu Dec 26 15:31:06 CST 2024
	 */
	public void setOperationName(String operationName) {
		this.operationName = operationName == null ? null : operationName.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value
	 * of the database column follow_up_records_history.unfollowed_days
	 *
	 * @return the value of follow_up_records_history.unfollowed_days
	 *
	 * @mbg.generated Thu Dec 26 15:31:06 CST 2024
	 */
	public Integer getUnfollowedDays() {
		return unfollowedDays;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of
	 * the database column follow_up_records_history.unfollowed_days
	 *
	 * @param unfollowedDays the value for follow_up_records_history.unfollowed_days
	 *
	 * @mbg.generated Thu Dec 26 15:31:06 CST 2024
	 */
	public void setUnfollowedDays(Integer unfollowedDays) {
		this.unfollowedDays = unfollowedDays;
	}

	public String getCollectionNotes() {
		return collectionNotes;
	}

	public void setCollectionNotes(String collectionNotes) {
		this.collectionNotes = collectionNotes;
	}
	
}