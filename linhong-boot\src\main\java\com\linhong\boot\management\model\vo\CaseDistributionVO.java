package com.linhong.boot.management.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 案件分布数据传输对象
 */
@Data
@Schema(description = "案件分布数据")
public class CaseDistributionVO {

    @Schema(description = "类别名称（部门名或个人姓名）")
    private String category;

    @Schema(description = "案件数量")
    private Integer caseCount;

    @Schema(description = "委托总金额")
    private BigDecimal totalAmount;

    @Schema(description = "占比百分比")
    private Double percentage;

    @Schema(description = "显示颜色")
    private String color;
}
