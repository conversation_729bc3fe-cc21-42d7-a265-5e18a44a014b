package com.linhong.boot.management.model.dto;

import java.math.BigDecimal;
import java.util.Date;

import com.alibaba.excel.annotation.ExcelProperty;

import lombok.Data;


/**
 * 案件导入对象
 */
@Data
public class CcUserDTO {

//    /**
//     * 客户姓名
//     */
//	@ExcelProperty(value = "客户姓名")
//    private String customerName;
//	
//	/**
//     * 身份证
//     */
//	@ExcelProperty(value = "持卡人身份证号")
//    private String cardholderIdNumber;
//	
//	/**
//     * 持卡人代码
//     */
//	@ExcelProperty(value = "持卡人代码")
//    private String cardholderCode;
	
	/**
     * 客户索引号
     */
	@ExcelProperty(value = "客户索引号")
    private String customerIndexNumber;
	
	/**
     * 委托金额段 xxx-xxx
     */
	@ExcelProperty(value = "委托金额")
    private String entrustAmount;
	
	/**
	 * 委托总金额
	 */
	private BigDecimal entrustTotalAmount;
	
	/**
	 * 委托本金
	 */
	@ExcelProperty(value = "委托本金")
	private String entrustPrincipal;
	
	/**
	 * 委托本金总额
	 */
	private BigDecimal entrustPrincipalTotal;
	
	/**
	 * 委托时逾期时段
	 */
	@ExcelProperty(value = "委托时逾期时段")
	private String entrustOverduePeriod;
	/**
	 * 目标时段
	 */
	@ExcelProperty(value = "目标时段")
	private String targetPeriod;
	/**
	 * 分案城市
	 */
	@ExcelProperty(value = "分案城市")
	private String caseCity;
	/**
	 *最后跟进日期
	 */
	@ExcelProperty(value = "最后跟进日期")
	private Date lastFollowUpDate;
	/**
	 * 委托开始日
	 */
	@ExcelProperty(value = "委托开始日")
	private Date entrustStartDate;
	/**
	 * 委托结束日
	 */
	@ExcelProperty(value = "委托结束日")
	private Date entrustEndDate;
	/**
	 * 案件类型
	 */
	@ExcelProperty(value = "案件类型")
	private String caseType;
	/**
	 * 新旧案标志
	 */
	@ExcelProperty(value = "新旧案标志")
	private String newOldCaseFlag;
	/**
	 * 批次号
	 */
	@ExcelProperty(value = "批次号")
	private String batchNumber;
	/**
	 * 余额OPS
	 */
	@ExcelProperty(value = "余额OPS")
	private BigDecimal balanceOps;
	/**
	 * 本金OPS
	 */
	@ExcelProperty(value = "本金OPS")
	private BigDecimal principalOps;
	/**
	 * 当前逾期时段
	 */
	@ExcelProperty(value = "当前逾期时段")
	private String currentOverduePeriod;
	/**
	 * 户籍城市
	 */
	@ExcelProperty(value = "户籍城市")
	private String householdCity;
	/**
	 * 客户年龄
	 */
	@ExcelProperty(value = "客户年龄")
	private Integer customerAge;
	/**
	 * 职业
	 */
	@ExcelProperty(value = "职业")
	private String occupation;
	/**
	 * 开卡日期
	 */
	@ExcelProperty(value = "开卡日期")
	private Date cardOpeningDate;
	/**
	 *信用额度
	 */
	@ExcelProperty(value = "信用额度")
	private BigDecimal creditLimit;
	/**
	 * 最后缴款日期
	 */
	@ExcelProperty(value = "最后缴款日期") 
	private Date lastPaymentDate;
	/**
	 * 技能组
	 */
	@ExcelProperty(value = "技能组")
	private String skillGroup;
	/**
	 * 账号后七位
	 */
	@ExcelProperty(value = "账户号后7位")
	private String accountNumberLast7;
	/**
	 * 委托时月龄分档
	 */
	@ExcelProperty(value = "委托时月龄分档")
	private String entrustAgeBracket;
	/**
	 * 委托时金额段
	 */
	@ExcelProperty(value = "委托时金额段")
	private String entrustAmountBracket;
	/**
	 * 委托时佣金分档
	 */
	@ExcelProperty(value = "委托时佣金分档")
	private String entrustCommissionBracket;
	/**
	 * 评分档
	 */
	@ExcelProperty(value = "评分档")
	private String ratingBracket;
	
	/**
	 * 是否诉讼(含风险代理)
	 */
	@ExcelProperty(value = "是否诉讼(含风险代理)")
	private String isLitigation;
	
	/**
	 * 客户当月主动还款金额
	 */
	@ExcelProperty(value = "客户当月主动还款金额")
	private BigDecimal customerCurrentMonthRepayment;

	/**
	 * 客户当日主动还款金额
	 */
	@ExcelProperty(value = "客户前日主动还款金额")
	private BigDecimal customerPreviousDayRepayment;
	
	/**
	 * 抢案用户
	 */
	@ExcelProperty(value = "抢案用户")
	private String caseGrabbingUser;

	/**
	 * 专项类型
	 */
	@ExcelProperty(value = "专项类型")
	private String specialProjectType;

	/**
	 * 保留次数
	 */
	@ExcelProperty(value = "保留次数")
	private Integer retentionCount;

	/**
	 * 个性化分期状态
	 */
	@ExcelProperty(value = "个性化分期状态")
	private String personalizedInstallmentStatus;

	/**
	 * 个性化分期履约状态
	 */
	@ExcelProperty(value = "个性化分期履约状态")
	private String personalizedInstallmentFulfillmentStatus;

	/**
	 * 投诉标签
	 */
	@ExcelProperty(value = "投诉标签")
	private String complaintLabel;

	/**
	 * 个分履约标签
	 */
	@ExcelProperty(value = "个分履约标签")
	private String personalizedFulfillmentLabel;

	/**
	 * 诉讼标签
	 */
	@ExcelProperty(value = "诉讼标签")
	private String litigationLabel;

	/**
	 * 智能语音标签
	 */
	@ExcelProperty(value = "智能语音标签")
	private String smartVoiceLabel;
	
	/**
	 * 专项标签
	 */
	@ExcelProperty(value = "专项标签")
	private String specialProjectLabel;

	/**
	 * 是否诉讼(结佣)
	 */
	@ExcelProperty(value = "是否诉讼(结佣)")
	private String isLitigationForCommission;

    
}