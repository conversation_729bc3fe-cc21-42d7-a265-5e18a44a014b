package com.linhong.boot.management.model.entity;

import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;


public class RepaymentRecords {
	
	@TableField(exist = false)
	private BigDecimal SumRepaymentAmount;

	@TableField(exist = false)
	private BigDecimal entrustTotalAmount;

	@TableField(exist = false)
	private BigDecimal recoveryRate;
	
	

	public BigDecimal getSumRepaymentAmount() {
		return SumRepaymentAmount;
	}

	public void setSumRepaymentAmount(BigDecimal sumRepaymentAmount) {
		SumRepaymentAmount = sumRepaymentAmount;
	}

	public BigDecimal getEntrustTotalAmount() {
		return entrustTotalAmount;
	}

	public void setEntrustTotalAmount(BigDecimal entrustTotalAmount) {
		this.entrustTotalAmount = entrustTotalAmount;
	}

	public BigDecimal getRecoveryRate() {
		return recoveryRate;
	}

	public void setRecoveryRate(BigDecimal recoveryRate) {
		this.recoveryRate = recoveryRate;
	}

	/**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column repayment_records.id
     *
     * @mbg.generated Wed Dec 04 09:54:44 CST 2024
     */
    private Integer id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column repayment_records.cc_id
     *
     * @mbg.generated Wed Dec 04 09:54:44 CST 2024
     */
    private Long ccId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column repayment_records.outsource_serial_number
     *
     * @mbg.generated Wed Dec 04 09:54:44 CST 2024
     */
    private String outsourceSerialNumber;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column repayment_records.batch_number
     *
     * @mbg.generated Wed Dec 04 09:54:44 CST 2024
     */
    private String batchNumber;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column repayment_records.cardholder_code
     *
     * @mbg.generated Wed Dec 04 09:54:44 CST 2024
     */
    private String cardholderCode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column repayment_records.customer_name
     *
     * @mbg.generated Wed Dec 04 09:54:44 CST 2024
     */
    private String customerName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column repayment_records.customer_index_number
     *
     * @mbg.generated Wed Dec 04 09:54:44 CST 2024
     */
    private String customerIndexNumber;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column repayment_records.case_type
     *
     * @mbg.generated Wed Dec 04 09:54:44 CST 2024
     */
    private String caseType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column repayment_records.repayment_amount
     *
     * @mbg.generated Wed Dec 04 09:54:44 CST 2024
     */
    private BigDecimal repaymentAmount;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column repayment_records.repayment_date
     *
     * @mbg.generated Wed Dec 04 09:54:44 CST 2024
     */
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date repaymentDate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column repayment_records.dept_name
     *
     * @mbg.generated Wed Dec 04 09:54:44 CST 2024
     */
    private String deptName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column repayment_records.operator_id
     *
     * @mbg.generated Wed Dec 04 09:54:44 CST 2024
     */
    private String operatorId;
    
    private String isDelete;


    

    public String getIsDelete() {
		return isDelete;
	}

	public void setIsDelete(String isDelete) {
		this.isDelete = isDelete;
	}

	/**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column repayment_records.id
     *
     * @return the value of repayment_records.id
     *
     * @mbg.generated Wed Dec 04 09:54:44 CST 2024
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column repayment_records.id
     *
     * @param id the value for repayment_records.id
     *
     * @mbg.generated Wed Dec 04 09:54:44 CST 2024
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column repayment_records.cc_id
     *
     * @return the value of repayment_records.cc_id
     *
     * @mbg.generated Wed Dec 04 09:54:44 CST 2024
     */
    public Long getCcId() {
        return ccId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column repayment_records.cc_id
     *
     * @param ccId the value for repayment_records.cc_id
     *
     * @mbg.generated Wed Dec 04 09:54:44 CST 2024
     */
    public void setCcId(Long ccId) {
        this.ccId = ccId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column repayment_records.outsource_serial_number
     *
     * @return the value of repayment_records.outsource_serial_number
     *
     * @mbg.generated Wed Dec 04 09:54:44 CST 2024
     */
    public String getOutsourceSerialNumber() {
        return outsourceSerialNumber;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column repayment_records.outsource_serial_number
     *
     * @param outsourceSerialNumber the value for repayment_records.outsource_serial_number
     *
     * @mbg.generated Wed Dec 04 09:54:44 CST 2024
     */
    public void setOutsourceSerialNumber(String outsourceSerialNumber) {
        this.outsourceSerialNumber = outsourceSerialNumber == null ? null : outsourceSerialNumber.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column repayment_records.batch_number
     *
     * @return the value of repayment_records.batch_number
     *
     * @mbg.generated Wed Dec 04 09:54:44 CST 2024
     */
    public String getBatchNumber() {
        return batchNumber;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column repayment_records.batch_number
     *
     * @param batchNumber the value for repayment_records.batch_number
     *
     * @mbg.generated Wed Dec 04 09:54:44 CST 2024
     */
    public void setBatchNumber(String batchNumber) {
        this.batchNumber = batchNumber == null ? null : batchNumber.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column repayment_records.cardholder_code
     *
     * @return the value of repayment_records.cardholder_code
     *
     * @mbg.generated Wed Dec 04 09:54:44 CST 2024
     */
    public String getCardholderCode() {
        return cardholderCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column repayment_records.cardholder_code
     *
     * @param cardholderCode the value for repayment_records.cardholder_code
     *
     * @mbg.generated Wed Dec 04 09:54:44 CST 2024
     */
    public void setCardholderCode(String cardholderCode) {
        this.cardholderCode = cardholderCode == null ? null : cardholderCode.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column repayment_records.customer_name
     *
     * @return the value of repayment_records.customer_name
     *
     * @mbg.generated Wed Dec 04 09:54:44 CST 2024
     */
    public String getCustomerName() {
        return customerName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column repayment_records.customer_name
     *
     * @param customerName the value for repayment_records.customer_name
     *
     * @mbg.generated Wed Dec 04 09:54:44 CST 2024
     */
    public void setCustomerName(String customerName) {
        this.customerName = customerName == null ? null : customerName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column repayment_records.customer_index_number
     *
     * @return the value of repayment_records.customer_index_number
     *
     * @mbg.generated Wed Dec 04 09:54:44 CST 2024
     */
    public String getCustomerIndexNumber() {
        return customerIndexNumber;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column repayment_records.customer_index_number
     *
     * @param customerIndexNumber the value for repayment_records.customer_index_number
     *
     * @mbg.generated Wed Dec 04 09:54:44 CST 2024
     */
    public void setCustomerIndexNumber(String customerIndexNumber) {
        this.customerIndexNumber = customerIndexNumber == null ? null : customerIndexNumber.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column repayment_records.case_type
     *
     * @return the value of repayment_records.case_type
     *
     * @mbg.generated Wed Dec 04 09:54:44 CST 2024
     */
    public String getCaseType() {
        return caseType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column repayment_records.case_type
     *
     * @param caseType the value for repayment_records.case_type
     *
     * @mbg.generated Wed Dec 04 09:54:44 CST 2024
     */
    public void setCaseType(String caseType) {
        this.caseType = caseType == null ? null : caseType.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column repayment_records.repayment_amount
     *
     * @return the value of repayment_records.repayment_amount
     *
     * @mbg.generated Wed Dec 04 09:54:44 CST 2024
     */
    public BigDecimal getRepaymentAmount() {
        return repaymentAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column repayment_records.repayment_amount
     *
     * @param repaymentAmount the value for repayment_records.repayment_amount
     *
     * @mbg.generated Wed Dec 04 09:54:44 CST 2024
     */
    public void setRepaymentAmount(BigDecimal repaymentAmount) {
        this.repaymentAmount = repaymentAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column repayment_records.repayment_date
     *
     * @return the value of repayment_records.repayment_date
     *
     * @mbg.generated Wed Dec 04 09:54:44 CST 2024
     */
    public Date getRepaymentDate() {
        return repaymentDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column repayment_records.repayment_date
     *
     * @param repaymentDate the value for repayment_records.repayment_date
     *
     * @mbg.generated Wed Dec 04 09:54:44 CST 2024
     */
    public void setRepaymentDate(Date repaymentDate) {
        this.repaymentDate = repaymentDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column repayment_records.dept_name
     *
     * @return the value of repayment_records.dept_name
     *
     * @mbg.generated Wed Dec 04 09:54:44 CST 2024
     */
    public String getDeptName() {
        return deptName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column repayment_records.dept_name
     *
     * @param deptName the value for repayment_records.dept_name
     *
     * @mbg.generated Wed Dec 04 09:54:44 CST 2024
     */
    public void setDeptName(String deptName) {
        this.deptName = deptName == null ? null : deptName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column repayment_records.operator_id
     *
     * @return the value of repayment_records.operator_id
     *
     * @mbg.generated Wed Dec 04 09:54:44 CST 2024
     */
    public String getOperatorId() {
        return operatorId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column repayment_records.operator_id
     *
     * @param operatorId the value for repayment_records.operator_id
     *
     * @mbg.generated Wed Dec 04 09:54:44 CST 2024
     */
    public void setOperatorId(String operatorId) {
        this.operatorId = operatorId == null ? null : operatorId.trim();
    }

   
}