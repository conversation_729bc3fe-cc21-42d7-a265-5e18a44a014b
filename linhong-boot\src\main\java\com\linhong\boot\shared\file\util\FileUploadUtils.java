package com.linhong.boot.shared.file.util;

import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.util.Arrays;
import java.util.List;

/**
 * 文件上传工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class FileUploadUtils {

    /**
     * 允许的图片类型
     */
    public static final List<String> ALLOWED_IMAGE_TYPES = Arrays.asList(
        "image/png", 
        "image/jpeg", 
        "image/jpg", 
        "image/svg+xml"
    );

    /**
     * 最大文件大小 (2MB)
     */
    public static final long MAX_FILE_SIZE = 2 * 1024 * 1024;

    /**
     * 规范化文件路径，适配不同操作系统
     *
     * @param path 原始路径
     * @return 规范化后的路径
     */
    public static String normalizePath(String path) {
        if (path == null || path.isEmpty()) {
            return path;
        }
        
        // 将所有路径分隔符统一为当前系统的分隔符
        return path.replace("\\", File.separator).replace("/", File.separator);
    }

    /**
     * 确保目录存在，如果不存在则创建
     *
     * @param dirPath 目录路径
     * @return 是否成功
     */
    public static boolean ensureDirectoryExists(String dirPath) {
        File dir = new File(dirPath);
        if (!dir.exists()) {
            boolean created = dir.mkdirs();
            if (created) {
                log.info("成功创建目录: {}", dirPath);
            } else {
                log.error("创建目录失败: {}", dirPath);
            }
            return created;
        }
        return true;
    }

    /**
     * 检查文件类型是否允许
     *
     * @param contentType 文件类型
     * @return 是否允许
     */
    public static boolean isAllowedImageType(String contentType) {
        return contentType != null && ALLOWED_IMAGE_TYPES.contains(contentType.toLowerCase());
    }

    /**
     * 检查文件大小是否超限
     *
     * @param fileSize 文件大小
     * @return 是否超限
     */
    public static boolean isFileSizeExceeded(long fileSize) {
        return fileSize > MAX_FILE_SIZE;
    }

    /**
     * 获取文件大小的可读格式
     *
     * @param size 文件大小（字节）
     * @return 可读格式的大小
     */
    public static String getReadableFileSize(long size) {
        if (size <= 0) return "0 B";
        
        final String[] units = new String[]{"B", "KB", "MB", "GB", "TB"};
        int digitGroups = (int) (Math.log10(size) / Math.log10(1024));
        
        return String.format("%.1f %s", 
            size / Math.pow(1024, digitGroups), 
            units[digitGroups]);
    }

    /**
     * 检查目录权限
     *
     * @param dirPath 目录路径
     * @return 权限检查结果
     */
    public static DirectoryPermission checkDirectoryPermission(String dirPath) {
        File dir = new File(dirPath);
        
        DirectoryPermission permission = new DirectoryPermission();
        permission.setExists(dir.exists());
        permission.setReadable(dir.canRead());
        permission.setWritable(dir.canWrite());
        permission.setExecutable(dir.canExecute());
        
        return permission;
    }

    /**
     * 目录权限信息
     */
    public static class DirectoryPermission {
        private boolean exists;
        private boolean readable;
        private boolean writable;
        private boolean executable;

        public boolean isExists() { return exists; }
        public void setExists(boolean exists) { this.exists = exists; }
        
        public boolean isReadable() { return readable; }
        public void setReadable(boolean readable) { this.readable = readable; }
        
        public boolean isWritable() { return writable; }
        public void setWritable(boolean writable) { this.writable = writable; }
        
        public boolean isExecutable() { return executable; }
        public void setExecutable(boolean executable) { this.executable = executable; }
        
        public boolean isValid() {
            return exists && readable && writable;
        }
        
        @Override
        public String toString() {
            return String.format("DirectoryPermission{exists=%s, readable=%s, writable=%s, executable=%s}", 
                exists, readable, writable, executable);
        }
    }
}
