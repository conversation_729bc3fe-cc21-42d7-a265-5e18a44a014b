package com.linhong.boot.management.model.vo;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Data;

@Data
public class TransferRecordsVO {

	private Long id;

	private String sourceOrganization;

	private String targetOrganization;

	private String sourceUser;

	private String targetUser;

	private String assignmentType;

	private String operator;
	
	private String userOrDept;

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" ,timezone = "GMT+8")
	private LocalDateTime operationTime;
	private String customerName;
	private String customerIndexNumber;
	private String outsourceSerialNumber;
	@TableField(exist = false)
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" ,timezone = "GMT+8")
	private LocalDateTime huanTime;
	@TableField(exist = false)
	private String jinNum;
	@TableField(exist = false)
	private String chuNum;
	@TableField(exist=false)
	private String assType;
	@TableField(exist=false)
	private String huanType;
	
	
}