package com.linhong.boot.management.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.linhong.boot.management.model.entity.KeyValueStore;
import com.linhong.boot.management.model.entity.KeyValueStoreExample;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface KeyValueStoreMapper extends BaseMapper<KeyValueStore>{
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table key_value_store
     *
     * @mbg.generated Fri Dec 06 14:42:34 CST 2024
     */
    long countByExample(KeyValueStoreExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table key_value_store
     *
     * @mbg.generated Fri Dec 06 14:42:34 CST 2024
     */
    int deleteByExample(KeyValueStoreExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table key_value_store
     *
     * @mbg.generated Fri Dec 06 14:42:34 CST 2024
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table key_value_store
     *
     * @mbg.generated Fri Dec 06 14:42:34 CST 2024
     */
    int insert(KeyValueStore row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table key_value_store
     *
     * @mbg.generated Fri Dec 06 14:42:34 CST 2024
     */
    int insertSelective(KeyValueStore row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table key_value_store
     *
     * @mbg.generated Fri Dec 06 14:42:34 CST 2024
     */
    List<KeyValueStore> selectByExample(KeyValueStoreExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table key_value_store
     *
     * @mbg.generated Fri Dec 06 14:42:34 CST 2024
     */
    KeyValueStore selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table key_value_store
     *
     * @mbg.generated Fri Dec 06 14:42:34 CST 2024
     */
    int updateByExampleSelective(@Param("row") KeyValueStore row, @Param("example") KeyValueStoreExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table key_value_store
     *
     * @mbg.generated Fri Dec 06 14:42:34 CST 2024
     */
    int updateByExample(@Param("row") KeyValueStore row, @Param("example") KeyValueStoreExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table key_value_store
     *
     * @mbg.generated Fri Dec 06 14:42:34 CST 2024
     */
    int updateByPrimaryKeySelective(KeyValueStore row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table key_value_store
     *
     * @mbg.generated Fri Dec 06 14:42:34 CST 2024
     */
    int updateByPrimaryKey(KeyValueStore row);
}