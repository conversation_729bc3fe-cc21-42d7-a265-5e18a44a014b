<template>
  <div class="repayment-trend-card">
    <div class="card-header">
      <div class="header-left">
        <div class="trend-icon">📈</div>
        <span class="header-title">回款趋势</span>
      </div>
      <div class="header-right">
        <div class="period-buttons">
          <button
            class="period-btn"
            :class="{ active: period === 'amount' }"
            @click="changePeriod('amount')"
          >
            金额
          </button>
          <button
            class="period-btn"
            :class="{ active: period === 'count' }"
            @click="changePeriod('count')"
          >
            户数
          </button>
          <div class="date-selector">
            <el-select
              v-model="selectedDays"
              placeholder="选择时间范围"
              popper-class="date-select-dropdown"
              @change="handleDaysChange"
            >
              <el-option
                v-for="item in dayOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </div>
        </div>
      </div>
    </div>

    <div class="chart-container">
      <div ref="chartRef" class="chart"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from "vue";
import * as echarts from "echarts";

interface ChartData {
  xAxis?: string[];  // 前端使用的字段名
  xaxis?: string[];  // 后端返回的字段名
  dates?: string[];
  series: {
    name: string;
    data: number[];
    type: string;
    color?: string;
  }[];
}

interface Props {
  chartData: ChartData;
  loading?: boolean;
  height?: string;
}

const props = withDefaults(defineProps<Props>(), {
  height: "400px",
});

const emit = defineEmits<{
  periodChange: [period: "amount" | "count" | "recent"];
  daysChange: [days: number];
}>();

const chartRef = ref<HTMLElement>();
const chart = ref<echarts.ECharts>();
const period = ref<"amount" | "count" | "recent">("amount");

// 时间范围选择相关
const selectedDays = ref<number>(7);
const dayOptions = [
  { label: "近7天", value: 7 },
  { label: "近30天", value: 30 }
];

const chartHeight = computed(() => props.height);

const changePeriod = (newPeriod: "amount" | "count" | "recent") => {
  period.value = newPeriod;
  emit("periodChange", newPeriod);
};

const handleDaysChange = (days: number) => {
  selectedDays.value = days;
  emit("daysChange", days);
};

const initChart = () => {
  if (!chartRef.value) return;

  chart.value = echarts.init(chartRef.value);
  updateChart();
};

const updateChart = () => {
  if (!chart.value || !props.chartData) return;

  const option = {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "cross",
        label: {
          backgroundColor: "#6a7985",
        },
      },
      formatter: (params: any) => {
        let result = `<div style="margin-bottom: 4px;">${params[0].axisValue}</div>`;
        params.forEach((param: any) => {
          const isAmount = param.seriesName.includes("金额");
          const isCount = param.seriesName.includes("户数");
          let valueStr = "";

          if (isAmount) {
            valueStr = `${param.value}万元`;
          } else if (isCount) {
            valueStr = `${param.value}户`;
          } else {
            valueStr = param.value.toLocaleString();
          }

          result += `
            <div style="display: flex; align-items: center; margin-bottom: 2px;">
              <span style="display: inline-block; width: 10px; height: 10px; background-color: ${param.color}; border-radius: 50%; margin-right: 8px;"></span>
              <span style="margin-right: 8px;">${param.seriesName}:</span>
              <span style="font-weight: bold;">${valueStr}</span>
            </div>
          `;
        });
        return result;
      },
    },
    legend: {
      data: props.chartData.series?.map((s) => s.name) || [],
      top: 5,
      left: "center",
      textStyle: {
        color: "rgba(255, 255, 255, 0.9)",
        fontSize: 12,
      },
    },
    grid: {
      left: "5%",
      right: "5%",
      bottom: "10%",
      top: "15%",
      containLabel: true,
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: props.chartData.xAxis || props.chartData.xaxis || [],
      axisLine: {
        lineStyle: {
          color: "rgba(255, 255, 255, 0.3)",
        },
      },
      axisLabel: {
        color: "rgba(255, 255, 255, 0.8)",
        fontSize: 11,
      },
    },
    yAxis: {
      type: "value",
      axisLine: {
        lineStyle: {
          color: "rgba(255, 255, 255, 0.3)",
        },
      },
      axisLabel: {
        color: "rgba(255, 255, 255, 0.8)",
        fontSize: 11,
        formatter: (value: number) => {
          if (value >= 10000) {
            return (value / 10000).toFixed(1) + "W";
          }
          return value.toLocaleString();
        },
      },
      splitLine: {
        lineStyle: {
          color: "rgba(255, 255, 255, 0.2)",
          type: "dashed",
        },
      },
    },
    series:
      props.chartData.series?.map((series, index) => {
        const colors = ["#fbbf24", "#4080ff", "#23c343", "#ff9a2e"];
        const color = series.color || colors[index % colors.length];

        return {
          name: series.name,
          type: "line",
          smooth: true,
          data: series.data,
          lineStyle: {
            color: color,
            width: 3,
          },
          itemStyle: {
            color: color,
          },
          areaStyle: {
            color: {
              type: "linear",
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: color + "40",
                },
                {
                  offset: 1,
                  color: color + "10",
                },
              ],
            },
          },
          emphasis: {
            focus: "series",
          },
        };
      }) || [],
  };

  chart.value.setOption(option);
};

// 防抖处理的 resize 函数
let resizeTimer: NodeJS.Timeout | null = null;
const resizeChart = () => {
  if (resizeTimer) {
    clearTimeout(resizeTimer);
  }
  resizeTimer = setTimeout(() => {
    if (chart.value && chartRef.value) {
      chart.value.resize();
    }
  }, 100);
};

// ResizeObserver 实例
let resizeObserver: ResizeObserver | null = null;

onMounted(() => {
  nextTick(() => {
    initChart();

    // 监听窗口大小变化
    window.addEventListener("resize", resizeChart);

    // 监听容器大小变化
    if (chartRef.value && window.ResizeObserver) {
      resizeObserver = new ResizeObserver(() => {
        resizeChart();
      });
      resizeObserver.observe(chartRef.value);
    }
  });
});

onUnmounted(() => {
  // 清理定时器
  if (resizeTimer) {
    clearTimeout(resizeTimer);
  }

  // 清理 ResizeObserver
  if (resizeObserver) {
    resizeObserver.disconnect();
    resizeObserver = null;
  }

  // 清理事件监听器
  window.removeEventListener("resize", resizeChart);

  // 销毁图表实例
  if (chart.value) {
    chart.value.dispose();
    chart.value = undefined;
  }
});

watch(
  () => props.chartData,
  () => {
    updateChart();
  },
  { deep: true }
);

watch(
  () => props.loading,
  (loading) => {
    if (chart.value) {
      if (loading) {
        chart.value.showLoading();
      } else {
        chart.value.hideLoading();
      }
    }
  }
);
</script>

<style lang="scss" scoped>
.repayment-trend-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(15px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  padding: 20px;
  height: 40vh; /* 固定高度为视口高度的40%，适合图表显示 */
  min-height: 350px; /* 最小高度确保图表可见 */
  max-height: 600px; /* 最大高度防止过大 */
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-sizing: border-box;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-shrink: 0;

  .header-left {
    display: flex;
    align-items: center;
    gap: 8px;

    .trend-icon {
      font-size: 18px;
    }

    .header-title {
      font-size: 16px;
      font-weight: 600;
      color: rgba(255, 255, 255, 0.95);
    }
  }

  .header-right {
    .period-buttons {
      display: flex;
      gap: 0;
      align-items: center;

      .period-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 8px 16px;
        border: none;
        background: rgba(255, 255, 255, 0.15);
        color: rgba(255, 255, 255, 0.8);
        font-size: 13px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
        border-radius: 6px;
        margin: 0 2px;

        &:hover {
          background: rgba(255, 255, 255, 0.2);
          color: rgba(255, 255, 255, 0.95);
        }

        &.active {
          padding: 10px 20px;
          font-size: 14px;
          font-weight: 600;
          color: white;
          border-radius: 8px;
          margin: 0;
          background: #fbbf24;
        }
      }

      .date-selector {
        margin-left: 8px;

        :deep(.el-select) {
          width: 100px;

          .el-input {
            .el-input__wrapper {
              background: rgba(255, 255, 255, 0.15);
              border: 1px solid rgba(255, 255, 255, 0.3);
              border-radius: 6px;
              box-shadow: none;
              transition: all 0.3s ease;

              &:hover {
                background: rgba(255, 255, 255, 0.2);
                border-color: rgba(255, 255, 255, 0.5);
              }

              &.is-focus {
                background: rgba(255, 255, 255, 0.2);
                border-color: #fbbf24;
                box-shadow: 0 0 0 2px rgba(251, 191, 36, 0.2);
              }

              .el-input__inner {
                color: rgba(255, 255, 255, 0.9);
                font-size: 13px;
                font-weight: 500;

                &::placeholder {
                  color: rgba(255, 255, 255, 0.6);
                }
              }

              .el-input__suffix {
                .el-input__suffix-inner {
                  .el-select__caret {
                    color: rgba(255, 255, 255, 0.7);

                    &:hover {
                      color: rgba(255, 255, 255, 0.9);
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}

.chart-container {
  flex: 1;
  min-height: 0;
  overflow: hidden;
  position: relative;

  .chart {
    width: 100% !important;
    height: 100% !important;
    min-height: 280px;
    max-width: 100%;
    max-height: 100%;
  }
}

@media (max-width: 768px) {
  .repayment-trend-card {
    height: 50vh;
    min-height: 300px;
    padding: 16px;
  }

  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .header-right .control-buttons {
    flex-wrap: wrap;
    gap: 8px;

    .date-selector {
      margin-left: 0;
    }
  }

  .chart-container .chart {
    min-height: 200px;
  }
}

@media (max-width: 480px) {
  .repayment-trend-card {
    height: 45vh;
    min-height: 250px;
    padding: 12px;
  }

  .chart-container .chart {
    min-height: 180px;
  }
}
</style>

<style lang="scss">
// 全局样式：下拉选择器弹出层
.date-select-dropdown {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.9)) !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  border-radius: 8px !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15) !important;
  backdrop-filter: blur(20px) !important;

  .el-select-dropdown__item {
    color: rgba(0, 0, 0, 0.8) !important;
    font-size: 13px !important;
    font-weight: 500 !important;
    padding: 8px 12px !important;
    transition: all 0.2s ease !important;

    &:hover {
      background: rgba(251, 191, 36, 0.1) !important;
      color: rgba(0, 0, 0, 0.9) !important;
    }

    &.selected {
      background: #fbbf24 !important;
      color: white !important;
      font-weight: 600 !important;
    }
  }
}
</style>
