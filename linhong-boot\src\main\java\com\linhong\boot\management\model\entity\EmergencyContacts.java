package com.linhong.boot.management.model.entity;

import java.util.Date;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 紧急联系人表
 */
@Schema(description = "紧急联系人")
public class EmergencyContacts {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column emergency_contacts.id
     *
     * @mbg.generated Tue Nov 26 07:16:48 HKT 2024
     */
	@Schema(description = "紧急联系人ID")
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column emergency_contacts.user_id
     *
     * @mbg.generated Tue Nov 26 07:16:48 HKT 2024
     */
	@Schema(description = "案件ID")
    private Long userId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column emergency_contacts.name
     *
     * @mbg.generated Tue Nov 26 07:16:48 HKT 2024
     */
	@Schema(description = "联系人姓名")
    private String name;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column emergency_contacts.phone
     *
     * @mbg.generated Tue Nov 26 07:16:48 HKT 2024
     */
	@Schema(description = "联系人电话")
    private String phone;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column emergency_contacts.email
     *
     * @mbg.generated Tue Nov 26 07:16:48 HKT 2024
     */
	@Schema(description = "联系人邮箱")
    private String email;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column emergency_contacts.relationship
     *
     * @mbg.generated Tue Nov 26 07:16:48 HKT 2024
     */
	@Schema(description = "联系人和用户关系")
    private String relationship;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column emergency_contacts.is_primary
     *
     * @mbg.generated Tue Nov 26 07:16:48 HKT 2024
     */
	@Schema(description = "该联系人是否为主要紧急联系人0:不是 1:是 , 默认:0")
    private Boolean isPrimary;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column emergency_contacts.created_at
     *
     * @mbg.generated Tue Nov 26 07:16:48 HKT 2024
     */
	@Schema(description = "创建时间")
    private Date createdAt;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column emergency_contacts.updated_at
     *
     * @mbg.generated Tue Nov 26 07:16:48 HKT 2024
     */
	@Schema(description = "修改时间")
    private Date updatedAt;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column emergency_contacts.id
     *
     * @return the value of emergency_contacts.id
     *
     * @mbg.generated Tue Nov 26 07:16:48 HKT 2024
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column emergency_contacts.id
     *
     * @param id the value for emergency_contacts.id
     *
     * @mbg.generated Tue Nov 26 07:16:48 HKT 2024
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column emergency_contacts.user_id
     *
     * @return the value of emergency_contacts.user_id
     *
     * @mbg.generated Tue Nov 26 07:16:48 HKT 2024
     */
    public Long getUserId() {
        return userId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column emergency_contacts.user_id
     *
     * @param userId the value for emergency_contacts.user_id
     *
     * @mbg.generated Tue Nov 26 07:16:48 HKT 2024
     */
    public void setUserId(Long userId) {
        this.userId = userId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column emergency_contacts.name
     *
     * @return the value of emergency_contacts.name
     *
     * @mbg.generated Tue Nov 26 07:16:48 HKT 2024
     */
    public String getName() {
        return name;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column emergency_contacts.name
     *
     * @param name the value for emergency_contacts.name
     *
     * @mbg.generated Tue Nov 26 07:16:48 HKT 2024
     */
    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column emergency_contacts.phone
     *
     * @return the value of emergency_contacts.phone
     *
     * @mbg.generated Tue Nov 26 07:16:48 HKT 2024
     */
    public String getPhone() {
        return phone;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column emergency_contacts.phone
     *
     * @param phone the value for emergency_contacts.phone
     *
     * @mbg.generated Tue Nov 26 07:16:48 HKT 2024
     */
    public void setPhone(String phone) {
        this.phone = phone == null ? null : phone.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column emergency_contacts.email
     *
     * @return the value of emergency_contacts.email
     *
     * @mbg.generated Tue Nov 26 07:16:48 HKT 2024
     */
    public String getEmail() {
        return email;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column emergency_contacts.email
     *
     * @param email the value for emergency_contacts.email
     *
     * @mbg.generated Tue Nov 26 07:16:48 HKT 2024
     */
    public void setEmail(String email) {
        this.email = email == null ? null : email.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column emergency_contacts.relationship
     *
     * @return the value of emergency_contacts.relationship
     *
     * @mbg.generated Tue Nov 26 07:16:48 HKT 2024
     */
    public String getRelationship() {
        return relationship;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column emergency_contacts.relationship
     *
     * @param relationship the value for emergency_contacts.relationship
     *
     * @mbg.generated Tue Nov 26 07:16:48 HKT 2024
     */
    public void setRelationship(String relationship) {
        this.relationship = relationship == null ? null : relationship.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column emergency_contacts.is_primary
     *
     * @return the value of emergency_contacts.is_primary
     *
     * @mbg.generated Tue Nov 26 07:16:48 HKT 2024
     */
    public Boolean getIsPrimary() {
        return isPrimary;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column emergency_contacts.is_primary
     *
     * @param isPrimary the value for emergency_contacts.is_primary
     *
     * @mbg.generated Tue Nov 26 07:16:48 HKT 2024
     */
    public void setIsPrimary(Boolean isPrimary) {
        this.isPrimary = isPrimary;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column emergency_contacts.created_at
     *
     * @return the value of emergency_contacts.created_at
     *
     * @mbg.generated Tue Nov 26 07:16:48 HKT 2024
     */
    public Date getCreatedAt() {
        return createdAt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column emergency_contacts.created_at
     *
     * @param createdAt the value for emergency_contacts.created_at
     *
     * @mbg.generated Tue Nov 26 07:16:48 HKT 2024
     */
    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column emergency_contacts.updated_at
     *
     * @return the value of emergency_contacts.updated_at
     *
     * @mbg.generated Tue Nov 26 07:16:48 HKT 2024
     */
    public Date getUpdatedAt() {
        return updatedAt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column emergency_contacts.updated_at
     *
     * @param updatedAt the value for emergency_contacts.updated_at
     *
     * @mbg.generated Tue Nov 26 07:16:48 HKT 2024
     */
    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }
}