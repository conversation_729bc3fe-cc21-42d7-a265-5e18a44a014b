package com.linhong.boot.system.controller;

import com.linhong.boot.common.result.Result;
import com.linhong.boot.shared.file.model.FileInfo;
import com.linhong.boot.shared.file.service.FileService;
import com.linhong.boot.shared.file.util.FileUploadUtils;
import com.linhong.boot.system.model.dto.LoginConfigDTO;
import com.linhong.boot.system.model.vo.LoginConfigVO;
import com.linhong.boot.system.service.LoginConfigService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 登录页配置控制器
 *
 * <AUTHOR>
 */
@Tag(name = "登录页配置")
@RestController
@RequiredArgsConstructor
@Slf4j
@Validated
public class LoginConfigController {

    private final LoginConfigService loginConfigService;
    private final FileService fileService;

    @Operation(summary = "获取登录页配置", description = "前端登录页调用，获取所有配置信息")
    @GetMapping("/api/login/config/all")
    public Result<LoginConfigVO.LoginPageConfigVO> getLoginPageConfig() {
        LoginConfigVO.LoginPageConfigVO config = loginConfigService.getLoginPageConfig();
        return Result.success(config);
    }

    @Operation(summary = "测试接口", description = "测试数据库连接和基础功能")
    @GetMapping("/api/v1/login-config/test")
    public Result<String> testConnection() {
        try {
            // 简单测试，返回当前时间和状态
            log.info("测试接口被调用");
            return Result.success("登录配置服务正常运行，时间: " + java.time.LocalDateTime.now());
        } catch (Exception e) {
            log.error("测试接口失败", e);
            return Result.failed("测试失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取可编辑配置", description = "后台管理页面调用，获取可编辑的配置信息")
    @GetMapping("/api/v1/login-config")
    public Result<LoginConfigVO.EditableConfigVO> getEditableConfig() {
        try {
            log.info("收到获取可编辑配置请求");
            LoginConfigVO.EditableConfigVO config = loginConfigService.getEditableConfig();
            log.info("返回配置数据: {}", config);
            return Result.success(config);
        } catch (Exception e) {
            log.error("获取可编辑配置失败", e);
            return Result.failed("获取配置失败: " + e.getMessage());
        }
    }

    @Operation(summary = "更新基础配置", description = "更新系统标题、企业文化标题等基础配置")
    @PutMapping("/api/v1/login-config/basic")
    public Result<Void> updateBasicConfig(@Valid @RequestBody LoginConfigDTO.BasicConfigDTO basicConfig) {
        loginConfigService.updateBasicConfig(basicConfig);
        return Result.success();
    }

    @Operation(summary = "更新企业文化配置", description = "批量更新企业文化内容")
    @PutMapping("/api/v1/login-config/culture")
    public Result<Void> updateCultureConfig(@Valid @RequestBody List<LoginConfigDTO.CultureItemDTO> cultureItems) {
        loginConfigService.updateCultureConfig(cultureItems);
        return Result.success();
    }

    @Operation(summary = "测试上传接口", description = "测试上传接口是否可访问")
    @GetMapping("/api/v1/login-config/upload/test")
    public Result<String> testUpload() {
        log.info("上传测试接口被调用");
        return Result.success("上传接口路径正常");
    }

    @Operation(summary = "上传LOGO", description = "上传系统LOGO图片")
    @PostMapping("/api/v1/login-config/upload/logo")
    public Result<String> uploadLogo(
            @Parameter(description = "LOGO图片文件", required = true)
            @RequestParam("file") MultipartFile file) {

        try {
            log.info("收到LOGO上传请求，文件名: {}, 大小: {}", file.getOriginalFilename(), file.getSize());

            // 验证文件
            validateLogoFile(file);
            log.info("文件验证通过");

            // 上传文件
            FileInfo fileInfo = fileService.uploadFile(file);
            log.info("文件上传成功，URL: {}", fileInfo.getUrl());

            // 更新配置
            loginConfigService.updateConfig("system_logo", fileInfo.getUrl());
            log.info("配置更新成功");

            return Result.success(fileInfo.getUrl());
        } catch (Exception e) {
            log.error("LOGO上传失败", e);
            return Result.failed("LOGO上传失败: " + e.getMessage());
        }
    }

    @Operation(summary = "恢复默认配置", description = "将所有配置恢复为默认值")
    @PostMapping("/api/v1/login-config/reset")
    public Result<Void> resetToDefault() {
        loginConfigService.resetToDefault();
        return Result.success();
    }

    /**
     * 验证LOGO文件
     */
    private void validateLogoFile(MultipartFile file) {
        if (file.isEmpty()) {
            throw new IllegalArgumentException("请选择要上传的文件");
        }

        // 文件大小限制
        if (FileUploadUtils.isFileSizeExceeded(file.getSize())) {
            throw new IllegalArgumentException("文件大小不能超过" +
                FileUploadUtils.getReadableFileSize(FileUploadUtils.MAX_FILE_SIZE));
        }

        // 文件类型限制
        String contentType = file.getContentType();
        if (!FileUploadUtils.isAllowedImageType(contentType)) {
            throw new IllegalArgumentException("只支持PNG、JPG、JPEG、SVG格式的图片");
        }

        // 文件名安全检查
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || originalFilename.contains("..")) {
            throw new IllegalArgumentException("文件名不合法");
        }

        log.info("文件验证通过 - 文件名: {}, 大小: {}, 类型: {}",
                originalFilename, FileUploadUtils.getReadableFileSize(file.getSize()), contentType);
    }
}
