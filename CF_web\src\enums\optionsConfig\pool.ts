/**-----------------------静态筛选内容-----------------------------**/
//筛选内容
const queryMode = [
  {
    value: "精确",
    label: "精确",
  },
  {
    value: "模糊",
    label: "模糊",
  },
];
// 机构
const queryInstitution = [
  {
    value: "中信银行",
    label: "中信银行",
  },
];
// 跟进状态
const followUpStatus = [
  // { value: "案池", label: "案池" },
  { value: "新案", label: "新案" },
  { value: "继续跟进", label: "继续跟进" },
  { value: "重点跟进", label: "重点跟进" },
  { value: "谈方案", label: "谈方案" },
  { value: "线下分期", label: "线下分期" },
  { value: "个性化分期", label: "个性化分期" },
  { value: "待减免", label: "待减免" },
  { value: "要求退案", label: "要求退案" },
  { value: "保留案件", label: "保留案件" },
  { value: "投诉倾向", label: "投诉倾向" },
];

// 是否选择
const switchOptions = [
  {
    value: -1,
    label: "否",
  },
  {
    value: 1,
    label: "是",
  },
];

//关系的筛选条件
const relationOptions = [
  {
    value: "父母",
    label: "父母",
  },
  {
    value: "夫妻",
    label: "夫妻",
  },
  {
    value: "子女",
    label: "子女",
  },
  {
    value: "朋友",
    label: "朋友",
  },
  {
    value: "其他",
    label: "其他",
  },
];

//换单类型
const changeRecordsOption = [
  {
    value: 1,
    label: "换进",
  },
  {
    value: 2,
    label: "换出",
  },
];

export {
  queryMode,
  queryInstitution,
  followUpStatus,
  switchOptions,
  relationOptions,
  changeRecordsOption,
};
