import * as XLSX from "xlsx"; // 引入  xlsx
import table2Excel from "js-table2excel"; // 引入  js-table2excel
import { ElMessage, ElMessageBox, ElLoading } from "element-plus";
import { format } from "./commit.ts";
import dataSystemAPI from "@/api/dataSystem/pool";
interface ExportConfig {
  key: string;
  title: string;
  type?: string;
}
/**
 * 导出表格
 *
 * exportConfig 导出表头格式
 * list         导出列表内容
 * fileName     导出文件名
 * */
const deriveExcel = (exportConfig: ExportConfig, list: Array<any>, fileName?: string) => {
  exportConfig = exportConfig.filter((e: ExportConfig) => e.title != "UnKnown");
  fileName = fileName ? fileName : `导出-${format(new Date())}`;
  if (!list.length) {
    return ElMessage({
      message: "暂无需要导出的数据",
      type: "warning",
    });
  }
  ElMessageBox.close(); // 关闭弹出框
  const loading = ElLoading.service({
    // 打开遮罩层
    lock: true,
    text: "请稍等...",
    background: "rgba(255, 255, 255, 0.5)",
  });
  table2Excel(exportConfig, list, fileName);
  loading.close(); // 关闭遮罩层
  // dataSystemAPI.casepoolExport(search).then((res) => {
  //   loading.close(); // 关闭遮罩层
  // });
};

/**
 * 导入表格 解析表格内容成arr数组
 * file @param file
 * callback  回调方法
 */
const toLeadIntoExcel = (file: any, callback: any) => {
  const reader = new FileReader();
  reader.onload = (e: any) => {
    const data = e.target.result;
    const workbook = XLSX.read(data, { type: "array" });
    const firstSheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[firstSheetName];
    const results = XLSX.utils.sheet_to_json(worksheet);
    callback(results);
  };
  reader.readAsArrayBuffer(file);
};

export { deriveExcel, toLeadIntoExcel };
