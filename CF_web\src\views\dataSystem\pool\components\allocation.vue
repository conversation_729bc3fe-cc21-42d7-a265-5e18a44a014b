<template>
  <!-- 分配案件 -->
  <el-drawer v-model="props.show" title="分配案件" append-to-body @close="handleCloseDialog">
    <el-form
      ref="eleFormRef"
      :model="allocationListForm"
      label-width="100px"
      :rules="rules"
      @submit="handleSubmit"
    >
      <el-row gutter="24">
        <el-col span="24">
          <el-form-item label="分配数量" prop="cIdNum" v-if="props.ids.length <= 0">
            <el-input v-model="allocationListForm.cIdNum" placeholder="请输入" type="number" />
          </el-form-item>
          <el-form-item label="分配人员" prop="uIds">
            <el-tree
              ref="permTreeRef"
              node-key="value"
              style="width: 100%"
              show-checkbox
              v-model="allocationListForm.user"
              :default-expand-all="true"
              :data="menuPermOptions"
              :filter-node-method="handlePermFilter"
              @check-change="treeChange"
            >
              <template #default="{ data }">
                {{ data.label }}
              </template>
            </el-tree>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" :loading="loading" @click="handleSubmit">确 定</el-button>
        <el-button :loading="loading" @click="handleCloseDialog">取 消</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script lang="ts" setup>
import eleForm from "@/components/EleComponents/ele-form.vue";
import { ElMessage, ElMessageBox, ElButton, ElLoading } from "element-plus";
import { userOrDept } from "../fieldsSys";
import { useArchitectureStoreHook } from "@/store";
import { removeEmptyValues } from "@/utils/commit";
import dataSystemAPI from "@/api/dataSystem/pool";
const props = defineProps({
  show: {
    type: Boolean,
    required: true,
  },
  // 分配idsid
  ids: {
    type: Array,
    required: () => [],
  },
  type: {
    //分配类型1 案池 2作业清单
    type: Number,
    required: () => 1,
  },
});

// 人员下拉
const menuPermOptions = ref(null);
const permTreeRef = ref(null);

const architectureStore = useArchitectureStoreHook();
architectureStore.getArchitectureInfo();
const emit = defineEmits(["update:show", "refresh"]);
const loading = ref(false);

const eleFormRef = ref(null);
const allocationListForm = ref({
  cIdNum: "",
  uIds: "",
});
const rules = reactive({
  cIdNum: [{ required: true, message: "分配案件数量不能为空", trigger: "blur" }],
  uIds: [{ required: true, message: "请选择分配员工", trigger: "blur" }],
});
const treeChange = () => {
  const checkedMenuIds: number[] = permTreeRef
    .value!.getCheckedNodes(true, true)
    .map((node: any) => node.value);
  allocationListForm.value.uIds = checkedMenuIds;
  console.log(allocationListForm.value);
};
//提交
const handleSubmit = () => {
  eleFormRef.value.validate((valid: any) => {
    if (valid) {
      console.log(allocationListForm.value);
      let obj = {
        uIds: allocationListForm.value.uIds,
      };
      if (props.ids.length > 0) {
        obj.ids = props.ids;
      } else {
        obj.cIdNum = allocationListForm.value.cIdNum;
      }
      let apiName = "casepoolAllocate";
      if (props.type == 2) apiName = "workAllocate";
      dataSystemAPI[apiName](obj).then((e) => {
        ElMessage({
          message: "分配成功",
          type: "success",
        });
        emit("refresh");
        handleCloseDialog();
      });
    }
  });
};

//关闭弹窗
const handleCloseDialog = () => {
  console.log("关闭");
  eleFormRef.value.resetFields();
  emit("update:show", false);
};
function handlePermFilter(
  value: string,
  data: {
    [key: string]: any;
  }
) {
  console.log(data, value);
  if (!value) return true;
  return data.label.includes(value);
}

//递归重组组织
function mergeUsersToChildren(data: any) {
  data.forEach((node: any) => {
    if (node.children && node.children.length > 0) {
      if (!node.children) {
        node.children = [];
      }
      node.children = node.children.map((user: any) => {
        user.disabled = user.isUser ? false : true;
        return user;
      });
    }
    if (node.children && node.children.length > 0) {
      mergeUsersToChildren(node.children);
    }
  });
  return data;
}

onUpdated(() => {
  console.log("--------onUpdated");
  nextTick(() => {
    menuPermOptions.value = mergeUsersToChildren(
      JSON.parse(JSON.stringify(architectureStore.architectureInfoTissue))
    );
    console.log(menuPermOptions.value);
  });
});
</script>

<style lang="scss" scoped></style>
