import { computed } from "vue";
import { ElMessage, ElMessageBox, ElButton, ElLoading } from "element-plus";
import useClipboard from "vue-clipboard3";
/**
 * 删除对象里面的空数据
 *
 */
const removeEmptyValues = (obj: any) => {
  for (const key in obj) {
    if (obj[key] === null || obj[key] === undefined) {
      // eslint-disable-next-line @typescript-eslint/no-dynamic-delete
      delete obj[key];
    } else if (typeof obj[key] === "object" && Object.keys(obj[key]).length === 0) {
      // eslint-disable-next-line @typescript-eslint/no-dynamic-delete
      delete obj[key];
    }
  }
  return obj;
};
/**
 * 删除对象里面值 两边空格
 *
 */
const trimObjectValuesInPlace = (obj: any) => {
  // 创建一个新的对象来存储处理后的键值对
  let trimmedObj = {};

  // 遍历原对象的每个键值对
  for (let key in obj) {
    if (obj.hasOwnProperty(key)) {
      // 对每个值进行 trim
      trimmedObj[key] = typeof obj[key] === "string" ? obj[key].trim() : obj[key];
    }
  }

  return trimmedObj;
};

/**
 * 时间格式转换
 *
 * @ dates 时间搓
 * @ formatData 转换格式
 * **/
const format = (dates: any, formatData = null) => {
  //时间戳转换方法    date:时间戳数字
  dates = Number(dates);
  var date = new Date(dates);
  var YY = date.getFullYear() + "-";
  var MM = (date.getMonth() + 1 < 10 ? "0" + (date.getMonth() + 1) : date.getMonth() + 1) + "-";
  var DD = date.getDate() < 10 ? "0" + date.getDate() : date.getDate();
  var hh = (date.getHours() < 10 ? "0" + date.getHours() : date.getHours()) + ":";
  var mm = (date.getMinutes() < 10 ? "0" + date.getMinutes() : date.getMinutes()) + ":";
  var ss = date.getSeconds() < 10 ? "0" + date.getSeconds() : date.getSeconds();
  if (formatData === "HH-MM-SS") {
    return hh + mm + ss;
  } else if (formatData === "HH-MM") {
    let str = hh + mm;
    return str.substring(0, str.length - 1);
  } else if (formatData === "YY-MM-DD") {
    return YY + MM + DD;
  } else {
    return YY + MM + DD + " " + hh + mm + ss;
  }
};

/**
 * 复制内容
 *
 * text  复制内容
 */
async function copyToClipboard(text: any) {
  // 直接调用 Clipboard API 的 writeText 方法
  try {
    const { toClipboard } = useClipboard();
    await toClipboard(text);
    ElMessage({
      message: "内容已复制到剪贴板",
      type: "success",
    });
  } catch (error) {
    ElMessage({
      message: "复制失败",
      type: "error",
    });
  }
}

export { removeEmptyValues, format, trimObjectValuesInPlace, copyToClipboard };
