package com.linhong.boot.system.model.query;

import com.linhong.boot.common.base.BasePageQuery;
import com.linhong.boot.system.model.entity.Dict;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 用户分页查询对象
 *
 */
@Data
//@EqualsAndHashCode(callSuper = false)
@EqualsAndHashCode
@Schema(description ="用户分页查询对象")
public class UserPageQuery extends BasePageQuery {

    @Schema(description="关键字(用户名/昵称/手机号)")
    private String keywords;

    @Schema(description="用户状态")
    private Integer status;

    @Schema(description="部门ID")
    private String deptId;

    @Schema(description="角色ID")
    private List<Long> roleIds;

    @Schema(description="创建时间范围")
    private List<String> createTime;

}
