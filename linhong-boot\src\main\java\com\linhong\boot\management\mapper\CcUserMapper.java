package com.linhong.boot.management.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linhong.boot.management.model.entity.CcUser;
import com.linhong.boot.management.model.entity.CcUserExample;
import com.linhong.boot.management.model.query.CcUserExcQuery;
import com.linhong.boot.management.model.query.CcUserQuery;
import com.linhong.boot.management.model.vo.CcUserVo;

@Mapper
public interface CcUserMapper extends BaseMapper<CcUser>{
	
	/**
	 * 案池分页列表
	 * @param page
	 * @param queryParams
	 * @return
	 */
	Page<CcUserVo> getPoolPage(Page<CcUserVo> page,CcUserQuery example);
	Page<CcUser> getPoolPage2(Page<CcUserVo> page,CcUserQuery example);
	/**
	 * 批量更新
	 * @param ccUsers
	 * @return
	 */
	int updateBatchByIdNumber(@Param("ccUsers") List<CcUser> ccUsers);
	/**
	 * 作业清单
	 * @param page
	 * @param example
	 * @return
	 */
	Page<CcUserVo> getWorkPage(Page<CcUserVo> page,CcUserQuery example);
	/**
	 * 案池导出
	 * @param page
	 * @param queryParams
	 * @return
	 */
	List<CcUser> getPoolExt(String a ,CcUserExcQuery example);
	/**
	 * 作业清单导出
	 * @param a
	 * @param example
	 * @return
	 */
	List<CcUser> getWorkPoolExt(String a ,CcUserExcQuery example);
	/**
	 * 案池配置查询影响案件
	 * @param example
	 * @return
	 */
	List<CcUser> getCaseList(@Param("example") CcUserQuery example);
	/**
	 * 查询条件的总金额
	 * @param example
	 * @return
	 */
	CcUserVo getPoolPageAmount(String a,CcUserQuery example);
	
	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table cc_user
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	long countByExample(CcUserExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table cc_user
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	int deleteByExample(CcUserExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table cc_user
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	int deleteByPrimaryKey(Long id);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table cc_user
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table cc_user
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	int insertSelective(CcUser row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table cc_user
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	List<CcUser> selectByExample(CcUserExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table cc_user
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	CcUser selectByPrimaryKey(Long id);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table cc_user
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	int updateByExampleSelective(@Param("row") CcUser row, @Param("example") CcUserExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table cc_user
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	int updateByExample(@Param("row") CcUser row, @Param("example") CcUserExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table cc_user
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	int updateByPrimaryKeySelective(CcUser row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table cc_user
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	int updateByPrimaryKey(CcUser row);
}