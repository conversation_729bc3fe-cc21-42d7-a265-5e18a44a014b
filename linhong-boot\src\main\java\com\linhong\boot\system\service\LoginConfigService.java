package com.linhong.boot.system.service;

import com.linhong.boot.system.model.dto.LoginConfigDTO;
import com.linhong.boot.system.model.vo.LoginConfigVO;

import java.util.List;

/**
 * 登录页配置Service接口
 *
 * <AUTHOR>
 */
public interface LoginConfigService {

    /**
     * 获取登录页配置（前端登录页调用）
     *
     * @return 登录页配置
     */
    LoginConfigVO.LoginPageConfigVO getLoginPageConfig();

    /**
     * 获取可编辑的登录页配置（后台管理调用）
     *
     * @return 可编辑配置
     */
    LoginConfigVO.EditableConfigVO getEditableConfig();

    /**
     * 更新基础配置
     *
     * @param basicConfig 基础配置
     */
    void updateBasicConfig(LoginConfigDTO.BasicConfigDTO basicConfig);

    /**
     * 更新企业文化配置
     *
     * @param cultureItems 企业文化列表
     */
    void updateCultureConfig(List<LoginConfigDTO.CultureItemDTO> cultureItems);

    /**
     * 更新单个配置项
     *
     * @param configKey   配置键
     * @param configValue 配置值
     */
    void updateConfig(String configKey, String configValue);

    /**
     * 恢复默认配置
     */
    void resetToDefault();

    /**
     * 清除缓存
     */
    void clearCache();
}
