<template>
  <div class="logo">
    <transition enter-active-class="animate__animated animate__fadeInLeft">
      <router-link :key="+collapse" class="logo-link" to="/">
        <img :src="currentLogo" class="logo-image" />
        <span v-if="!collapse" class="title">
          {{ currentTitle }}
        </span>
      </router-link>
    </transition>
  </div>
</template>

<script lang="ts" setup>
import defaultSettings from "@/settings";
import defaultLogo from "@/assets/logo.png";
import { useLoginConfigStore } from "@/store";

defineProps({
  collapse: {
    type: Boolean,
    required: true,
  },
});

const loginConfigStore = useLoginConfigStore();

// 使用接口获取的logo，如果没有则使用默认logo
const currentLogo = computed(() => {
  const logo = loginConfigStore.loginConfig.logo;
  console.log("Current logo from config:", logo);
  return logo || defaultLogo;
});

// 使用接口获取的系统标题，如果没有则使用默认标题
const currentTitle = computed(() => {
  const title = loginConfigStore.loginConfig.systemTitle;
  console.log("Current title from config:", title);
  return title || defaultSettings.title;
});

// 监听配置变化
watch(
  () => loginConfigStore.loginConfig,
  (newConfig) => {
    console.log("Login config updated:", newConfig);
  },
  { deep: true }
);
</script>

<style lang="scss" scoped>
.logo {
  width: 100%;
  height: $navbar-height;
  background-color: $sidebar-logo-background; /* 与菜单栏相同的深蓝色背景 */
  padding: 0 16px;

  .logo-link {
    display: flex;
    align-items: center;
    justify-content: flex-start; /* 靠左对齐 */
    width: 100%;
    height: 100%;
    text-decoration: none;
  }

  .logo-image {
    width: 32px !important;
    height: 32px !important;
    flex-shrink: 0;
  }

  .title {
    flex-shrink: 0; /* 防止容器在空间不足时缩小 */
    margin-left: 12px;
    font-size: 16px;
    font-weight: bold;
    color: white;
    white-space: nowrap; /* 防止文字换行 */
  }
}

.layout-top,
.layout-mix {
  .logo {
    width: auto;
    min-width: 200px;
  }

  &.hideSidebar {
    .logo {
      width: auto;
      min-width: 60px;
    }
  }
}
</style>
