package com.linhong.boot.management.controller;

import java.util.List;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.linhong.boot.common.result.Result;
import com.linhong.boot.management.model.entity.UserMonthlyRanking;
import com.linhong.boot.management.service.RepaymentRecordService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;

@Tag(name = "5.报表")
@RestController
@RequestMapping("/api/v1/report")
@RequiredArgsConstructor
public class ReportUserRankController {
	
	private final RepaymentRecordService recordService;
	 
	@Operation(summary = "查询上月还款排名")
	@GetMapping("/lastMonthRank")
	public Result<List<UserMonthlyRanking>> lastMonthRank(String type) {
		List<UserMonthlyRanking> reList;
		if ("team".equals(type)) {
			// 团队排名：按部门汇总
			reList = recordService.userRankByTeam();
		} else {
			// 个人排名：按个人汇总
			reList = recordService.userRank();
		}
		Integer rank = 1;
		for (UserMonthlyRanking u : reList) {
			u.setRankSort(rank);
			rank++;
		}
		return Result.success(reList);
	}
	
	@Operation(summary = "查询本月还款排名")
	@GetMapping("/NowMonthRank")
	public Result<List<UserMonthlyRanking>> NowMonthRank(String type) {
		List<UserMonthlyRanking> reList;
		if ("team".equals(type)) {
			// 团队排名：按部门汇总
			reList = recordService.userRankNowByTeam();
		} else {
			// 个人排名：按个人汇总
			reList = recordService.userRankNow();
		}
		Integer rank = 1;
		for (UserMonthlyRanking u : reList) {
			u.setRankSort(rank);
			rank++;
		}
		return Result.success(reList);
	}


	
}
