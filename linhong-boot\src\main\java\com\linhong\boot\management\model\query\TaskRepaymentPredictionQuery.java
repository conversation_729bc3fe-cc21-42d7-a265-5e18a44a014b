package com.linhong.boot.management.model.query;

import java.util.Date;
import java.util.List;

import lombok.Data;

@Data
public class TaskRepaymentPredictionQuery {

	private int pageNum=1;

	private int pageSize=10;

	private Integer id;

	private String customerName;

	private String customerIndex;
	
	private String customerIndexNumber;

	private String operatorId;

	private String operatorName;

	private String deptId;

	private String deptName;

	private Long nextMonthRepayment;

	private Date expectedRepaymentDate;

	private String remarks;
	
	private String ccId;
	
	private String[] times;
	
	private String userOrDept;
	
	private List<Long> uIds;
	
	private List<String> batchNumber;
	
	private String[] huanTime;
}