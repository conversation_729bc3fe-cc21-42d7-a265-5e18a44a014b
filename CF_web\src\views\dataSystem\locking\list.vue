<template>
  <div class="app-container">
    <div class="search-bar">
      <eleForm
        ref="eleFormRef"
        :formValue="screenValue"
        :formItemList="eleFormAllList"
        @handleSubmit="handleQuery"
        @handleCancel="handleQuery"
      />
    </div>

    <el-card shadow="never">
      <el-table
        ref="dataTableRef"
        v-loading="loading"
        :data="roleList"
        highlight-current-row
        border
        style="width: 100%"
      >
        <el-table-column
          v-for="item in listFields"
          :key="item.prop"
          :fixed="item.fixed || false"
          :label="item.label"
          :prop="item.prop"
          min-width="120"
          sortable
        />
        <el-table-column fixed="right" label="操作" width="140">
          <template #default="scope">
            <el-button
              type="primary"
              size="small"
              link
              icon="lock"
              @click="deleteLock(scope.row.id)"
            >
              索单
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: "lockingList",
  inheritAttrs: false,
});
import eleForm from "@/components/EleComponents/ele-form.vue";
import { screenList, listFields } from "./fieldsSys";
import { ElMessage, ElMessageBox, ElButton, ElLoading } from "element-plus";
import RoleAPI, { RolePageVO, RoleForm, RolePageQuery } from "@/api/system/role";
import dataSystemAPI from "@/api/dataSystem/pool";
import { useUserStore, useArchitectureStoreHook } from "@/store";
import { removeEmptyValues } from "@/utils/commit";
const router = useRouter();

const eleFormRef = ref(null);
const eleFormAllList = ref(screenList);
const loading = ref(false);
const total = ref(0);
//筛选字段默认值
const screenValue: any = ref({
  operatorId: [],
});
const userStore = useUserStore();
const architectureStore = useArchitectureStoreHook();
const queryParams = reactive<RolePageQuery>({
  pageNum: 1,
  pageSize: 10,
});

// 表格数据
const roleList = ref<[]>();
const dataTableRef = ref(null);
// 跟进状态
const followStatus = ref<[]>();
// 详情
const handleOpenDialog = (id?: any) => {
  let search = removeEmptyValues(eleFormRef.value.getFormVlaue());
  if (search["operatorId"])
    search["userOrDept"] = search["operatorId"][search["operatorId"].length - 1] || "";
  delete search.operatorId;
  router.push({
    path: "/dataSystem/pool/poolDetails",
    query: {
      id,
      pagesType: 2,
      search: JSON.stringify({
        ...search,
        ...queryParams,
      }),
    },
  });
};
// 查询
function handleQuery() {
  let search = removeEmptyValues(eleFormRef.value.getFormVlaue());
  let isEmpty = Object.keys(search).length === 0;
  if (isEmpty) {
    return ElMessage({
      message: "搜索条件必须填写一个！",
      type: "warning",
    });
  }
  loading.value = true;
  dataSystemAPI
    .casepoolLockCaseList({
      ...search,
    })
    .then((data: any) => {
      roleList.value = data;
    })
    .finally(() => {
      loading.value = false;
    });
}
//锁单
const deleteLock = (id: any) => {
  ElMessageBox.confirm("是否锁定该案子？", "提示", {
    confirmButtonText: "确认",
    cancelButtonText: "取消",
    type: "warning",
    draggable: true,
  }).then(() => {
    dataSystemAPI
      .casepoolCaseLock({
        id,
      })
      .then((data: any) => {
        ElMessage({
          message: "锁定成功！",
          type: "success",
        });
        handleQuery();
      });
  });
};

// 跟进结果
const collectionResultName = computed(() => {
  return (type: any) => {
    let options = followStatus.value;
    let name = "";
    if (options && options.length > 0 && type) {
      if (type == 1) {
        name = "新案";
      } else {
        name = options.find((t: any) => t.value == type)?.label || "-";
      }
    }
    return name;
  };
});

onUpdated(async () => {});
onMounted(async () => {
  followStatus.value = await architectureStore.casepoolKeyValueList("follow_status");
});
</script>
<style>
.flex-between {
  display: flex;
  justify-content: space-between;
}

.flex-center {
  display: flex;
  justify-content: center;
}

.flex-start {
  display: flex;
  align-items: center;
}

.tips {
  span {
    margin-right: 2px;
  }

  text {
    font-weight: bold;
    color: red;
  }
}
</style>
