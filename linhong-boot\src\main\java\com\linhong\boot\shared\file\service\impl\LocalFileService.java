package com.linhong.boot.shared.file.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import com.linhong.boot.shared.file.model.FileInfo;
import com.linhong.boot.shared.file.service.FileService;
import com.linhong.boot.shared.file.util.FileUploadUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.PostConstruct;
import java.io.File;
import java.io.IOException;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.time.LocalDateTime;
import java.util.Enumeration;

/**
 * 本地文件上传服务实现
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@ConditionalOnProperty(value = "oss.type", havingValue = "local", matchIfMissing = true)
public class LocalFileService implements FileService {

    @Value("${oss.local.upload-path:/uploads}")
    private String uploadPath;

    @Value("${oss.local.domain:http://localhost:8989}")
    private String domain;

    @PostConstruct
    public void init() {
        // 规范化路径，适配不同操作系统
        uploadPath = FileUploadUtils.normalizePath(uploadPath);

        // 在Linux生产环境下自动获取局域网IP
        if (domain.contains("localhost") && !System.getProperty("os.name").toLowerCase().contains("windows")) {
            String localIp = getLocalIpAddress();
            if (localIp != null) {
                domain = domain.replace("localhost", localIp);
                log.info("自动获取局域网IP，更新访问域名为: {}", domain);
            }
        }

        // 确保上传目录存在
        boolean dirCreated = FileUploadUtils.ensureDirectoryExists(uploadPath);
        if (!dirCreated) {
            log.error("无法创建上传目录: {}", uploadPath);
        }

        // 检查目录权限
        FileUploadUtils.DirectoryPermission permission = FileUploadUtils.checkDirectoryPermission(uploadPath);
        log.info("目录权限检查: {}", permission);

        if (!permission.isValid()) {
            log.error("上传目录权限不足: {}", uploadPath);
        }

        log.info("文件上传服务初始化完成 - 操作系统: {}, 上传路径: {}, 访问域名: {}",
                System.getProperty("os.name"), uploadPath, domain);
    }

    @Override
    public FileInfo uploadFile(MultipartFile file) {
        try {
            // 生成文件名
            String suffix = FileUtil.getSuffix(file.getOriginalFilename());
            String uuid = IdUtil.simpleUUID();
            String dateFolder = DateUtil.format(LocalDateTime.now(), "yyyyMMdd");
            String fileName = dateFolder + "/" + uuid + "." + suffix;
            
            // 构建完整路径，使用系统文件分隔符
            String fullPath = uploadPath + File.separator + fileName.replace("/", File.separator);
            File targetFile = new File(fullPath);

            // 创建目录
            File parentDir = targetFile.getParentFile();
            if (!parentDir.exists()) {
                boolean created = parentDir.mkdirs();
                if (!created) {
                    throw new RuntimeException("无法创建目录: " + parentDir.getAbsolutePath());
                }
            }
            
            // 保存文件
            file.transferTo(targetFile);
            
            // 构建访问URL
            String fileUrl = domain + "/uploads/" + fileName;
            
            // 返回文件信息
            FileInfo fileInfo = new FileInfo();
            fileInfo.setName(fileName);
            fileInfo.setUrl(fileUrl);
            
            log.info("文件上传成功: {}", fileUrl);
            return fileInfo;
            
        } catch (IOException e) {
            log.error("文件上传失败", e);
            throw new RuntimeException("文件上传失败: " + e.getMessage());
        }
    }

    @Override
    public boolean deleteFile(String filePath) {
        try {
            if (filePath == null || filePath.isEmpty()) {
                return false;
            }
            
            // 从URL中提取文件路径
            String fileName;
            if (filePath.startsWith(domain)) {
                fileName = filePath.substring(domain.length() + "/uploads/".length());
            } else {
                fileName = filePath;
            }
            
            String fullPath = uploadPath + "/" + fileName;
            File file = new File(fullPath);
            
            if (file.exists()) {
                boolean deleted = file.delete();
                log.info("文件删除{}: {}", deleted ? "成功" : "失败", fullPath);
                return deleted;
            }
            
            return true;
        } catch (Exception e) {
            log.error("文件删除失败: {}", filePath, e);
            return false;
        }
    }

    /**
     * 获取局域网IP地址
     */
    private String getLocalIpAddress() {
        try {
            Enumeration<NetworkInterface> interfaces = NetworkInterface.getNetworkInterfaces();
            while (interfaces.hasMoreElements()) {
                NetworkInterface networkInterface = interfaces.nextElement();

                // 跳过回环接口和未启用的接口
                if (networkInterface.isLoopback() || !networkInterface.isUp()) {
                    continue;
                }

                Enumeration<InetAddress> addresses = networkInterface.getInetAddresses();
                while (addresses.hasMoreElements()) {
                    InetAddress address = addresses.nextElement();

                    // 只获取IPv4地址，且不是回环地址
                    if (!address.isLoopbackAddress() &&
                        !address.isLinkLocalAddress() &&
                        address.getHostAddress().indexOf(':') == -1) {

                        String ip = address.getHostAddress();
                        log.info("检测到局域网IP: {} (网卡: {})", ip, networkInterface.getName());
                        return ip;
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取局域网IP失败", e);
        }

        log.warn("未能获取到局域网IP，使用localhost");
        return null;
    }
}
