<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.linhong.boot.system.mapper.LoginPageConfigMapper">

    <!-- 根据配置键更新配置值 -->
    <update id="updateValueByKey">
        UPDATE login_page_config
        SET config_value = #{configValue},
            update_by = #{updateBy},
            update_time = NOW()
        WHERE config_key = #{configKey}
    </update>

</mapper>
