import request from "@/utils/request";

const statementAPI = {
  /**
   * 历史跟进记录
   * **/
  followHistoryPage(queryParams?: any) {
    return request<any>({
      url: "/api/v1/followHistory/page",
      method: "get",
      params: queryParams,
    });
  },
  /**
   * 删除历史记录
   */
  followHistoryDelete(queryParams?: any) {
    return request<any>({
      url: "/api/v1/followHistory/delete",
      method: "delete",
      params: queryParams,
    });
  },
};
export default statementAPI;
