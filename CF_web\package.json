{"name": "CF", "version": "2.18.5", "private": true, "type": "module", "scripts": {"dev": "vite --mode development", "build": "vue-tsc --noEmit & vite build", "dev:dev": "vite --mode development", "dev:prod": "vite --mode production", "build:dev": "vite build --mode development", "build:pre": "vite build --mode preelopment", "build:prod": "vite build --mode production", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --noEmit", "lint:eslint": "eslint --fix ./src", "lint:prettier": "prettier --write \"**/*.{js,cjs,ts,json,tsx,css,less,scss,vue,html,md}\"", "lint:stylelint": "stylelint  \"**/*.{css,scss,vue}\" --fix", "lint:lint-staged": "lint-staged", "preinstall": "npx only-allow pnpm", "prepare": "husky", "commit": "git-cz"}, "config": {"commitizen": {"path": "node_modules/cz-git"}}, "lint-staged": {"*.{js,ts}": ["eslint --fix", "prettier --write"], "*.{cjs,json}": ["prettier --write"], "*.{vue,html}": ["eslint --fix", "prettier --write", "stylelint --fix"], "*.{scss,css}": ["stylelint --fix", "prettier --write"], "*.md": ["prettier --write"]}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@stomp/stompjs": "^7.0.0", "@vueuse/core": "^10.11.1", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "5.1.10", "animate.css": "^4.1.1", "axios": "^1.7.7", "bignumber.js": "^9.1.2", "clipboard": "^2.0.11", "codemirror": "^5.65.18", "codemirror-editor-vue3": "^2.8.0", "echarts": "^5.5.1", "element-plus": "^2.8.7", "exceljs": "^4.4.0", "js-table2excel": "^1.1.2", "lodash-es": "^4.17.21", "lodash-js": "file:lodash-js-1.0.7.tgz", "nprogress": "^0.2.0", "path-browserify": "^1.0.1", "path-to-regexp": "^6.3.0", "pinia": "^2.2.6", "qs": "^6.13.0", "sortablejs": "^1.15.3", "vue": "^3.5.12", "vue-clipboard3": "^2.0.0", "vue-i18n": "9.9.1", "vue-router": "^4.4.5", "xlsx": "^0.18.5"}, "devDependencies": {"@commitlint/cli": "^19.5.0", "@commitlint/config-conventional": "^19.5.0", "@eslint/js": "^9.14.0", "@types/codemirror": "^5.60.15", "@types/lodash": "^4.17.13", "@types/node": "^22.9.0", "@types/nprogress": "^0.2.3", "@types/path-browserify": "^1.0.3", "@types/qs": "^6.9.17", "@types/sortablejs": "^1.15.8", "@typescript-eslint/eslint-plugin": "^8.14.0", "@typescript-eslint/parser": "^8.14.0", "@vitejs/plugin-vue": "^5.1.5", "autoprefixer": "^10.4.20", "commitizen": "^4.3.1", "cz-git": "1.9.4", "eslint": "^9.14.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-vue": "^9.31.0", "globals": "^15.12.0", "husky": "^9.1.6", "lint-staged": "^15.2.10", "postcss": "^8.4.49", "postcss-html": "^1.7.0", "postcss-scss": "^4.0.9", "prettier": "^3.3.3", "sass": "^1.80.6", "stylelint": "^16.10.0", "stylelint-config-html": "^1.1.0", "stylelint-config-recess-order": "^5.1.1", "stylelint-config-recommended-scss": "^14.1.0", "stylelint-config-recommended-vue": "^1.5.0", "stylelint-config-standard": "^36.0.1", "terser": "^5.36.0", "typescript": "5.5.4", "typescript-eslint": "^8.14.0", "unocss": "^0.63.6", "unplugin-auto-import": "^0.18.3", "unplugin-vue-components": "^0.27.4", "vite": "^5.4.11", "vite-plugin-mock-dev-server": "^1.8.0", "vite-plugin-svg-icons": "^2.0.1", "vue-eslint-parser": "^9.4.3", "vue-tsc": "^2.1.10"}, "engines": {"node": ">=18.0.0"}, "repository": "https://gitee.com/youlaiorg/vue3-element-admin.git", "author": "有来开源组织", "license": "MIT"}