<template>
  <div class="login-config-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>登录页配置</span>
          <div class="header-buttons">
            <el-button type="primary" :loading="saving" @click="saveAllConfig">
              <el-icon><Check /></el-icon>
              保存配置
            </el-button>
            <el-button type="warning" @click="resetToDefaultConfig">
              <el-icon><RefreshLeft /></el-icon>
              恢复默认
            </el-button>
          </div>
        </div>
      </template>

      <!-- 基础设置 -->
      <el-row :gutter="20" class="config-section">
        <el-col :span="24">
          <h3 class="section-title">基础设置</h3>
        </el-col>

        <el-col :span="12">
          <div class="logo-upload-section">
            <div class="logo-preview">
              <img v-if="form.logo" :src="form.logo" alt="LOGO预览" class="logo-image" />
              <div v-else class="logo-placeholder">
                <el-icon><Picture /></el-icon>
                <span>LOGO预览</span>
              </div>
            </div>
            <el-upload
              class="logo-uploader"
              :action="uploadAction"
              :headers="uploadHeaders"
              :show-file-list="false"
              :on-success="handleLogoSuccess"
              :on-error="handleLogoError"
              :before-upload="beforeLogoUpload"
              accept="image/png,image/jpeg,image/jpg"
              name="file"
            >
              <el-button type="primary" :loading="logoUploading">
                <el-icon><Upload /></el-icon>
                {{ logoUploading ? "上传中..." : "上传LOGO" }}
              </el-button>
            </el-upload>
            <div class="upload-tip">支持 PNG、JPG 格式，文件大小不超过 2MB</div>
          </div>
        </el-col>

        <el-col :span="12">
          <el-form :model="form" label-width="120px">
            <el-form-item label="系统标题">
              <el-input
                v-model="form.systemTitle"
                placeholder="请输入系统标题"
                maxlength="50"
                show-word-limit
              />
            </el-form-item>
            <el-form-item label="企业文化标题">
              <el-input
                v-model="form.cultureTitle"
                placeholder="请输入企业文化标题"
                maxlength="30"
                show-word-limit
              />
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>

      <el-divider />

      <!-- 企业文化配置 -->
      <el-row class="config-section">
        <el-col :span="24">
          <div class="section-header">
            <h3 class="section-title">企业文化内容</h3>
            <el-button type="primary" size="small" @click="addCultureItem">
              <el-icon><Plus /></el-icon>
              添加一项
            </el-button>
          </div>
        </el-col>

        <el-col :span="24">
          <div class="culture-items">
            <div v-for="(item, index) in cultureItems" :key="index" class="culture-item-editor">
              <div class="item-controls">
                <span class="item-number">{{ index + 1 }}</span>
                <el-button
                  type="danger"
                  size="small"
                  circle
                  :disabled="cultureItems.length <= 1"
                  @click="removeCultureItem(index)"
                >
                  <el-icon><Delete /></el-icon>
                </el-button>
              </div>

              <div class="item-content">
                <div class="icon-selector">
                  <el-select v-model="item.icon" placeholder="选择图标" style="width: 80px">
                    <el-option label="😊" value="😊" />
                    <el-option label="🎯" value="🎯" />
                    <el-option label="💡" value="💡" />
                    <el-option label="📚" value="📚" />
                    <el-option label="⭐" value="⭐" />
                    <el-option label="👑" value="👑" />
                    <el-option label="🚀" value="🚀" />
                    <el-option label="💎" value="💎" />
                  </el-select>
                </div>

                <div class="content-input">
                  <el-input
                    v-model="item.content"
                    type="textarea"
                    :rows="2"
                    placeholder="请输入企业文化内容"
                    maxlength="200"
                    show-word-limit
                  />
                </div>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { Check, RefreshLeft, Picture, Upload, Plus, Delete } from "@element-plus/icons-vue";
import { getToken } from "@/utils/auth";
import { useLoginConfigStore } from "@/store";
import {
  getEditableConfig,
  updateBasicConfig,
  updateCultureConfig,
  uploadLogo,
  uploadLogoUrl,
  resetToDefault,
  type CultureItem,
  type BasicConfigDTO,
} from "@/api/system/login-config";

// 表单数据
const form = reactive({
  systemTitle: "CF-作业系统",
  cultureTitle: "企业文化",
  logo: "",
});

// 企业文化项目
const cultureItems = ref([
  { icon: "😊", content: "托底人生，共筑未来!" },
  { icon: "😊", content: "新百年基业，成万千大众!" },
  { icon: "👑", content: "万千大众共生日，便民县业永同时!" },
  { icon: "📚", content: "做大、做强、做优秀、做永恒!" },
]);

// 状态
const saving = ref(false);
const logoUploading = ref(false);

// 上传配置
const uploadAction = uploadLogoUrl;
const uploadHeaders = {
  Authorization: getToken(),
};

// 加载配置
const loadConfig = async (showSuccessMessage = false) => {
  try {
    console.log("开始加载配置...");

    // 调用API获取配置
    // 注意：由于响应拦截器的处理，这里直接返回的是data字段的内容
    const data = await getEditableConfig();
    console.log("API返回的数据:", data);

    if (!data) {
      throw new Error("API返回数据为空");
    }

    // 检查数据结构
    if (!data.basicConfig) {
      console.warn("basicConfig为空，使用默认配置");
      // 使用默认配置
      form.systemTitle = "CF-作业系统";
      form.cultureTitle = "企业文化";
      form.logo = "/images/logo.png";
      cultureItems.value = [
        { icon: "😊", content: "托底人生，共筑未来!" },
        { icon: "😊", content: "新百年基业，成万千大众!" },
        { icon: "👑", content: "万千大众共生日，便民县业永同时!" },
        { icon: "📚", content: "做大、做强、做优秀、做永恒!" },
      ];
      ElMessage.warning("使用默认配置，请检查数据库配置");
      return;
    }

    // 解构数据
    const { basicConfig, cultureItems: items } = data;

    // 设置表单数据
    form.systemTitle = basicConfig.systemTitle || "CF-作业系统";
    form.cultureTitle = basicConfig.cultureTitle || "企业文化";
    form.logo = basicConfig.logo || "/images/logo.png";
    cultureItems.value = items || [];

    console.log("配置加载成功:", {
      basicConfig,
      cultureItemsCount: cultureItems.value.length,
    });

    // 只有在明确要求显示成功消息时才显示
    if (showSuccessMessage) {
      ElMessage.success("配置加载成功");
    }
  } catch (error) {
    console.error("加载配置失败:", error);

    // 使用默认配置作为后备
    form.systemTitle = "CF-作业系统";
    form.cultureTitle = "企业文化";
    form.logo = "/images/logo.png";
    cultureItems.value = [
      { icon: "😊", content: "托底人生，共筑未来!" },
      { icon: "😊", content: "新百年基业，成万千大众!" },
      { icon: "👑", content: "万千大众共生日，便民县业永同时!" },
      { icon: "📚", content: "做大、做强、做优秀、做永恒!" },
    ];

    ElMessage.error(`加载配置失败，使用默认配置: ${error.message || error}`);
  }
};

// 获取登录配置 store
const loginConfigStore = useLoginConfigStore();

// 保存所有配置
const saveAllConfig = async () => {
  saving.value = true;
  try {
    // 保存基础配置
    await updateBasicConfig(form);

    // 保存企业文化配置
    await updateCultureConfig(cultureItems.value);

    // 保存成功后，重新获取最新配置并更新全局状态
    await loginConfigStore.fetchLoginConfig();
    console.log("保存配置后重新获取登录配置成功");

    ElMessage.success("配置保存成功");
  } catch (error) {
    console.error("保存配置失败:", error);
    ElMessage.error("保存配置失败");
  } finally {
    saving.value = false;
  }
};

// LOGO上传相关
const beforeLogoUpload = (file: File) => {
  const isImage = file.type.startsWith("image/");
  const isLt2M = file.size / 1024 / 1024 < 2;

  if (!isImage) {
    ElMessage.error("只能上传图片文件!");
    return false;
  }
  if (!isLt2M) {
    ElMessage.error("图片大小不能超过 2MB!");
    return false;
  }

  logoUploading.value = true;
  return true;
};

const handleLogoSuccess = (response: any) => {
  logoUploading.value = false;
  console.log("LOGO上传响应:", response);
  if (response.code === "00000") {
    form.logo = response.data;
    ElMessage.success("LOGO上传成功");
  } else {
    ElMessage.error(response.msg || "LOGO上传失败");
  }
};

const handleLogoError = () => {
  logoUploading.value = false;
  ElMessage.error("LOGO上传失败");
};

// 使用API上传LOGO的替代方法
const handleLogoUploadAPI = async (file: File) => {
  logoUploading.value = true;
  try {
    const result = await uploadLogo(file);
    form.logo = result.data;
    ElMessage.success("LOGO上传成功");
  } catch (error) {
    console.error("LOGO上传失败:", error);
    ElMessage.error("LOGO上传失败");
  } finally {
    logoUploading.value = false;
  }
};

// 企业文化项目管理
const addCultureItem = () => {
  cultureItems.value.push({
    icon: "😊",
    content: "",
  });
};

const removeCultureItem = (index: number) => {
  if (cultureItems.value.length > 1) {
    cultureItems.value.splice(index, 1);
  }
};

// 恢复默认配置
const resetToDefaultConfig = async () => {
  try {
    await ElMessageBox.confirm("确定要恢复默认配置吗？这将清除所有自定义设置。", "警告", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    });

    await resetToDefault();

    // 恢复默认配置后，重新获取最新配置并更新全局状态
    await loginConfigStore.fetchLoginConfig();
    console.log("恢复默认配置后重新获取登录配置成功");

    ElMessage.success("已恢复默认配置");
    // 重新加载配置，但不显示成功消息（避免重复提示）
    loadConfig(false);
  } catch (error) {
    if (error !== "cancel") {
      console.error("恢复默认配置失败:", error);
      ElMessage.error("恢复默认配置失败");
    }
  }
};

onMounted(() => {
  // 页面初始加载时不显示成功消息
  loadConfig(false);
});
</script>

<style lang="scss" scoped>
.login-config-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .header-buttons {
    display: flex;
    gap: 10px;
  }
}

.config-section {
  margin-bottom: 30px;
}

.section-title {
  margin: 0 0 20px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-bottom: 20px;
}

// LOGO上传区域
.logo-upload-section {
  .logo-preview {
    width: 200px;
    height: 120px;
    border: 2px dashed #dcdfe6;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 15px;
    background-color: #fafafa;

    .logo-image {
      max-width: 100%;
      max-height: 100%;
      object-fit: contain;
    }

    .logo-placeholder {
      display: flex;
      flex-direction: column;
      align-items: center;
      color: #909399;

      .el-icon {
        font-size: 32px;
        margin-bottom: 8px;
      }
    }
  }

  .logo-uploader {
    margin-bottom: 10px;
  }

  .upload-tip {
    font-size: 12px;
    color: #909399;
    line-height: 1.4;
  }
}

// 企业文化编辑区域
.culture-items {
  .culture-item-editor {
    display: flex;
    align-items: flex-start;
    margin-bottom: 20px;
    padding: 15px;
    border: 1px solid #ebeef5;
    border-radius: 8px;
    background-color: #fafafa;

    .item-controls {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-right: 15px;
      min-width: 40px;

      .item-number {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 24px;
        height: 24px;
        background-color: #409eff;
        color: white;
        border-radius: 50%;
        font-size: 12px;
        font-weight: 600;
        margin-bottom: 10px;
      }
    }

    .item-content {
      flex: 1;
      display: flex;
      gap: 15px;

      .icon-selector {
        flex-shrink: 0;
      }

      .content-input {
        flex: 1;
      }
    }

    &:hover {
      border-color: #c6e2ff;
      background-color: #f0f9ff;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .config-section {
    .el-col {
      margin-bottom: 20px;
    }
  }

  .logo-upload-section {
    text-align: center;
  }

  .culture-item-editor {
    .item-content {
      flex-direction: column;
      gap: 10px;
    }
  }
}
</style>
