package com.linhong.boot.system.service.impl;


import com.linhong.boot.core.security.util.SecurityUtils;
import com.linhong.boot.system.constant.LoginConfigConstants;
import com.linhong.boot.system.mapper.LoginCultureConfigMapper;
import com.linhong.boot.system.mapper.LoginPageConfigMapper;
import com.linhong.boot.system.model.entity.LoginCultureConfig;
import com.linhong.boot.system.model.entity.LoginPageConfig;
import com.linhong.boot.system.model.dto.LoginConfigDTO;
import com.linhong.boot.system.model.vo.LoginConfigVO;
import com.linhong.boot.system.service.LoginConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;


import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 登录页配置Service实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LoginConfigServiceImpl implements LoginConfigService {

    private final LoginPageConfigMapper loginPageConfigMapper;
    private final LoginCultureConfigMapper loginCultureConfigMapper;
    private final RedisTemplate<String, Object> redisTemplate;

    private static final String CACHE_KEY = LoginConfigConstants.Cache.CACHE_KEY;
    private static final String LOCK_KEY = LoginConfigConstants.Cache.LOCK_KEY;
    private static final int CACHE_EXPIRE_MINUTES = LoginConfigConstants.Cache.CACHE_EXPIRE_MINUTES;

    @Override
    public LoginConfigVO.LoginPageConfigVO getLoginPageConfig() {
        // 先从缓存获取
        LoginConfigVO.LoginPageConfigVO cachedConfig = 
            (LoginConfigVO.LoginPageConfigVO) redisTemplate.opsForValue().get(CACHE_KEY);
        
        if (cachedConfig != null) {
            return cachedConfig;
        }

        // 缓存未命中，从数据库查询
        LoginConfigVO.LoginPageConfigVO config = buildConfigFromDB();

        // 写入缓存
        redisTemplate.opsForValue().set(CACHE_KEY, config, CACHE_EXPIRE_MINUTES, TimeUnit.MINUTES);

        return config;
    }

    @Override
    public LoginConfigVO.EditableConfigVO getEditableConfig() {
        log.info("开始获取可编辑配置");
        LoginConfigVO.EditableConfigVO editableConfig = new LoginConfigVO.EditableConfigVO();

        try {
            // 获取基础配置
            List<LoginPageConfig> basicConfigs = loginPageConfigMapper.selectAllActive();
            log.info("查询到基础配置数量: {}", basicConfigs.size());

            Map<String, String> configMap = basicConfigs.stream()
                    .collect(Collectors.toMap(
                        LoginPageConfig::getConfigKey,
                        LoginPageConfig::getConfigValue
                    ));
            log.info("配置映射: {}", configMap);

            LoginConfigVO.BasicConfigVO basicConfig = new LoginConfigVO.BasicConfigVO();
            basicConfig.setLogo(configMap.get(LoginConfigConstants.ConfigKeys.SYSTEM_LOGO));
            basicConfig.setSystemTitle(configMap.get(LoginConfigConstants.ConfigKeys.SYSTEM_TITLE));
            basicConfig.setCultureTitle(configMap.get(LoginConfigConstants.ConfigKeys.CULTURE_TITLE));
            editableConfig.setBasicConfig(basicConfig);
            log.info("基础配置设置完成: {}", basicConfig);

            // 获取企业文化配置
            List<LoginCultureConfig> cultureConfigs = loginCultureConfigMapper.selectAllActiveOrderBySort();
            log.info("查询到企业文化配置数量: {}", cultureConfigs.size());

            List<LoginConfigVO.CultureItemVO> cultureItems = cultureConfigs.stream()
                    .map(this::convertToCultureItemVO)
                    .collect(Collectors.toList());
            editableConfig.setCultureItems(cultureItems);
            log.info("企业文化配置设置完成，数量: {}", cultureItems.size());

            log.info("可编辑配置获取完成");
            return editableConfig;
        } catch (Exception e) {
            log.error("获取可编辑配置失败", e);
            throw new RuntimeException("获取配置失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateBasicConfig(LoginConfigDTO.BasicConfigDTO basicConfig) {
        String currentUser = SecurityUtils.getUsername();

        // 分布式锁防止并发修改
        Boolean lockAcquired = redisTemplate.opsForValue()
                .setIfAbsent(LOCK_KEY, "1", 30, TimeUnit.SECONDS);
        
        if (!lockAcquired) {
            throw new RuntimeException("配置正在被其他用户修改，请稍后再试");
        }

        try {
            // 更新系统标题
            if (basicConfig.getSystemTitle() != null) {
                updateSingleConfig("system_title", basicConfig.getSystemTitle(), currentUser);
            }

            // 更新企业文化标题
            if (basicConfig.getCultureTitle() != null) {
                updateSingleConfig("culture_title", basicConfig.getCultureTitle(), currentUser);
            }

            // 更新LOGO
            if (basicConfig.getLogo() != null) {
                updateSingleConfig("system_logo", basicConfig.getLogo(), currentUser);
            }

            // 清除缓存
            clearCache();

        } finally {
            redisTemplate.delete(LOCK_KEY);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateCultureConfig(List<LoginConfigDTO.CultureItemDTO> cultureItems) {
        String currentUser = SecurityUtils.getUsername();

        // 删除现有数据
        loginCultureConfigMapper.deleteAll();

        // 插入新数据
        if (!CollectionUtils.isEmpty(cultureItems)) {
            for (int i = 0; i < cultureItems.size(); i++) {
                LoginConfigDTO.CultureItemDTO item = cultureItems.get(i);
                LoginCultureConfig config = new LoginCultureConfig();
                config.setContent(item.getContent());
                config.setIcon(item.getIcon());
                config.setSortOrder(i + 1);
                config.setStatus(1);
                config.setUpdateBy(currentUser);
                loginCultureConfigMapper.insert(config);
            }
        }

        // 清除缓存
        clearCache();
    }

    @Override
    public void updateConfig(String configKey, String configValue) {
        String currentUser = SecurityUtils.getUsername();
        updateSingleConfig(configKey, configValue, currentUser);
        clearCache();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void resetToDefault() {
        String currentUser = SecurityUtils.getUsername();

        // 恢复基础配置
        updateSingleConfig(LoginConfigConstants.ConfigKeys.SYSTEM_LOGO,
                          LoginConfigConstants.DefaultValues.DEFAULT_LOGO, currentUser);
        updateSingleConfig(LoginConfigConstants.ConfigKeys.SYSTEM_TITLE,
                          LoginConfigConstants.DefaultValues.DEFAULT_SYSTEM_TITLE, currentUser);
        updateSingleConfig(LoginConfigConstants.ConfigKeys.CULTURE_TITLE,
                          LoginConfigConstants.DefaultValues.DEFAULT_CULTURE_TITLE, currentUser);

        // 恢复企业文化配置
        loginCultureConfigMapper.deleteAll();

        String[][] defaultCultures = LoginConfigConstants.DefaultValues.DEFAULT_CULTURE_ITEMS;

        for (int i = 0; i < defaultCultures.length; i++) {
            String[] culture = defaultCultures[i];
            LoginCultureConfig config = new LoginCultureConfig();
            config.setIcon(culture[0]);
            config.setContent(culture[1]);
            config.setSortOrder(i + 1);
            config.setStatus(1);
            config.setUpdateBy(currentUser);
            loginCultureConfigMapper.insert(config);
        }

        // 清除缓存
        clearCache();
    }

    @Override
    public void clearCache() {
        redisTemplate.delete(CACHE_KEY);
        log.info("登录页配置缓存已清除");
    }

    /**
     * 从数据库构建配置
     */
    private LoginConfigVO.LoginPageConfigVO buildConfigFromDB() {
        LoginConfigVO.LoginPageConfigVO config = new LoginConfigVO.LoginPageConfigVO();

        // 获取基础配置
        List<LoginPageConfig> basicConfigs = loginPageConfigMapper.selectAllActive();
        Map<String, String> configMap = basicConfigs.stream()
                .collect(Collectors.toMap(
                    LoginPageConfig::getConfigKey,
                    LoginPageConfig::getConfigValue
                ));

        config.setLogo(configMap.get(LoginConfigConstants.ConfigKeys.SYSTEM_LOGO));
        config.setSystemTitle(configMap.get(LoginConfigConstants.ConfigKeys.SYSTEM_TITLE));
        config.setCultureTitle(configMap.get(LoginConfigConstants.ConfigKeys.CULTURE_TITLE));

        // 获取企业文化配置
        List<LoginCultureConfig> cultureConfigs = loginCultureConfigMapper.selectAllActiveOrderBySort();
        List<LoginConfigVO.CultureItemVO> cultureItems = cultureConfigs.stream()
                .map(this::convertToCultureItemVO)
                .collect(Collectors.toList());
        config.setCultureItems(cultureItems);

        return config;
    }

    /**
     * 更新单个配置项
     */
    private void updateSingleConfig(String configKey, String configValue, String updateBy) {
        int updated = loginPageConfigMapper.updateValueByKey(configKey, configValue, updateBy);
        if (updated == 0) {
            log.warn("配置项更新失败，configKey: {}", configKey);
        }
    }

    /**
     * 转换为企业文化项VO
     */
    private LoginConfigVO.CultureItemVO convertToCultureItemVO(LoginCultureConfig config) {
        LoginConfigVO.CultureItemVO vo = new LoginConfigVO.CultureItemVO();
        vo.setIcon(config.getIcon());
        vo.setContent(config.getContent());
        return vo;
    }
}
