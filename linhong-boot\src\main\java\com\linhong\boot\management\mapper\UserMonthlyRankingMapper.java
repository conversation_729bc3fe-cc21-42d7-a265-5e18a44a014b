package com.linhong.boot.management.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.linhong.boot.management.model.entity.UserMonthlyRanking;
import com.linhong.boot.management.model.entity.UserMonthlyRankingExample;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface UserMonthlyRankingMapper extends BaseMapper<UserMonthlyRanking> {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table user_monthly_ranking
     *
     * @mbg.generated Sun Dec 15 12:39:02 CST 2024
     */
    long countByExample(UserMonthlyRankingExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table user_monthly_ranking
     *
     * @mbg.generated Sun Dec 15 12:39:02 CST 2024
     */
    int deleteByExample(UserMonthlyRankingExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table user_monthly_ranking
     *
     * @mbg.generated Sun Dec 15 12:39:02 CST 2024
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table user_monthly_ranking
     *
     * @mbg.generated Sun Dec 15 12:39:02 CST 2024
     */
    int insert(UserMonthlyRanking row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table user_monthly_ranking
     *
     * @mbg.generated Sun Dec 15 12:39:02 CST 2024
     */
    int insertSelective(UserMonthlyRanking row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table user_monthly_ranking
     *
     * @mbg.generated Sun Dec 15 12:39:02 CST 2024
     */
    List<UserMonthlyRanking> selectByExample(UserMonthlyRankingExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table user_monthly_ranking
     *
     * @mbg.generated Sun Dec 15 12:39:02 CST 2024
     */
    UserMonthlyRanking selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table user_monthly_ranking
     *
     * @mbg.generated Sun Dec 15 12:39:02 CST 2024
     */
    int updateByExampleSelective(@Param("row") UserMonthlyRanking row, @Param("example") UserMonthlyRankingExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table user_monthly_ranking
     *
     * @mbg.generated Sun Dec 15 12:39:02 CST 2024
     */
    int updateByExample(@Param("row") UserMonthlyRanking row, @Param("example") UserMonthlyRankingExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table user_monthly_ranking
     *
     * @mbg.generated Sun Dec 15 12:39:02 CST 2024
     */
    int updateByPrimaryKeySelective(UserMonthlyRanking row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table user_monthly_ranking
     *
     * @mbg.generated Sun Dec 15 12:39:02 CST 2024
     */
    int updateByPrimaryKey(UserMonthlyRanking row);
}