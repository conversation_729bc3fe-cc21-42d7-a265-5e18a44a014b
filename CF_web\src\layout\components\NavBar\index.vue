<template>
  <div class="navbar">
    <!-- 系统通知 -->
    <div class="navbar__center">
      <SystemNotice />
    </div>

    <!-- 导航栏右侧 -->
    <NavbarRight />
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: "NavBar",
});

import SystemNotice from "./components/SystemNotice.vue";
</script>

<style lang="scss" scoped>
.navbar {
  display: flex;
  align-items: center;
  height: $navbar-height;
  background: #2c3e50; /* 深色背景 */
  border-bottom: 1px solid #34495e;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 0 16px;

  &__center {
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 1;
    min-width: 0;
    padding: 0 20px;
  }

  &__right {
    display: flex;
    align-items: center;
    flex: 1;
    justify-content: flex-end;
    min-width: 0;
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .navbar {
    &__center {
      padding: 0 10px;
    }
  }
}

@media (max-width: 768px) {
  .navbar {
    padding: 0 8px;

    &__center {
      padding: 0 5px;
    }

    &__right {
      flex: 0.8;
    }
  }
}
</style>
