package com.linhong.boot.system.controller;

import com.linhong.boot.common.annotation.Log;
import com.linhong.boot.common.annotation.RepeatSubmit;
import com.linhong.boot.common.enums.LogModuleEnum;
import com.linhong.boot.common.model.Option;
import com.linhong.boot.common.model.OptionOfDept;
import com.linhong.boot.common.result.Result;
import com.linhong.boot.system.model.form.DeptForm;
import com.linhong.boot.system.model.query.DeptQuery;
import com.linhong.boot.system.model.vo.DeptVO;
import com.linhong.boot.system.service.DeptService;

import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 部门控制器
 *
 */
@Tag(name = "05.部门接口")
@RestController
@RequestMapping("/api/v1/dept")
@RequiredArgsConstructor
public class DeptController {

	private final DeptService deptService;

	@Operation(summary = "部门列表")
	@GetMapping
	@Log(value = "部门列表", module = LogModuleEnum.DEPT)
	public Result<List<DeptVO>> getDeptList(DeptQuery queryParams) {
		List<DeptVO> list = deptService.getDeptList(queryParams);
		return Result.success(list);
	}

	@Operation(summary = "部门下拉列表")
	@GetMapping("/options")
	public Result<List<Option<String>>> getDeptOptions() {
		List<Option<String>> list = deptService.listDeptOptions();
		return Result.success(list);
	}

	@Operation(summary = "部门及其下属下拉列表")
	@GetMapping("/optionsAndUsers")
	public Result<List<OptionOfDept<String>>> getUserList() {
		List<OptionOfDept<String>> list = deptService.recurDeptTreeOptions();
		return Result.success(list);
	}

	@Operation(summary = "新增部门")
	@PostMapping
	@PreAuthorize("@ss.hasPerm('sys:dept:add')")
	@RepeatSubmit
	public Result<?> saveDept(@Valid @RequestBody DeptForm formData) {
		String id = deptService.saveDept(formData);
		return Result.success(id);
	}

	@Operation(summary = "获取部门表单数据")
	@GetMapping("/{deptId}/form")
	public Result<DeptForm> getDeptForm(@Parameter(description = "部门ID") @PathVariable String deptId) {
		DeptForm deptForm = deptService.getDeptForm(deptId);
		return Result.success(deptForm);
	}

	@Operation(summary = "修改部门")
	@PutMapping(value = "/{deptId}")
	@PreAuthorize("@ss.hasPerm('sys:dept:edit')")
	public Result<?> updateDept(@PathVariable String deptId, @Valid @RequestBody DeptForm formData) {
		deptId = deptService.updateDept(deptId, formData);
		return Result.success(deptId);
	}

	@Operation(summary = "删除部门")
	@DeleteMapping("/{ids}")
	@PreAuthorize("@ss.hasPerm('sys:dept:delete')")
	public Result<?> deleteDepartments(@Parameter(description = "部门ID，多个以英文逗号(,)分割") @PathVariable("ids") String ids) {
		boolean result = deptService.deleteByIds(ids);
		return Result.judge(result);
	}

}
