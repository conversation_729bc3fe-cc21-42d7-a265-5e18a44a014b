<template>
  <!-- 自定义新增统计弹窗 -->
  <div v-if="props.show" class="statistics-modal-overlay" @click.self="handleCloseDialog">
    <div class="statistics-modal" @click.stop>
      <!-- 头部 -->
      <div class="modal-header">
        <div class="modal-title">新增统计</div>
        <button class="modal-close" @click="handleCloseDialog">
          <svg viewBox="0 0 1024 1024" width="16" height="16">
            <path
              d="M872.448 149.504c-23.552-23.552-61.44-23.552-84.992 0L512 425.984 236.544 150.528c-23.552-23.552-61.44-23.552-84.992 0s-23.552 61.44 0 84.992L426.496 510.976 151.552 785.92c-23.552 23.552-23.552 61.44 0 84.992 11.776 11.776 27.136 17.664 42.496 17.664s30.72-5.888 42.496-17.664L512 595.968l275.456 275.456c11.776 11.776 27.136 17.664 42.496 17.664s30.72-5.888 42.496-17.664c23.552-23.552 23.552-61.44 0-84.992L597.504 510.976l274.944-274.944c23.552-23.552 23.552-61.44 0-86.528z"
              fill="currentColor"
            />
          </svg>
        </button>
      </div>

      <!-- 内容区域 -->
      <div class="modal-body">
        <el-row :gutter="24">
          <el-col :span="24">
            <eleForm
              ref="eleFormRef"
              :formItemList="statisticsFormList"
              :formBox="formBox"
              :operateOpen="false"
              @handleSubmit="formHandleSubmit"
            />
          </el-col>
        </el-row>
      </div>

      <!-- 底部按钮区域 -->
      <div class="modal-footer">
        <el-button type="primary" class="save-btn" :loading="loading" @click="handleSubmit">
          保存
        </el-button>
        <el-button class="cancel-btn" @click="handleCloseDialog">取消</el-button>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ElMessage } from "element-plus";
import { statisticsFormList } from "../fieldsSys";
import eleForm from "@/components/EleComponents/ele-form.vue";
import dataSystemAPI from "@/api/dataSystem/pool";
import { onMounted, onUnmounted } from "vue";

const props = defineProps({
  show: {
    type: Boolean,
    required: true,
  },
  // ids
  ids: {
    type: Number,
    required: false,
    default: null,
  },
});

const emit = defineEmits(["update:show", "refresh"]);

const formBox = ref({
  spanItem: 12, // 每项占比 - 两列布局
  formContent: 24, // 表单父级占比
  operate: 4, // 操作父容器占比
  operateItem: 12,
  operateGrid: " start",
});

const eleFormRef: any = ref(null);
const loading = ref(false);

// ESC键关闭弹窗
const handleEscKey = (event: KeyboardEvent) => {
  if (event.key === "Escape" && props.show) {
    handleCloseDialog();
  }
};

// 组件挂载时添加ESC监听
onMounted(() => {
  document.addEventListener("keydown", handleEscKey);
});

// 组件卸载时移除ESC监听
onUnmounted(() => {
  document.removeEventListener("keydown", handleEscKey);
});

// 提交表单
const handleSubmit = () => {
  eleFormRef.value?.handleSubmit();
};

// 表单提交处理
const formHandleSubmit = (data: any) => {
  loading.value = true;

  // 构建任务预测数据，映射表单字段到数据库字段
  const taskData = {
    // 映射次月还款金额
    nextMonthRepayment: data.nextPaymentAmount,
    // 映射预计还款时间
    expectedRepaymentDate: data.nextPaymentDate,
    // 映射作业员名字
    userOrDept: data.registrar,
    // 只保存用户输入的备注信息
    remarks: data.remarks,
    // 传递案件ID
    ccId: props.ids,
  };

  // 按照taskPrediction的方式调用任务预测保存接口
  dataSystemAPI
    .taskRepaySave(taskData)
    .then((e: any) => {
      ElMessage({
        message: "新增成功",
        type: "success",
      });
      resetForm();
      emit("refresh");
    })
    .catch((error: any) => {
      console.error("保存失败:", error);
      ElMessage({
        message: error.message || "保存失败，请稍后重试",
        type: "error",
      });
    })
    .finally(() => {
      loading.value = false;
    });
};

// 重置表单
const resetForm = () => {
  eleFormRef.value?.resetFields();
  handleCloseDialog();
};

// 关闭弹窗
const handleCloseDialog = () => {
  emit("update:show", false);
};
</script>

<style lang="scss" scoped>
/* 模态背景遮罩 */
.statistics-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 弹窗主体 */
.statistics-modal {
  width: 800px;
  max-width: 90vw;
  max-height: 90vh;
  background: #ffffff;
  border-radius: 8px;
  border: 3px solid #67c23a;
  box-shadow: 0 4px 20px rgba(103, 194, 58, 0.3);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 头部区域 */
.modal-header {
  background: #ffffff;
  padding: 16px 20px;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-shrink: 0;
}

/* "新增统计"标题绿色背景块 */
.modal-title {
  display: inline-block;
  background: linear-gradient(135deg, #67c23a 0%, #5cb85c 100%);
  color: white;
  font-weight: bold;
  font-size: 16px;
  padding: 12px 20px;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(103, 194, 58, 0.3);
}

/* 关闭按钮 */
.modal-close {
  background: none;
  border: none;
  color: #6c757d;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;

  &:hover {
    color: #67c23a;
    background: rgba(103, 194, 58, 0.1);
  }
}

/* 内容区域 */
.modal-body {
  padding: 20px;
  background: #ffffff;
  flex: 1;
  overflow-y: auto;
}

/* 底部按钮区域 */
.modal-footer {
  background: #ffffff;
  padding: 16px 20px;
  border-top: 1px solid #e9ecef;
  display: flex;
  justify-content: center;
  gap: 12px;
  flex-shrink: 0;
}

/* 保存按钮 */
.save-btn {
  background: linear-gradient(135deg, #67c23a 0%, #5cb85c 100%);
  border: none;
  padding: 10px 24px;
  border-radius: 4px;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(103, 194, 58, 0.3);
  transition: all 0.3s ease;

  &:hover {
    background: linear-gradient(135deg, #5cb85c 0%, #52a555 100%);
    box-shadow: 0 4px 8px rgba(103, 194, 58, 0.4);
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(103, 194, 58, 0.3);
  }
}

/* 取消按钮 */
.cancel-btn {
  background: #6c757d;
  border: none;
  color: white;
  padding: 10px 24px;
  border-radius: 4px;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(108, 117, 125, 0.3);
  transition: all 0.3s ease;

  &:hover {
    background: #5a6268;
    box-shadow: 0 4px 8px rgba(108, 117, 125, 0.4);
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(108, 117, 125, 0.3);
  }
}

/* 表单样式增强 */
:deep(.el-form-item__label) {
  color: #333;
  font-weight: 500;
}

:deep(.el-input__wrapper) {
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

:deep(.el-input__wrapper:hover) {
  box-shadow: 0 1px 6px rgba(103, 194, 58, 0.2);
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #67c23a inset;
  border-color: #67c23a;
}

:deep(.el-select .el-input__wrapper:hover) {
  box-shadow: 0 1px 6px rgba(103, 194, 58, 0.2);
}

:deep(.el-select .el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #67c23a inset;
  border-color: #67c23a;
}

:deep(.el-textarea__inner) {
  border-radius: 4px;
  border-color: #dcdfe6;
  min-height: 80px;
  transition: all 0.3s ease;
}

:deep(.el-textarea__inner:hover) {
  border-color: #67c23a;
}

:deep(.el-textarea__inner:focus) {
  border-color: #67c23a;
  box-shadow: 0 0 0 1px #67c23a inset;
}

:deep(.el-date-editor.el-input) {
  width: 100%;
}

:deep(.el-date-editor .el-input__wrapper) {
  border-radius: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .statistics-modal {
    width: 95vw;
    max-height: 95vh;
    margin: 16px;
  }

  .modal-body {
    padding: 16px;
  }

  .modal-footer {
    padding: 12px 16px;
  }
}
</style>
