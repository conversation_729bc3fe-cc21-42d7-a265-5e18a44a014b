<template>
  <div class="app-container pool-container">
    <!-- 筛选条件卡片 -->
    <el-card shadow="never" class="pool-filter-card">
      <div class="filter-header">
        <div class="filter-title">
          <el-icon class="filter-icon">
            <Filter />
          </el-icon>
          <span class="title-text">案件筛选条件</span>
        </div>
        <el-divider class="filter-divider" />
      </div>
      <eleForm
        id="eleForm"
        ref="eleFormRef"
        :formValue="screenValue"
        :formItemList="eleFormAllList"
        :formBox="{
          spanItem: 4,
          formContent: 24,
          operate: 24,
          operateItem: 24,
          operateGrid: 'end',
        }"
        :operateOpen="true"
        defineTxt="搜索"
        cancelTxt="重置"
        label-position="left"
        label-width="80px"
        @handleSubmit="handleQuery"
        @handleCancel="handleReset"
      />
    </el-card>

    <!-- 操作按钮区域 -->
    <div class="action-section">
      <div class="stats-info">
        <div class="tips flex-start">
          <el-icon size="16px" color="#ff0000">
            <Warning />
          </el-icon>
          <span class="ml-2">
            总户数：
            <text class="Danger">{{ total }}</text>
            ；
          </span>
          <span>
            总委托金额：
            <text>{{ sumEntrustTotalAmount }}</text>
            ；
          </span>
          <span>
            勾选户数：
            <text>{{ ids.length }}</text>
            ；
          </span>
          <span>
            勾选金额：
            <text>¥{{ checkTotal }}</text>
            ；
          </span>
        </div>
      </div>

      <div class="action-buttons">
        <el-button type="success" icon="Position" @click="automaticAllocation">任务分派</el-button>
        <el-button
          v-hasPerm="['sys:pool:dispositionLock']"
          :disabled="ids.length === 0"
          type="info"
          icon="Lock"
          @click="goDisposition(1)"
        >
          锁定
        </el-button>
        <el-button
          v-hasPerm="['sys:pool:dispositionUnlock']"
          :disabled="ids.length === 0"
          type="warning"
          icon="Unlock"
          @click="goDisposition(2)"
        >
          解除
        </el-button>
        <el-button
          v-hasPerm="['sys:pool:import']"
          type="success"
          icon="FolderOpened"
          @click="userImport"
        >
          导入
        </el-button>
        <el-button
          v-hasPerm="['sys:pool:export']"
          type="primary"
          icon="FolderChecked"
          @click="exportShow = true"
        >
          导出
        </el-button>
      </div>
    </div>

    <!-- 数据表格卡片 -->
    <el-card shadow="never" class="table-card">
      <div class="data-header">
        <div class="data-title">
          <el-icon class="data-icon">
            <Grid />
          </el-icon>
          <span class="title-text">案件数据列表</span>
        </div>
        <el-divider class="data-divider" />
      </div>
      <el-table
        ref="dataTableRef"
        v-loading="loading"
        :data="roleList"
        highlight-current-row
        border
        style="width: 100%; flex: 1;"
        height="100%"
        stripe
        :row-class-name="tableRowClassName"
        @selection-change="handleSelectionChange"
        @sort-change="sortChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column
          v-for="item in listFields"
          :key="item.prop"
          :fixed="item.fixed || false"
          :label="item.label"
          sortable
          :prop="item.prop"
          min-width="120"
          :width="item.width || ''"
        >
          <template v-if="item.prop == 'customerIndexNumber'" #default="scope">
            <el-button type="primary" size="small" link @click="handleOpenDialog(scope.row.id)">
              {{ scope.row.customerIndexNumber || "——" }}
            </el-button>
          </template>
          <template v-if="item.prop == 'lostContactQueryResult'" #default="scope">
            {{ lostContactQueryResultName(scope.row.lostContactQueryResult) || "——" }}
          </template>
          <template v-if="item.prop == 'caseType'" #default="scope">
            {{ caseTypeName(scope.row.caseType) || "——" }}
          </template>
          <template v-else-if="item.prop == 'isLock'" #default="scope">
            {{
              scope.row.isLock === "1" || scope.row.isLock === 1
                ? "锁定"
                : scope.row.isLock === "0" || scope.row.isLock === 0
                  ? "解锁"
                  : "——"
            }}
          </template>
          <template v-else-if="item.option" #default="scope">
            {{ item.option[scope.row[item.prop]] }}
          </template>
        </el-table-column>

        <el-table-column fixed="right" label="操作" width="100">
          <template #default="scope">
            <el-button
              type="primary"
              size="small"
              link
              icon="user"
              @click="handleOpenDialog(scope.row.id)"
            >
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          :page-sizes="[10, 20, 50, 100, 200, 500]"
          @change="handleQuery"
          @pagination="handleQuery"
        />
      </div>
    </el-card>
    <!-- 分配案件 -->
    <allocation
      v-model:show="allocationShow"
      v-model:ids="allocationIds"
      :type="1"
      @refresh="handleQuery"
    />
    <!-- 导出 -->
    <el-dialog
      v-model="exportShow"
      title="导出案池"
      width="450"
      align-center
      @close="exportHandleCloseDialog"
    >
      <el-form ref="exportFormRef" :model="exportForm" label-width="100px">
        <el-row gutter="24">
          <el-col span="24">
            <el-form-item label="导出方式" prop="derivedValue">
              <el-segmented v-model="exportForm.derivedValue" :options="derivedOptions" />
            </el-form-item>
            <el-form-item label="是否退案" prop="isTuiAn">
              <el-switch v-model="exportForm.isTuiAn" />
            </el-form-item>
            <el-form-item label="退案文件名" prop="fileName">
              <el-input v-model="exportForm.fileName" placeholder="请输入文件名称" type="string" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" :loading="exportLoading" @click="exportHandleSubmit">
            确 定
          </el-button>
          <el-button :loading="exportLoading" @click="exportHandleCloseDialog">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: "Pool",
  inheritAttrs: false,
});
import eleForm from "@/components/EleComponents/ele-form.vue";
import allocation from "./components/allocation.vue";
import { screenList, screenAllList, listFields, createBatchNumberField } from "./fieldsSys";
import { ElMessage, ElMessageBox, ElButton, ElLoading } from "element-plus";
import { Filter, Grid } from "@element-plus/icons-vue";
import { deriveExcel } from "@/utils/tools";
import RoleAPI, { RolePageVO, RoleForm, RolePageQuery } from "@/api/system/role";
import dataSystemAPI from "@/api/dataSystem/pool";
import { useUserStore, useArchitectureStoreHook } from "@/store";
import { $add } from "@/utils/calculate";
import { removeEmptyValues } from "@/utils/commit";
const router = useRouter();

const loading = ref(false);
const total = ref(0);
const userStore = useUserStore();
const architectureStore = useArchitectureStoreHook();
const eleFormRef = ref(null);
const eleFormAllList = ref(screenAllList);

// 为案池管理页面创建独立的批次号实例
const poolBatchNumberInstance = createBatchNumberField();
const {
  batchNumber: poolBatchNumber,
  getDynamicBatchNumber: poolGetDynamicBatchNumber,
  clearBatchNumberCache: poolClearBatchNumberCache,
} = poolBatchNumberInstance;

// 替换筛选配置中的批次号字段
const poolScreenAllList = screenAllList.map((field) => {
  if (field.field === "batchNumber") {
    return poolBatchNumber;
  }
  return field;
});
eleFormAllList.value = poolScreenAllList;

const queryParams = reactive<RolePageQuery>({
  pageNum: 1,
  pageSize: 10,
});

// 表格数据
const roleList = ref<[]>();

const dataTableRef = ref(null);
//筛选字段默认值
const screenValue: any = ref({
  userOrDept: [],
  // lostContactQueryResult: 1,
});
// 选中的角色
const ids = ref([]);
//选中的总金额;
const checkTotal = ref(0);
//总委托金额
const sumEntrustTotalAmount = ref(0);
//总委托本金额
const sumentrustPrincipalTotal = ref(0);
watch(ids, (val) => {
  let arr = dataTableRef.value.getSelectionRows();
  //计算勾选总金额;
  checkTotal.value = arr.reduce((sum, e) => $add(sum, e.entrustTotalAmount || 0), 0);
});
// 分配案件
const allocationShow = ref(false);
//分配案件的id集合
const allocationIds = ref([]);
//标志新案
const tableRowClassName = ({ row, rowIndex }: any) => {
  if (row.followUpStatus === "新案") {
    return "res-nova";
  }
  return "";
};

/**
 * 获取表格内容可用高度
 * **/
const contentHeader = ref(450); // 调整表格高度，避免过高

// 获取表格高度
const getTableHeight = () => {
  const appMainH = document.querySelectorAll(".app-main")[0]?.offsetHeight;
  const searchHeight = document.querySelectorAll("#eleForm")[0]?.offsetHeight;
  const startTextH = document.querySelectorAll(".start_text")[0]?.offsetHeight;
  console.log(appMainH, searchHeight, startTextH);
  contentHeader.value = appMainH - searchHeight - startTextH - 130;
};
//数字排序
const scoreOrder = (a: any, b: any) => {
  console.log(a, b);
  // 自定义排序逻辑
  if (a.score < b.score) {
    return -1;
  }
  if (a.score > b.score) {
    return 1;
  }
  // 相同则返回0，表示不变
  return 0;
};
// 排序
const sortChange = (data: any) => {
  console.log(data);
  const { order, prop } = data;
  queryParams.orderBy = prop;
  queryParams.ascOrDesc = order == "descending" ? "desc" : order == "ascending" ? "asc" : "";
  // handleQuery();
};
// 重置筛选条件
function handleReset() {
  // 重置筛选条件到初始值
  const { userId, deptId, roles } = userStore.userInfo;
  if (deptId && roles[0] != "YG") {
    screenValue.value = { userOrDept: [deptId, userId] };
  } else {
    screenValue.value = { userOrDept: [userId] };
  }

  // 重新获取批次号选项
  poolGetDynamicBatchNumber({}, 'pool').catch((error) => {
    console.warn("重置时批次号更新失败:", error);
  });

  // 重新查询
  handleQuery();
}

// 查询
function handleQuery() {
  loading.value = true;
  let obj = {};
  sumEntrustTotalAmount.value = 0;

  // 添加错误处理
  let search = {};
  try {
    if (eleFormRef.value && eleFormRef.value.getFormVlaue) {
      search = eleFormRef.value.getFormVlaue();
      console.log("表单数据:", search);
    } else {
      console.warn("eleFormRef 或 getFormVlaue 方法不可用");
    }
  } catch (error) {
    console.error("获取表单数据时出错:", error);
  }

  for (let key in search) {
    if (search[key]) {
      if (key == "userOrDept") search[key] = search[key][search[key].length - 1] || "";
      obj[key] = search[key];
    }
  }
  dataSystemAPI
    .getCasepoolPage({
      ...queryParams,
      ...obj,
    })
    .then((data: any) => {
      console.log("API返回完整数据:", data); // 添加调试日志
      console.log("API返回数据类型:", typeof data);
      console.log("data.list:", data.list);
      console.log("data.list长度:", data.list ? data.list.length : 'undefined');

      if (data.list && data.list.length > 0) {
        console.log("第一条数据:", data.list[0]);
        console.log("第一条数据的所有字段:", Object.keys(data.list[0]));
      }

      roleList.value = data.list || [];
      if (data.list && data.list.length > 0) {
        sumEntrustTotalAmount.value = data.list[0].sumEntrustTotalAmount || 0;
        sumentrustPrincipalTotal.value = data.list[0].sumentrustPrincipalTotal || 0;
      }
      total.value = data.total || 0;
      console.log("设置后的roleList.value:", roleList.value);
      console.log("设置后的total:", total.value);

      // 添加测试数据验证表格是否能正常显示
      if (!roleList.value || roleList.value.length === 0) {
        console.warn("没有数据或数据为空，添加测试数据");
        // 可以临时添加测试数据来验证表格是否正常工作
        // roleList.value = [
        //   {
        //     id: 1,
        //     customerName: "测试客户",
        //     customerIndexNumber: "TEST001",
        //     cardholderCode: "CARD001",
        //     entrustTotalAmount: 10000,
        //     entrustPrincipalTotal: 8000
        //   }
        // ];
        // total.value = 1;
      }
    })
    .catch((error: any) => {
      console.error("API调用错误:", error); // 添加错误日志
    })
    .finally(() => {
      loading.value = false;
    });
}
/**
 * 案池配置
 * type 1 锁定 2解锁
 * */
const goDisposition = (type: Number) => {
  ElMessageBox.confirm(`确认${type == 1 ? "锁定" : "解锁"}勾选的案件`, "提示", {
    confirmButtonText: "确认",
    cancelButtonText: "取消",
    type: "warning",
    draggable: true,
  }).then(() => {
    let apiName = "casepoolCaseConfig";
    if (type == 2) apiName = "casepoolUnCaseConfig";
    dataSystemAPI[apiName]({
      ids: ids.value,
    }).then((data: any) => {
      ElMessage({
        message: `${type == 1 ? "锁定" : "解锁"}成功`,
        type: "success",
      });
      loading.value = true;
      handleQuery();
    });
  });
};
// 详情
const handleOpenDialog = (id?: any) => {
  let search = removeEmptyValues(eleFormRef.value.getFormVlaue());
  if (search["userOrDept"])
    search["userOrDept"] = search["userOrDept"][search["userOrDept"].length - 1] || "";

  router.replace({
    path: "/dataSystem/pool/poolDetails",
    query: {
      id,
      pagesType: 1,
      search: JSON.stringify({
        ...search,
        ...queryParams,
      }),
    },
  });
};

// 行复选框选中
function handleSelectionChange(selection: any) {
  ids.value = selection.map((item: any) => item.id);
}

// 表格导入
function importBefore(file: any, value: string) {
  dataSystemAPI.postCasepoolImport(file, value).then((e: any) => {
    ElMessage({
      message: e,
      type: "success",
    });
  });
}

//导入弹窗
const userImport = () => {
  ElMessageBox.prompt("导入Excel表格", "提示", {
    inputPlaceholder: "请填写委案机构",
    confirmButtonText: "选择文件",
    inputPattern: /^[\s\S]*.*[^\s][\s\S]*$/,
    inputErrorMessage: "委托机构不能为空",
  })
    .then((res) => {
      const { action, value } = res;
      if (action == "confirm") {
        // 创建一个新的 input 元素
        const fileInput = document.createElement("input");
        fileInput.type = "file";
        // 设置 accept 属性以限制文件类型
        fileInput.accept = ".csv,.xlsx,.xls";
        fileInput.style.display = "none"; // 隐藏文件输入元素
        // 监听文件输入元素的变化事件
        fileInput.addEventListener("change", function (event) {
          const file = event.target.files;
          if (file) {
            // 检查文件扩展名（作为额外验证，因为 accept 属性可能不是万无一失的）
            const fileExtension = file[0].name.split(".").pop().toLowerCase();
            const allowedExtensions = ["csv", "xlsx", "xls"];
            if (allowedExtensions.includes(fileExtension)) {
              importBefore(file[0], value);
              // importBefore(file[0]);
              // 在这里处理文件，例如读取文件内容
            } else {
              // 重置文件输入（移除已选择的文件）
              event.target.value = "";
            }
          }
        });
        // 为了不直接显示在页面上，可以将其添加到一个不可见的容器中
        const container = document.createElement("div");
        container.style.display = "none";
        container.appendChild(fileInput);
        document.body.appendChild(container);
        // 触发文件输入元素的点击事件
        fileInput.click();
      }
    })
    .catch((res) => {
      console.log("报错", res);
    });
};

/**导出相关**/
const exportFormRef = ref(null);
const exportLoading = ref(false);
const exportShow = ref(false);
const exportForm = reactive({
  derivedValue: "导出选中数据",
  isTuiAn: false,
  fileName: "",
});

const derivedOptions = ["导出选中数据", "导出指定数据"];
//取消 重置
const exportHandleCloseDialog = () => {
  exportShow.value = false;
  exportFormRef.value.resetFields();
};
//确认
const exportHandleSubmit = () => {
  const { derivedValue, isTuiAn, fileName } = exportForm;
  let obj = {};
  // 导出部分
  if (derivedValue == "导出选中数据") {
    if (ids.value.length == 0) {
      return ElMessage({
        message: "请勾选需要导出的内容",
        type: "warning",
      });
    } else {
      obj.ids = ids.value;
    }
  } else {
    //导出全部
    let search = removeEmptyValues(eleFormRef.value.getFormVlaue());
    if (search["userOrDept"])
      search["userOrDept"] = search["userOrDept"][search["userOrDept"].length - 1] || "";
    obj = search;
  }
  if (!fileName || fileName.trim() == "") {
    return ElMessage({
      message: "请输入文件名",
      type: "warning",
    });
  } else {
    obj.fileName = fileName;
  }
  // 退案
  if (isTuiAn) {
    obj.isTuiAn = 1;
  }
  exportLoading.value = true;
  dataSystemAPI.casepoolCaseExport(obj).then((data: any) => {
    console.log(data);
    deriveExcel(data.titleList, data.list, data.fileName);
    nextTick(() => {
      exportLoading.value = false;
      if (isTuiAn) handleQuery();
      exportHandleCloseDialog();
    });
  });
};

//自动分配
const automaticAllocation = () => {
  ElMessageBox({
    title: "自动分配案池",
    draggable: true,
    showCancelButton: true,
    showConfirmButton: false,
    message: h("div", null, [
      // 这里用到了h函数
      h(ElButton, {
        text: true,
        type: "primary",
        innerHTML: "分配选中数据",
        onClick: automaticList,
      }),
      h(ElButton, {
        text: true,
        type: "success",
        innerHTML: "分配指定数据",
        onClick: () => {
          allocationIds.value = [];
          allocationShow.value = true;
        },
      }),
    ]),
  })
    .then((res) => {})
    .catch((res) => {});
};
//分配选中的案池
const automaticList = async () => {
  let list = ids.value;
  if (!list.length) {
    return ElMessage({
      message: "请选择需要分配的数据",
      type: "warning",
    });
  }
  allocationIds.value = list;
  allocationShow.value = true;
  // automaticAll(list);
};
//分配数据
const automaticAll = (list?: any) => {
  ElMessageBox.close(); // 关闭弹出框

  let obj = {};
  if (Array.isArray(list) && list.length > 0) obj.ids = list;
  dataSystemAPI.casepoolAllocate(obj).then((e) => {
    ElMessage({
      message: "分配成功",
      type: "success",
    });
    handleQuery();
  });
};
// 失联查询结果转换
const lostContactQueryResultName = computed(() => {
  return (type: any) => {
    const { options } = eleFormAllList.value.find((e: any) => e.label == "失联查询结果");
    let name = false;
    if (options && options.length > 0) {
      name = options.find((t: any) => t.value == type)?.label || "-";
    }
    return name;
  };
});
// 案件类型
const caseTypeName = computed(() => {
  return (type: any) => {
    const { options } = eleFormAllList.value.find((e: any) => e.label == "案件类型");
    let name = false;
    if (options && options.length > 0) {
      name = options.find((t: any) => t.value == type)?.label || "-";
    }
    return name;
  };
});

const initOptions = async () => {
  // casepoolKeyValueList("case_status");
  eleFormAllList.value.find((e: any) => e.field == "followUpStatus").options =
    await architectureStore.casepoolKeyValueList("lock_status");
  eleFormAllList.value.find((e: any) => e.field == "lostContactQueryResult").options =
    await architectureStore.casepoolKeyValueList("lost_status");

  // 初始化案池管理页面的批次号选项，使用案池接口（不需要传递userOrDept）
  await poolGetDynamicBatchNumber({}, 'pool');
  console.log("案池管理页面选项初始化完成");
};
// 移除 onUpdated 中的 initOptions 调用，避免重复请求
// onUpdated(async () => {
//   console.log("onUpdated");
//   initOptions();
// });
onMounted(async () => {
  const { userId, deptId, roles } = userStore.userInfo;
  if (deptId && roles[0] != "YG") {
    screenValue.value["userOrDept"] = [deptId, userId];
  } else {
    screenValue.value["userOrDept"] = [userId];
  }
  initOptions();
  setTimeout(() => {
    handleQuery();
    getTableHeight();
  }, 0);
});
</script>
<style>
.pool-container {
  background-color: #19275d;
  height: calc(100vh - 60px);
  padding: 16px;
  color: #2c3e50;
  font-weight: bold;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-sizing: border-box;
}

.filter-card {
  margin-bottom: 12px;
  border-radius: 12px;
  background: #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
  max-height: 280px;
  overflow: hidden;
}

.filter-card .el-card__body {
  padding: 16px;
  max-height: 240px;
  overflow-y: auto;
}

.filter-header {
  margin-bottom: 16px;
}

.filter-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 12px;
}

.filter-icon {
  margin-right: 8px;
  color: #409eff;
  font-size: 18px;
}

.title-text {
  color: #2c3e50;
}

.filter-divider {
  margin: 0;
  border-color: #e4e7ed;
}

/* 筛选条件表单项间距 */
.filter-card .el-form-item {
  margin-bottom: 16px;
}

/* 筛选条件标签样式 */
.filter-card .el-form-item__label {
  color: #2c3e50 !important;
  font-weight: bold !important;
  font-size: 14px;
}

/* 筛选条件内容区域 */
.filter-card .el-form-item__content {
  max-width: 200px;
}

/* 筛选条件输入框样式 */
.filter-card .el-input__wrapper {
  border-radius: 12px;
  height: 40px;
  width: 100%;
  max-width: 200px;
  border: 1px solid #e4e7ed;
  box-shadow: none;
  transition: all 0.3s;
  background-color: #f8f9fa;
}

.filter-card .el-input__wrapper:hover {
  border-color: #c0c4cc;
  background-color: #ffffff;
}

.filter-card .el-input__wrapper.is-focus {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
  background-color: #ffffff;
}

/* 筛选条件下拉框样式 */
.filter-card .el-select .el-input__wrapper {
  border-radius: 12px;
  height: 40px;
  width: 100%;
  max-width: 200px;
  border: 1px solid #e4e7ed;
  box-shadow: none;
  background-color: #f8f9fa;
  transition: all 0.3s;
}

.filter-card .el-select .el-input__wrapper:hover {
  border-color: #c0c4cc;
  background-color: #ffffff;
}

.filter-card .el-select .el-input__wrapper.is-focus {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
  background-color: #ffffff;
}

/* 筛选条件级联选择器样式 */
.filter-card .el-cascader .el-input__wrapper {
  border-radius: 12px;
  height: 40px;
  width: 100%;
  max-width: 200px;
  border: 1px solid #e4e7ed;
  box-shadow: none;
  background-color: #f8f9fa;
  transition: all 0.3s;
}

.filter-card .el-cascader .el-input__wrapper:hover {
  border-color: #c0c4cc;
  background-color: #ffffff;
}

.filter-card .el-cascader .el-input__wrapper.is-focus {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
  background-color: #ffffff;
}

/* 筛选条件日期选择器样式 */
.filter-card .el-date-editor.el-input {
  border-radius: 12px;
  height: 40px;
  width: 100%;
  max-width: 200px;
}

.filter-card .el-date-editor .el-input__wrapper {
  border-radius: 12px;
  height: 40px;
  border: 1px solid #e4e7ed;
  box-shadow: none;
  background-color: #f8f9fa;
  transition: all 0.3s;
}

.filter-card .el-date-editor .el-input__wrapper:hover {
  border-color: #c0c4cc;
  background-color: #ffffff;
}

.filter-card .el-date-editor .el-input__wrapper.is-focus {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
  background-color: #ffffff;
}

/* 筛选条件输入框内部文字样式 */
.filter-card .el-input__inner {
  height: 38px;
  line-height: 38px;
  border-radius: 12px;
  font-size: 14px;
  color: #606266;
  background-color: transparent;
}

/* 筛选条件占位符样式 */
.filter-card .el-input__wrapper input::placeholder {
  color: #c0c4cc;
  font-size: 14px;
}

/* 筛选条件下拉箭头样式 */
.filter-card .el-select .el-input__suffix,
.filter-card .el-cascader .el-input__suffix {
  height: 40px;
  display: flex;
  align-items: center;
}

/* 筛选条件按钮样式 */
.filter-card .el-button {
  height: 40px;
  border-radius: 12px;
  padding: 0 20px;
  font-weight: 500;
  font-size: 14px;
  border: 1px solid #e4e7ed;
  background: #f8f9fa;
  color: #606266;
  min-width: 80px;
}

.filter-card .el-button--primary {
  background: #409eff;
  border-color: #409eff;
  color: #ffffff;
}

.filter-card .el-button:hover {
  border-color: #c0c4cc;
  background: #ffffff;
}

.filter-card .el-button--primary:hover {
  background: #66b1ff;
  border-color: #66b1ff;
}

/* 表单标签样式 */
.pool-container .el-form-item__label {
  color: #2c3e50 !important;
  font-weight: bold !important;
}

/* 表格和其他文本内容样式 */
.pool-container .el-table,
.pool-container .el-table th,
.pool-container .el-table td,
.pool-container .el-button,
.pool-container .tips span {
  color: #2c3e50;
  font-weight: bold;
}

/* 表头样式 - 单行不换行，居中对齐 */
.pool-container .el-table th {
  text-align: center !important;
}

.pool-container .el-table th .cell {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  text-align: center !important;
  justify-content: center !important;
  display: flex !important;
  align-items: center !important;
  font-weight: bold !important;
  color: #2c3e50 !important;
}

/* 表格内容居中对齐 */
.pool-container .el-table td {
  text-align: center !important;
}

.pool-container .el-table td .cell {
  text-align: center !important;
  justify-content: center !important;
  display: flex !important;
  align-items: center !important;
}

.data-header {
  margin-bottom: 12px;
  flex-shrink: 0;
}

/* 表格容器样式 */
.table-card :deep(.el-table) {
  flex: 1;
  height: 100%;
}

.table-card :deep(.el-table .el-table__body-wrapper) {
  overflow-y: auto;
  max-height: calc(100vh - 400px);
}

/* 分页容器样式 */
.pagination-container {
  margin-top: 12px;
  padding: 8px 0;
  flex-shrink: 0;
  display: flex;
  justify-content: center;
}

.data-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 12px;
}

.data-icon {
  margin-right: 8px;
  color: #409eff;
  font-size: 18px;
}

.data-divider {
  margin: 0;
  border-color: #e4e7ed;
}

.table-card {
  border-radius: 12px;
  background: #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-height: 0;
}

.table-card .el-card__body {
  padding: 12px;
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-height: 0;
}

.action-section {
  margin-bottom: 12px;
  padding: 6px 0;
  color: #ffffff;
  height: 45px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.stats-info {
  display: flex;
  align-items: center;
}

.action-section .el-button {
  background-color: #19275d;
  border-color: #19275d;
  color: #ffffff;
  font-weight: bold;
  border-radius: 4px;
  padding: 10px 20px;
  font-size: 14px;
  min-width: 80px;
  height: 36px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.action-section .el-button:hover {
  background-color: #2a3a7a;
  border-color: #2a3a7a;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.action-section .el-button:disabled {
  background-color: #6c757d;
  border-color: #6c757d;
  color: #ffffff;
  opacity: 0.6;
  box-shadow: none;
}

.action-section .el-button:active {
  background-color: #1a2654;
  border-color: #1a2654;
  transform: translateY(1px);
}

.action-section .tips span {
  color: #ffffff;
  font-weight: bold;
}

.flex-between {
  display: flex;
  justify-content: space-between;
}

.flex-center {
  display: flex;
  justify-content: center;
}

.flex-start {
  display: flex;
  align-items: center;
}

.tips {
  span {
    margin-right: 2px;
  }

  text {
    font-weight: bold;
    color: red;
  }
}

.el-table .res-nova {
  --el-table-tr-bg-color: var(--el-color-danger-light-9);
}
</style>

<style scoped>
/* 案池管理页面专用样式 */
.action-section {
  margin-bottom: 4px;
  padding: 2px 0;
  color: #ffffff;
  min-height: 32px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filter-card {
  margin-bottom: 6px;
}

/* 案池管理筛选条件样式 - 强制label和输入框左右排列 */
.pool-filter-card :deep(.el-form-item) {
  display: flex !important;
  flex-direction: row !important;
  align-items: center !important;
  margin-bottom: 15px !important;
}

.pool-filter-card :deep(.el-form-item__label) {
  width: 80px !important;
  min-width: 80px !important;
  max-width: 80px !important;
  text-align: left !important;
  font-size: 12px !important;
  font-weight: 600 !important;
  color: #2c3e50 !important;
  white-space: nowrap !important;
  margin-right: 8px !important;
  margin-bottom: 0 !important;
  padding: 0 !important;
  line-height: 32px !important;
}

.pool-filter-card :deep(.el-form-item__content) {
  flex: 1 !important;
  max-width: none !important;
  margin-left: 0 !important;
  width: auto !important;
}

.pool-filter-card :deep(.el-input__wrapper) {
  height: 32px !important;
  font-size: 12px !important;
  border: 1px solid #d1d5db !important;
  border-radius: 4px !important;
  background-color: #ffffff !important;
}

.pool-filter-card :deep(.el-select .el-input__wrapper) {
  height: 32px !important;
  font-size: 12px !important;
  border: 1px solid #d1d5db !important;
  border-radius: 4px !important;
  background-color: #ffffff !important;
}

.pool-filter-card :deep(.el-date-editor .el-input__wrapper) {
  height: 32px !important;
  font-size: 12px !important;
  border: 1px solid #d1d5db !important;
  border-radius: 4px !important;
  background-color: #ffffff !important;
}

.pool-filter-card :deep(.el-cascader .el-input__wrapper) {
  height: 32px !important;
  font-size: 12px !important;
  border: 1px solid #d1d5db !important;
  border-radius: 4px !important;
  background-color: #ffffff !important;
}

/* 强制统一所有输入控件的宽度和样式 - 最高优先级 */
.filter-card :deep(.el-form-item__content),
.filter-card :deep(.el-form-item__content > *),
.filter-card :deep(.el-form-item__content .el-input),
.filter-card :deep(.el-form-item__content .el-select),
.filter-card :deep(.el-form-item__content .el-date-editor),
.filter-card :deep(.el-form-item__content .el-cascader) {
  width: 100% !important;
  max-width: 100% !important;
  min-width: 0 !important;
}

/* 强制统一所有输入框包装器的样式 */
.filter-card :deep(.el-form-item__content .el-input__wrapper),
.filter-card :deep(.el-form-item__content .el-select .el-input__wrapper),
.filter-card :deep(.el-form-item__content .el-date-editor .el-input__wrapper),
.filter-card :deep(.el-form-item__content .el-cascader .el-input__wrapper),
.filter-card :deep(.el-input__wrapper),
.filter-card :deep(.el-select .el-input__wrapper),
.filter-card :deep(.el-date-editor .el-input__wrapper),
.filter-card :deep(.el-cascader .el-input__wrapper) {
  width: 100% !important;
  max-width: 100% !important;
  min-width: 0 !important;
  height: 32px !important;
  font-size: 12px !important;
  border: 1px solid #d1d5db !important;
  border-radius: 4px !important;
  background-color: #ffffff !important;
  box-sizing: border-box !important;
}

/* 特别处理下拉框 */
.filter-card :deep(.el-select),
.filter-card :deep(.el-select .el-input) {
  width: 100% !important;
  display: block !important;
}

/* 特别处理日期选择器 */
.filter-card :deep(.el-date-editor) {
  width: 100% !important;
  display: block !important;
}

/* 特别处理级联选择器 */
.filter-card :deep(.el-cascader) {
  width: 100% !important;
  display: block !important;
}

/* 强制表单项一行显示5个 - 只针对表单项容器 */
.pool-filter-card :deep(.el-form .el-row .el-col .el-row > .el-col) {
  width: 20% !important;
  max-width: 20% !important;
  flex: 0 0 20% !important;
}

/* 特别处理数值范围选择器（委托时金额段） */
.filter-card :deep(.number-range-container) {
  width: 100% !important;
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
}

.filter-card :deep(.number-range-container .el-input) {
  width: 100% !important;
  flex: 1 !important;
}

.filter-card :deep(.number-range-container .el-input__wrapper) {
  width: 100% !important;
  height: 32px !important;
  font-size: 12px !important;
  border: 1px solid #d1d5db !important;
  border-radius: 4px !important;
  background-color: #ffffff !important;
  box-sizing: border-box !important;
}

.filter-card :deep(.number-range-container .range-separator) {
  color: #606266 !important;
  font-size: 12px !important;
  white-space: nowrap !important;
  flex-shrink: 0 !important;
}

/* 操作按钮样式 - 靠右对齐 */
.filter-card :deep(.operate-box) {
  display: flex !important;
  gap: 12px !important;
  align-items: center !important;
  justify-content: flex-end !important;
  width: 100% !important;
  padding: 0 !important;
}

.filter-card :deep(.operate-box .el-button) {
  height: 32px !important;
  font-size: 12px !important;
  padding: 8px 20px !important;
  border-radius: 4px !important;
  min-width: 70px !important;
  font-weight: 500 !important;
}

.filter-card :deep(.operate-box .el-button--primary) {
  background-color: #409eff !important;
  border-color: #409eff !important;
  color: #ffffff !important;
}

.filter-card :deep(.operate-box .el-button--default) {
  background-color: #ffffff !important;
  border-color: #dcdfe6 !important;
  color: #606266 !important;
}

/* 配置状态下拉框选项样式 */
.el-select-dropdown__item[data-value="0"] {
  background-color: #e8f5e8 !important;
  color: #52c41a !important;
  border-radius: 4px !important;
  margin: 2px 4px !important;
  padding: 8px 12px !important;
}

.el-select-dropdown__item[data-value="1"] {
  background-color: #e6f7ff !important;
  color: #1890ff !important;
  border-radius: 4px !important;
  margin: 2px 4px !important;
  padding: 8px 12px !important;
}

.el-select-dropdown__item[data-value="0"]:hover {
  background-color: #d9f7be !important;
  color: #389e0d !important;
}

.el-select-dropdown__item[data-value="1"]:hover {
  background-color: #bae7ff !important;
  color: #096dd9 !important;
}

/* 配置状态选中值的样式 */
.filter-card :deep(.el-select .el-input__inner) {
  color: #606266 !important;
}

/* 为配置状态字段的选中值添加背景色 */
.filter-card :deep(.el-select[data-field="followUpStatus"] .el-input__inner) {
  background-color: #f8f9fa !important;
}
</style>
