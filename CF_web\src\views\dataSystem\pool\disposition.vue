<template>
  <div class="app-container">
    <div class="search-bar">
      <eleForm
        ref="eleFormRef"
        :formValue="screenValue"
        :formItemList="eleFormAllList"
        :defineTxt="'提交'"
        @handleSubmit="handleQuery"
        @handleCancel="handleQuery"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: "Pool",
  inheritAttrs: false,
});
import eleForm from "@/components/EleComponents/ele-form.vue";
import { screenList, screenAllList, listFields } from "./fieldsSys";
import { removeEmptyValues } from "@/utils/commit";
import RoleAPI, { RolePageVO, RoleForm, RolePageQuery } from "@/api/system/role";
import dataSystemAPI from "@/api/dataSystem/pool";
import { useUserStore, useArchitectureStoreHook } from "@/store";

const router = useRouter();

const loading = ref(false);
const total = ref(0);
const userStore = useUserStore();
const architectureStore = useArchitectureStoreHook();
const eleFormRef = ref(null);
const eleFormAllList = ref(screenAllList);
const queryParams = reactive<RolePageQuery>({
  pageNum: 1,
  pageSize: 10,
});

// 表格数据
const roleList = ref<[]>();

//筛选字段默认值
const screenValue: any = ref({
  userOrDept: [],
  // lostContactQueryResult: 1,
});

// 查询
function handleQuery() {
  let obj = {};
  let search = removeEmptyValues(eleFormRef.value.getFormVlaue());
  console.log(search);
  if (Object.keys(search).length === 0) {
    return ElMessage({
      message: "搜索条件必须填写一个！",
      type: "warning",
    });
  }
  for (let key in search) {
    if (search[key]) {
      if (key == "userOrDept") search[key] = search[key][search[key].length - 1] || "";
      obj[key] = search[key];
    }
  }

  dataSystemAPI.casepoolCaseConfig(obj).then((data: any) => {
    ElMessage({
      message: "配置成功",
      type: "success",
    });
  });
}

const initOptions = async () => {
  // casepoolKeyValueList("case_status");
  eleFormAllList.value.find((e: any) => e.label == "跟进状态").options =
    await architectureStore.casepoolKeyValueList("case_status");
  eleFormAllList.value.find((e: any) => e.label == "失联查询结果").options =
    await architectureStore.casepoolKeyValueList("lost_status");
};
// 移除 onUpdated 中的 initOptions 调用，避免重复请求
// onUpdated(async () => {
//   console.log("onUpdated");
//   initOptions();
// });
onMounted(async () => {
  const { userId, deptId, roles } = userStore.userInfo;
  if (deptId && roles[0] != "YG") {
    screenValue.value["userOrDept"] = [deptId, userId];
  } else {
    screenValue.value["userOrDept"] = [userId];
  }
  initOptions();
});
</script>
<style>
.flex-between {
  display: flex;
  justify-content: space-between;
}

.flex-center {
  display: flex;
  justify-content: center;
}

.flex-start {
  display: flex;
  align-items: center;
}

.tips {
  span {
    margin-right: 2px;
  }

  text {
    font-weight: bold;
    color: red;
  }
}

.el-table .res-nova {
  --el-table-tr-bg-color: var(--el-color-danger-light-9);
}
</style>
