package com.linhong.boot.management.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.linhong.boot.management.model.entity.TransferRecords;
import com.linhong.boot.management.model.query.CcUserQuery;
import com.linhong.boot.management.model.vo.TransferRecordsVO;

/**
 * 分配历史记录
 */
public interface TransferRecordsService extends IService<TransferRecords>{

	/**
	 * @param ccId 案件id
	 * @param sourceUserId 源催收人
	 * @param sourceDept 源组织
	 * @param targetUserID 目标催收人
	 * @param targetDept 目标组织
	 * @param operatorName 操作人
	 * @return TransferRecords 分派类型，0:案池分派 1:换单 2:锁定 3:主管分派 TransferRecordEnum
	 */
	public TransferRecords saveTransFerRecord(Long ccId, String sourceUserId ,String sourceDept ,String targetUserID ,String targetDept, String operatorName,String assignmentType);
	/**
	 * 导出  查询案件最近记录
	 * @param list
	 * @return
	 */
	public List<TransferRecords> selectUserOrDept(List<String> list);
	/**
	 * 查询换单记录
	 * @param query
	 * @return
	 */
	public Page<TransferRecordsVO> selectTransPage(CcUserQuery query);
}
