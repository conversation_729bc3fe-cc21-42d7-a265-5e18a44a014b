// 登录页配置相关的Mock数据

// 模拟数据存储
let mockData = {
  basicConfig: {
    logo: '/images/logo.png',
    systemTitle: 'CF-作业系统',
    cultureTitle: '企业文化'
  },
  cultureItems: [
    { icon: '😊', content: '托底人生，共筑未来!' },
    { icon: '😊', content: '新百年基业，成万千大众!' },
    { icon: '👑', content: '万千大众共生日，便民县业永同时!' },
    { icon: '📚', content: '做大、做强、做优秀、做永恒!' },
    { icon: '⭐', content: '不受谈股权国家，而是通过为客户创造价值，推动行业健康发展，不辜负社会所托，以实际行动诠释企业的社会责任。' }
  ]
}

// 默认配置
const defaultConfig = {
  basicConfig: {
    logo: '/images/logo.png',
    systemTitle: 'CF-作业系统',
    cultureTitle: '企业文化'
  },
  cultureItems: [
    { icon: '😊', content: '托底人生，共筑未来!' },
    { icon: '😊', content: '新百年基业，成万千大众!' },
    { icon: '👑', content: '万千大众共生日，便民县业永同时!' },
    { icon: '📚', content: '做大、做强、做优秀、做永恒!' }
  ]
}

export default [
  // 获取登录页配置（公开接口）
  {
    url: '/api/login/config/all',
    method: 'get',
    response: () => {
      return {
        code: 200,
        message: 'success',
        data: {
          logo: mockData.basicConfig.logo,
          systemTitle: mockData.basicConfig.systemTitle,
          cultureTitle: mockData.basicConfig.cultureTitle,
          cultureItems: mockData.cultureItems
        }
      }
    }
  },

  // 获取可编辑配置（管理后台）
  {
    url: '/api/system/login-config',
    method: 'get',
    response: () => {
      return {
        code: 200,
        message: 'success',
        data: {
          basicConfig: mockData.basicConfig,
          cultureItems: mockData.cultureItems
        }
      }
    }
  },

  // 更新基础配置
  {
    url: '/api/system/login-config/basic',
    method: 'put',
    response: (req) => {
      const { systemTitle, cultureTitle, logo } = req.body
      
      if (systemTitle) mockData.basicConfig.systemTitle = systemTitle
      if (cultureTitle) mockData.basicConfig.cultureTitle = cultureTitle
      if (logo) mockData.basicConfig.logo = logo
      
      return {
        code: 200,
        message: '基础配置更新成功'
      }
    }
  },

  // 更新企业文化配置
  {
    url: '/api/system/login-config/culture',
    method: 'put',
    response: (req) => {
      const cultureItems = req.body
      
      if (Array.isArray(cultureItems)) {
        mockData.cultureItems = cultureItems
      }
      
      return {
        code: 200,
        message: '企业文化配置更新成功'
      }
    }
  },

  // 上传LOGO
  {
    url: '/api/system/login-config/upload/logo',
    method: 'post',
    response: () => {
      // 模拟上传成功，返回新的LOGO URL
      const timestamp = Date.now()
      const newLogoUrl = `/uploads/logo/logo_${timestamp}.png`
      
      // 更新配置中的LOGO
      mockData.basicConfig.logo = newLogoUrl
      
      return {
        code: 200,
        message: 'LOGO上传成功',
        data: newLogoUrl
      }
    }
  },

  // 恢复默认配置
  {
    url: '/api/system/login-config/reset',
    method: 'post',
    response: () => {
      // 恢复到默认配置
      mockData = JSON.parse(JSON.stringify(defaultConfig))
      
      return {
        code: 200,
        message: '已恢复默认配置'
      }
    }
  }
]
