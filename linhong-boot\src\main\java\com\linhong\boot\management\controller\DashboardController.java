package com.linhong.boot.management.controller;

import com.linhong.boot.common.result.Result;
import com.linhong.boot.management.model.vo.CaseDistributionVO;
import com.linhong.boot.management.model.vo.RepaymentTrendVO;
import com.linhong.boot.management.service.DashboardService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 仪表板控制器
 */
@Tag(name = "仪表板管理")
@RestController
@RequestMapping("/api/v1/dashboard")
@RequiredArgsConstructor
public class DashboardController {

    private final DashboardService dashboardService;

    @Operation(summary = "获取回款趋势数据")
    @GetMapping("/repayment-trend")
    public Result<RepaymentTrendVO> getRepaymentTrend(
            @Parameter(description = "开始日期", example = "2024-01-01") @RequestParam(required = false) String startDate,
            @Parameter(description = "结束日期", example = "2024-01-31") @RequestParam(required = false) String endDate,
            @Parameter(description = "分析类型", example = "amount") @RequestParam(defaultValue = "amount") String period,
            @Parameter(description = "操作员ID") @RequestParam(required = false) String operatorId
    ) {
        // 如果没有提供日期，默认使用最近30天
        LocalDate end = endDate != null ? LocalDate.parse(endDate) : LocalDate.now();
        LocalDate start = startDate != null ? LocalDate.parse(startDate) : end.minusDays(29);

        RepaymentTrendVO data = dashboardService.getRepaymentTrend(start, end, period, operatorId);
        return Result.success(data);
    }

    @Operation(summary = "获取案件分布数据")
    @GetMapping("/case-distribution")
    public Result<List<CaseDistributionVO>> getCaseDistribution(
            @Parameter(description = "类型：department-部门，personal-个人", example = "department")
            @RequestParam(defaultValue = "department") String type
    ) {
        List<CaseDistributionVO> result = dashboardService.getCaseDistribution(type);
        return Result.success(result);
    }
}
