server:
  port: 8989

spring:
  jackson:
    ## 默认序列化时间格式
    date-format: yyyy-MM-dd HH:mm:ss
    ## 默认序列化时区
    time-zone: GMT+8
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ***************************************************************************************************************************************
    username: root
    password: root
  data:
    redis:
      database: 0
      host: **************
      port: 6379
#      password: win@123
      timeout: 10s
      lettuce:
        pool:
          # 连接池最大连接数 默认8 ，负数表示没有限制
          max-active: 8
          # 连接池最大阻塞等待时间（使用负值表示没有限制） 默认-1
          max-wait: -1
          # 连接池中的最大空闲连接 默认8
          max-idle: 8
          # 连接池中的最小空闲连接 默认0
          min-idle: 0
  cache:
    enabled: false
    # 缓存类型 redis、none(不使用缓存)
    type: redis
    # 缓存时间(单位：ms)
    redis:
      time-to-live: 3600000
      # 缓存null值，防止缓存穿透
      cache-null-values: true
mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*.xml
  global-config:
    db-config:
      # 主键ID类型
      id-type: none
      # 逻辑删除全局属性名(驼峰和下划线都支持)
      logic-delete-field: isDeleted
      # 逻辑删除-删除值
      logic-delete-value: 1
      # 逻辑删除-未删除值
      logic-not-delete-value: 0
  configuration:
    # 驼峰下划线转换
    map-underscore-to-camel-case: true
    # 这个配置会将执行的sql打印出来，在开发或测试的时候可以用
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

# 安全配置
security:
  jwt:
    # JWT 秘钥
    key: SecretKey012345678901234567890123456789012345678901234567890123456789
    # 访问令牌 有效期(单位：秒)，默认 1 小时
    access-token-expiration: 14400
    # 刷新令牌有效期(单位：秒)，默认 7 天
    refresh-token-expiration: 604800
  # 白名单列表
  ignore-urls:
    - /v3/api-docs/**
    - /doc.html
    - /swagger-resources/**
    - /webjars/**
    - /doc.html
    - /swagger-ui/**
    - /swagger-ui.html
    - /api/v1/auth/captcha
    - /api/v1/auth/refresh-token
    - /api/v1/dict-data/page
    - /api/login/config/all
    - /api/v1/forget-password/send-request
    - /uploads/**
    - /ws/**
# springdoc配置： https://springdoc.org/properties.html
springdoc:
  swagger-ui:
    path: /swagger-ui.html
    operationsSorter: alpha
    tags-sorter: alpha
  api-docs:
    path: /v3/api-docs
  group-configs:
    - group: '系统管理'
      paths-to-match: "/**"
      packages-to-scan:
        - com.linhong.boot.system.controller
        - com.linhong.boot.shared.auth.controller
        - com.linhong.boot.shared.file.controller
        - com.linhong.boot.shared.codegen.controller
    - group: '案件管理'
      paths-to-match: "/**"
      packages-to-scan:
       - com.linhong.boot.management.controller
  default-flat-param-object: true

# knife4j 接口文档配置
knife4j:
  # 是否开启 Knife4j 增强功能
  enable: true  # 设置为 true 表示开启增强功能
  # 生产环境配置
  production: false
  setting:
    language: zh_cn

# xxl-job 定时任务配置
xxl:
  job:
    # 定时任务开关
    enabled: false
    admin:
      # 多个地址使用,分割
      addresses: http://127.0.0.1:8080/xxl-job-admin
    accessToken: default_token
    executor:
      appname: xxl-job-executor-${spring.application.name}
      address:
      ip:
      port: 9999
      logpath: /data/applogs/xxl-job/jobhandler
      logretentiondays: 30

# 验证码配置
captcha:
    # 验证码类型 circle-圆圈干扰验证码|gif-Gif验证码|line-干扰线验证码|shear-扭曲干扰验证码
    type: circle
    # 验证码宽度
    width: 120
    # 验证码高度
    height: 40
    # 验证码干扰元素个数
    interfere-count: 2
    # 文本透明度(0.0-1.0)
    text-alpha: 0.8
    # 验证码字符配置
    code:
      # 验证码字符类型 math-算术|random-随机字符
      type: math
      # 验证码字符长度，type=算术时，表示运算位数(1:个位数运算 2:十位数运算)；type=随机字符时，表示字符个数
      length: 1
    # 验证码字体
    font:
      # 字体名称 Dialog|DialogInput|Monospaced|Serif|SansSerif
      name: SansSerif
      # 字体样式 0-普通|1-粗体|2-斜体
      weight: 1
      # 字体大小
      size: 24
    # 验证码有效期(秒)
    expire-seconds: 120

# 对象存储配置
oss:
  # 对象存储类型 (aliyun|minio|local)
  type: local
  local:
    # 本地上传路径 (Windows开发环境)
    upload-path: D:/uploads
    # 访问域名
    domain: http://localhost:8989
  minio:
    # MinIO 服务地址
    endpoint: http://localhost:9000
    # 访问凭据
    access-key: minioadmin
    # 凭据密钥
    secret-key: minioadmin
    # 存储桶名称
    bucket-name: linhong
    # 自定义域名(可选)
    custom-domain:

