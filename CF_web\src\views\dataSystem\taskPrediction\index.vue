<template>
  <div class="app-container task-prediction-container">
    <!-- 筛选条件卡片 -->
    <el-card shadow="never" class="filter-card">
      <div class="filter-header">
        <div class="filter-title">
          <el-icon class="filter-icon">
            <Filter />
          </el-icon>
          <span class="title-text">新增筛选条件</span>
        </div>
        <el-divider class="filter-divider" />
      </div>
      <eleForm
        id="eleForm"
        ref="eleFormRef"
        :formValue="screenValue"
        :formItemList="eleFormAllList"
        :formBox="{
          spanItem: 6,
          formContent: 18,
          operate: 6,
          operateItem: 24,
          operateGrid: 'start',
        }"
        :operateOpen="true"
        defineTxt="搜索"
        cancelTxt="重置"
        @handleSubmit="handleQuery"
        @handleCancel="handleReset"
      />
    </el-card>

    <!-- 数据表格卡片 -->
    <el-card shadow="never" class="table-card">
      <div class="data-header">
        <div class="data-title">
          <el-icon class="data-icon">
            <Grid />
          </el-icon>
          <span class="title-text">新增列表</span>
        </div>
        <el-divider class="data-divider" />
      </div>
      <el-table
        ref="dataTableRef"
        v-loading="loading"
        :data="roleList"
        highlight-current-row
        border
        style="width: 100%"
        :height="contentHeader"
      >
        <el-table-column
          v-for="item in jobListFields"
          :key="item.prop"
          :label="item.label"
          :prop="item.prop"
          :width="item.width || ''"
          sortable
        />
        <el-table-column fixed="right" label="操作" width="100">
          <template #default="scope">
            <el-button
              type="primary"
              size="small"
              link
              icon="delete"
              @click="taskPredictionDelete(scope.row.id)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="handleQuery"
      />
    </el-card>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: "ChangeRecords",
  inheritAttrs: false,
});
import eleForm from "@/components/EleComponents/ele-form.vue";
import { userOrDept, screenList, batchNumber, jobListFields, getBatchNumber } from "./fieldsSys";
import { ElMessage, ElMessageBox, ElButton, ElLoading } from "element-plus";
import { Filter, Grid } from "@element-plus/icons-vue";
import RoleAPI, { RolePageVO, RoleForm, RolePageQuery } from "@/api/system/role";
import changeRecordsAPI from "@/api/dataSystem/changeRecords";
import { $add } from "@/utils/calculate";
import { removeEmptyValues } from "@/utils/commit";
import { useUserStore, useArchitectureStoreHook } from "@/store";
import { deriveExcel } from "@/utils/tools";
import taskPredictionApi from "@/api/dataSystem/taskPrediction";

const router = useRouter();
const eleFormRef = ref(null);
const loading = ref(false);
const total = ref(0);

const eleFormAllList = ref([userOrDept, ...screenList, batchNumber]);
const architectureStore = useArchitectureStoreHook();
const userStore = useUserStore();
const queryParams = reactive<RolePageQuery>({
  pageNum: 1,
  pageSize: 10,
});

// 表格数据
const roleList = ref<[]>();

const dataTableRef = ref(null);
//筛选字段默认值
const screenValue: any = ref({
  userOrDept: [],
  // lostContactQueryResult: 1,
});

// 查询
function handleQuery() {
  loading.value = true;
  let obj = {};
  let search = eleFormRef.value.getFormVlaue();
  for (let key in search) {
    if (search[key]) {
      if (key == "userOrDept") search[key] = search[key][search[key].length - 1] || "";
      obj[key] = search[key];
    }
  }
  taskPredictionApi
    .taskRepayTaskPage({
      ...queryParams,
      ...obj,
    })
    .then((data: any) => {
      roleList.value = data.list;
      total.value = data.total;
    })
    .finally(() => {
      loading.value = false;
    });
}

const taskPredictionDelete = (id: number) => {
  ElMessageBox.confirm("确认删除该任务预测?", "提示", {
    confirmButtonText: "确认",
    cancelButtonText: "取消",
    type: "warning",
    draggable: true,
  }).then(() => {
    loading.value = true;
    taskPredictionApi
      .taskRepayDelete({
        id: id,
      })
      .then((e) => {
        ElMessage({
          message: "删除成功",
          type: "success",
        });
        loading.value = false;
        handleQuery();
      });
  });
};
/**
 * 获取表格内容可用高度
 * **/
const contentHeader = ref(0);

// 获取表格高度
const getTableHeight = () => {
  const appMainH = document.querySelectorAll(".app-main")[0]?.offsetHeight;
  const searchHeight = document.querySelectorAll("#eleForm")[0]?.offsetHeight;
  contentHeader.value = appMainH - searchHeight - 130;
};

// 初始化选项数据
const initOptions = async () => {
  console.log("初始化选项数据...");

  // 并行加载所有选项数据，提高性能
  const [workStatusOptions, lostStatusOptions] = await Promise.all([
    architectureStore.casepoolKeyValueList("work_status"),
    architectureStore.casepoolKeyValueList("lost_status"),
  ]);

  // 设置跟进状态选项
  const followUpStatusField = eleFormAllList.value.find((e: any) => e.field == "followUpStatus");
  if (followUpStatusField) {
    followUpStatusField.options = workStatusOptions;
  }

  // 设置失联查询结果选项
  const lostContactField = eleFormAllList.value.find((e: any) => e.field == "lostContactQueryResult");
  if (lostContactField) {
    lostContactField.options = lostStatusOptions;
  }

  console.log("选项数据初始化完成");
};

onUpdated(async () => {});
onMounted(async () => {
  const { userId, deptId, roles } = userStore.userInfo;
  if (deptId && roles[0] != "YG") {
    screenValue.value["userOrDept"] = [deptId, userId];
  } else {
    screenValue.value["userOrDept"] = [userId];
  }

  // 初始化选项数据
  await initOptions();

  // 初始化批次号选项
  getBatchNumber();

  setTimeout(() => {
    handleQuery();
    getTableHeight();
  }, 0);
});
</script>
<style lang="scss" scoped>
.task-prediction-container {
  height: calc(100vh - 60px);
  padding: 12px;
  overflow: hidden;
  box-sizing: border-box;
}

.filter-card {
  margin-bottom: 12px;
}

.filter-header,
.data-header {
  margin-bottom: 16px;
}

.filter-title,
.data-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: bold;
  color: #2c3e50;
}

.filter-icon,
.data-icon {
  margin-right: 8px;
  color: #409eff;
  font-size: 18px;
}

.filter-divider,
.data-divider {
  margin: 8px 0;
}

.table-card {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.flex-between {
  display: flex;
  justify-content: space-between;
}

.flex-center {
  display: flex;
  justify-content: center;
}

.flex-start {
  display: flex;
  align-items: center;
}

.tips {
  span {
    margin-right: 2px;
  }

  text {
    font-weight: bold;
    color: red;
  }
}

.el-table .res-nova {
  --el-table-tr-bg-color: var(--el-color-danger-light-9);
}

/* 优化跟进状态和失联查询结果下拉框样式 */
:deep(.el-select) {
  width: 100%;

  .el-input__wrapper {
    border-radius: 6px;
    border: 1px solid var(--el-border-color);
    transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
    box-shadow: 0 0 0 1px transparent inset;
  }

  .el-input__wrapper:hover {
    border-color: var(--el-border-color-hover);
  }

  &.is-focus .el-input__wrapper {
    border-color: var(--el-color-primary);
    box-shadow: 0 0 0 1px var(--el-color-primary-light-7) inset;
  }

  .el-input__inner {
    color: var(--el-text-color-regular);
    font-size: 14px;
  }

  .el-input__suffix {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

/* 下拉框弹出层样式优化 */
:deep(.el-select-dropdown) {
  border: 1px solid var(--el-border-color-light);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

  .el-select-dropdown__item {
    color: var(--el-text-color-regular);
    font-size: 14px;
    font-weight: 400;
    padding: 8px 12px;
    transition: all 0.2s ease;

    &:hover {
      background-color: var(--el-color-primary-light-9);
      color: var(--el-color-primary);
    }

    &.selected {
      background-color: var(--el-color-primary);
      color: white;
      font-weight: 500;
    }

    &.is-disabled {
      color: var(--el-text-color-disabled);
      cursor: not-allowed;
    }
  }
}

/* 针对跟进状态和失联查询结果字段的特殊样式 */
:deep(.el-form-item) {
  &:has([placeholder*="跟进状态"]) .el-select,
  &:has([placeholder*="失联查询结果"]) .el-select {
    .el-input__wrapper {
      background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
      border: 1px solid #e3e6f0;
    }

    .el-input__wrapper:hover {
      border-color: var(--el-color-primary-light-3);
      background: linear-gradient(135deg, #f0f8ff 0%, #ffffff 100%);
    }

    &.is-focus .el-input__wrapper {
      border-color: var(--el-color-primary);
      background: linear-gradient(135deg, #e6f3ff 0%, #ffffff 100%);
      box-shadow: 0 0 0 2px var(--el-color-primary-light-8) inset;
    }
  }
}
</style>
