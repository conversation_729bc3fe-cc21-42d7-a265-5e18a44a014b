package com.linhong.boot.management.model.query;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import lombok.Data;

@Data
public class RepaymentQuery{

	private Integer id;

	private Long ccId;

	private String outsourceSerialNumber;

	private List<String> batchNumber;

	private String cardholderCode;

	private String customerName;

	private String customerIndexNumber;

	private String caseType;

	private BigDecimal repaymentAmount;

	private Date repaymentDate;

	private String deptName;

	private String operatorId;
	
	private String userOrDept;
	
	private int pageNum = 1;

	private int pageSize = 10;
	
	private List<Long> ids;
	
	private List<Long> uIds;
	
	private String[] entrustStartDate;

}