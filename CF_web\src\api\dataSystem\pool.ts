import request from "@/utils/request";

const dataSystemAPI = {
  /**
   * 导入案池
   * @param file
   */
  postCasepoolImport(file: File, caseAgency: string) {
    const formData = new FormData();
    formData.append("file", file);
    formData.append("caseAgency", caseAgency);

    console.log(formData);
    return request<any, FileInfo>({
      url: "/api/v1/casepool/import",
      method: "post",
      data: formData,
      loading: true,
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
  },
  /**导出案池**/
  casepoolCaseExport(queryParams: any) {
    return request({
      url: "/api/v1/casepool/CaseExport",
      method: "post",
      data: queryParams,
      loading: true,
      loadingMsg: "导出中,请稍等...",
    });
  },

  /**作业清单-导出案池**/
  workCaseExport(queryParams: any) {
    return request({
      url: "/api/v1/work/CaseExport",
      method: "post",
      data: queryParams,
      loading: true,
      loadingMsg: "导出中,请稍等...",
    });
  },
  /**
   * 案池-分配案池
   * **/
  casepoolAllocate(queryParams: any) {
    return request({
      url: "/api/v1/casepool/allocate",
      method: "post",
      loading: true,
      loadingMsg: "分配中,请稍等...",
      data: queryParams,
    });
  },

  /**
   * 作业清单- 分配案池
   * **/
  workAllocate(queryParams: any) {
    return request({
      url: "/api/v1/work/allocate",
      method: "post",
      loading: true,
      loadingMsg: "分配中,请稍等...",
      data: queryParams,
    });
  },

  /**分配记录**/
  casepoolAllocatePage(queryParams: any) {
    return request({
      url: "/api/v1/casepool/allocatePage",
      method: "get",
      params: queryParams,
    });
  },

  /**
   * 换单
   * **/
  casepoolChange(queryParams: any) {
    return request({
      url: "/api/v1/casepool/change",
      method: "post",
      data: queryParams,
      loading: true,
      loadingMsg: "换单中,请稍等...",
    });
  },

  /**
   * 案池列表
   *
   *  @param queryParams 查询参数
   */
  getCasepoolPage(queryParams: casepoolPageQuery) {
    return request<any>({
      url: "/api/v1/casepool/page",
      method: "get",
      params: queryParams,
    });
  },
  /**
   * 催收人/组织 下拉列表
   */
  optionsAndUsers(queryParams?: any) {
    return request<any>({
      url: "/api/v1/dept/optionsAndUsers",
      method: "get",
      params: queryParams,
    });
  },
  /**
   * 作业清单列表
   *
   *  @param queryParams 查询参数
   */
  getCasepoolWorkPage(queryParams: casepoolPageQuery) {
    return request<any>({
      url: "/api/v1/casepool/workPage",
      method: "get",
      params: queryParams,
    });
  },

  /**
   * 获取案件详情
   */
  getCaseDetail(queryParams?: any) {
    return request<any>({
      url: "/api/v1/casepool/getCaseDetail",
      method: "get",
      params: queryParams,
    });
  },
  /**
   * 修改案件详情
   * **/
  casepoolUpdateCase(queryParams?: any) {
    return request<any>({
      url: "/api/v1/casepool/updateCase",
      method: "put",
      data: queryParams,
      loading: true,
    });
  },
  /**
   * 获取联系人列表
   * **/
  emergencyFindByPoolId(queryParams?: any) {
    return request<any>({
      url: "/api/v1/emergency/findByPoolId",
      method: "get",
      params: queryParams,
    });
  },

  /**
   * 获取联系人详情
   * **/
  emergencyDetail(queryParams?: any) {
    return request<any>({
      url: "/api/v1/emergency/detail",
      method: "get",
      params: queryParams,
    });
  },

  /**
   * 新增联系人
   * **/
  emergencySave(queryParams?: any) {
    return request<any>({
      url: "/api/v1/emergency/save",
      method: "post",
      data: queryParams,
      loading: true,
    });
  },
  /**
   * 编辑联系人
   * **/
  emergencyModify(queryParams?: any) {
    return request<any>({
      url: "/api/v1/emergency/modify",
      method: "put",
      data: queryParams,
    });
  },
  /**
   * 删除联系人列表
   * **/
  emergencyDelete(queryParams?: any) {
    return request<any>({
      url: "/api/v1/emergency/delete",
      method: "delete",
      params: queryParams,
    });
  },

  /**
   * 跟进记录列表
   * **/
  followUpRecordsFindByPoolId(queryParams?: any) {
    return request<any>({
      url: "/api/v1/followUpRecords/page",
      method: "get",
      params: queryParams,
    });
  },
  /**
   * 新增跟进记录
   * **/
  followUpRecordsSave(queryParams?: any) {
    return request<any>({
      url: "/api/v1/followUpRecords/save",
      method: "post",
      data: queryParams,
      loading: true,
    });
  },
  /**
   * 删除跟进记录
   * **/
  followUpRecordsDelete(queryParams?: any) {
    return request<any>({
      url: "/api/v1/followUpRecords/delete",
      method: "delete",
      params: queryParams,
    });
  },

  /***
   * 还款列表
   */
  casepoolRepaymentPage(queryParams?: any) {
    return request<any>({
      url: "/api/v1/casepool/repaymentPage",
      method: "get",
      params: queryParams,
    });
  },
  /**删除还款记录**/
  casepoolDeleteRepayment(queryParams?: any) {
    return request<any>({
      url: "/api/v1/casepool/deleteRepayment",
      method: "put",
      data: queryParams,
      loading: true,
    });
  },
  /**锁单列表**/
  casepoolLockCaseList(queryParams?: any) {
    return request<any>({
      url: "/api/v1/casepool/lockCaseList",
      method: "get",
      params: queryParams,
    });
  },
  /**案件锁定**/
  casepoolCaseLock(queryParams?: any) {
    return request<any>({
      url: "/api/v1/casepool/caseLock",
      method: "put",
      data: queryParams,
      loading: true,
    });
  },
  /**
   * 筛选条件
   * 需要的参数传1
   * 批次号:  batchNumber
   * 案件类型: caseType
   * 委托月龄分档: entrustAgeBracket
   * 佣金分档: entrustCommissionBracket
   * 委托机构 caseAgency
   * **/
  casepoolGetChangeList(queryParams?: any) {
    return request<any>({
      url: "/api/v1/casepool/getChangeList",
      method: "get",
      params: queryParams,
    });
  },


  /**
   * 状态记录查询
   * case_status 跟进状态
   * **/
  casepoolKeyValueList(queryParams?: any) {
    return request<any>({
      url: "/api/v1/casepool/keyValueList",
      method: "post",
      data: queryParams,
    });
  },

  /**
   * 任务预测列表
   */
  taskRepayPage(queryParams?: any) {
    return request<any>({
      url: "/api/v1/taskRepay/page",
      method: "get",
      params: queryParams,
    });
  },
  /**
   * 创建任务预测
   */
  taskRepaySave(queryParams?: any) {
    return request<any>({
      url: "/api/v1/taskRepay/save",
      method: "post",
      data: queryParams,
      loading: true,
    });
  },
  // 删除任务预测
  taskRepayDelete(queryParams?: any) {
    return request<any>({
      url: "/api/v1/taskRepay/delete",
      method: "post",
      data: queryParams,
      loading: true,
    });
  },

  /**
   * 案池配置锁定
   */
  casepoolCaseConfig(queryParams?: any) {
    return request<any>({
      url: "/api/v1/casepool/caseConfig",
      method: "post",
      data: queryParams,
      loading: true,
    });
  },
  /**
   * 配置解锁
   */
  casepoolUnCaseConfig(queryParams?: any) {
    return request<any>({
      url: "/api/v1/casepool/unCaseConfig",
      method: "post",
      data: queryParams,
      loading: true,
    });
  },

  /**
   * 批量退案
   */
  casepoolCaseTuiAn(queryParams?: any) {
    return request<any>({
      url: "/api/v1/casepool/caseTuiAn",
      method: "post",
      data: queryParams,
      loading: true,
      loadingMsg: "退案中,请稍等...",
    });
  },

  /**
   * 批量标注(批量修改跟进状态)
   */
  casepoolBatchUpdateStatus(queryParams?: any) {
    return request<any>({
      url: "/api/v1/casepool/batchUpdateStatus",
      method: "post",
      data: queryParams,
      loading: true,
      loadingMsg: "标注中,请稍等...",
    });
  },

  /* 案池详情目录*/
  casepoolMenuPage(queryParams?: any) {
    return request<any>({
      url: "/api/v1/casepool/menuPage",
      method: "get",
      params: queryParams,
    });
  },
  /* 作业清单详情目录*/
  workWorkMunePage(queryParams?: any) {
    return request<any>({
      url: "/api/v1/work/workMunePage",
      method: "get",
      params: queryParams,
    });
  },
  /* 还款记录详情目录*/
  casepoolRepaymentMenuPage(queryParams?: any) {
    return request<any>({
      url: "/api/v1/casepool/repaymentMenuPage",
      method: "get",
      params: queryParams,
    });
  },
};
export default dataSystemAPI;
/**
 * 文件API类型声明
 */
export interface FileInfo {
  /** 文件名 */
  name: string;
  /** 文件路径 */
  url: string;
}

/**
 * 用户分页查询对象
 */
export interface casepoolPageQuery extends PageQuery {}
