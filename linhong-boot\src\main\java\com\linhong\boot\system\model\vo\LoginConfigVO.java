package com.linhong.boot.system.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 登录页配置 VO
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "登录页配置VO")
public class LoginConfigVO {

    /**
     * 企业文化项VO
     */
    @Data
    @Schema(description = "企业文化项")
    public static class CultureItemVO {
        
        @Schema(description = "图标")
        private String icon;

        @Schema(description = "内容")
        private String content;
    }

    /**
     * 登录页配置VO（前端登录页使用）
     */
    @Data
    @Schema(description = "登录页配置")
    public static class LoginPageConfigVO {
        
        @Schema(description = "系统LOGO")
        private String logo;

        @Schema(description = "系统标题")
        private String systemTitle;

        @Schema(description = "企业文化标题")
        private String cultureTitle;

        @Schema(description = "企业文化列表")
        private List<CultureItemVO> cultureItems;
    }

    /**
     * 基础配置VO
     */
    @Data
    @Schema(description = "基础配置")
    public static class BasicConfigVO {
        
        @Schema(description = "系统标题")
        private String systemTitle;

        @Schema(description = "企业文化标题")
        private String cultureTitle;

        @Schema(description = "系统LOGO")
        private String logo;
    }

    /**
     * 可编辑配置VO（后台管理使用）
     */
    @Data
    @Schema(description = "可编辑配置")
    public static class EditableConfigVO {
        
        @Schema(description = "基础配置")
        private BasicConfigVO basicConfig;

        @Schema(description = "企业文化列表")
        private List<CultureItemVO> cultureItems;
    }
}
