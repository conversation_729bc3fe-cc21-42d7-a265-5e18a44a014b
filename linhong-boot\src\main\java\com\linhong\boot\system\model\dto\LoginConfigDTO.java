package com.linhong.boot.system.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.util.List;

/**
 * 登录页配置 DTO
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "登录页配置DTO")
public class LoginConfigDTO {

    /**
     * 基础配置DTO
     */
    @Data
    @Schema(description = "基础配置")
    public static class BasicConfigDTO {
        
        @Schema(description = "系统标题")
        @NotBlank(message = "系统标题不能为空")
        @Size(max = 50, message = "系统标题长度不能超过50个字符")
        private String systemTitle;

        @Schema(description = "企业文化标题")
        @NotBlank(message = "企业文化标题不能为空")
        @Size(max = 30, message = "企业文化标题长度不能超过30个字符")
        private String cultureTitle;

        @Schema(description = "系统LOGO")
        private String logo;
    }

    /**
     * 企业文化项DTO
     */
    @Data
    @Schema(description = "企业文化项")
    public static class CultureItemDTO {
        
        @Schema(description = "图标")
        @NotBlank(message = "图标不能为空")
        private String icon;

        @Schema(description = "内容")
        @NotBlank(message = "内容不能为空")
        @Size(max = 200, message = "内容长度不能超过200个字符")
        private String content;
    }

    /**
     * 登录页完整配置DTO
     */
    @Data
    @Schema(description = "登录页完整配置")
    public static class LoginPageConfigDTO {
        
        @Schema(description = "系统LOGO")
        private String logo;

        @Schema(description = "系统标题")
        private String systemTitle;

        @Schema(description = "企业文化标题")
        private String cultureTitle;

        @Schema(description = "企业文化列表")
        private List<CultureItemDTO> cultureItems;
    }

    /**
     * 可编辑配置DTO
     */
    @Data
    @Schema(description = "可编辑配置")
    public static class EditableConfigDTO {
        
        @Schema(description = "基础配置")
        private BasicConfigDTO basicConfig;

        @Schema(description = "企业文化列表")
        private List<CultureItemDTO> cultureItems;
    }
}
