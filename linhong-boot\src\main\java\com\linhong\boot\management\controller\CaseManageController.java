package com.linhong.boot.management.controller;

import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.linhong.boot.common.annotation.Log;
import com.linhong.boot.common.enums.CaseEnum;
import com.linhong.boot.common.enums.LogModuleEnum;
import com.linhong.boot.common.enums.TransferRecordEnum;
import com.linhong.boot.common.result.PageResult;
import com.linhong.boot.common.result.Result;
import com.linhong.boot.common.util.ExcelUtils;
import com.linhong.boot.common.util.ReflectiveCopierUtils;
import com.linhong.boot.common.util.StrUtils;
import com.linhong.boot.core.security.util.SecurityUtils;
import com.linhong.boot.management.listener.CcUserImportListener;
import com.linhong.boot.management.model.dto.CcUserDTO;
import com.linhong.boot.management.model.dto.CcUserExport;
import com.linhong.boot.management.model.dto.CcUserExtDTO;
import com.linhong.boot.management.model.entity.CcUser;
import com.linhong.boot.management.model.entity.CcUserWork;
import com.linhong.boot.management.model.entity.FollowUpRecords;
import com.linhong.boot.management.model.entity.FollowUpRecordsHistory;
import com.linhong.boot.management.model.entity.KeyValueStore;
import com.linhong.boot.management.model.entity.RepaymentRecords;
import com.linhong.boot.management.model.entity.TransferRecords;
import com.linhong.boot.management.model.query.CcUserExcQuery;
import com.linhong.boot.management.model.query.CcUserQuery;
import com.linhong.boot.management.model.query.ChangeCaseQuery;
import com.linhong.boot.management.model.query.RepaymentQuery;
import com.linhong.boot.management.model.vo.CcUserVo;
import com.linhong.boot.management.model.vo.ExcelTitleVo;
import com.linhong.boot.management.model.vo.TransferRecordsVO;
import com.linhong.boot.management.service.CasePoolService;
import com.linhong.boot.management.service.CcWorksService;
import com.linhong.boot.management.service.FollowUpRecordsHistoryService;
import com.linhong.boot.management.service.FollowUpRecordsService;
import com.linhong.boot.management.service.KeyValueStoreService;
import com.linhong.boot.management.service.RepaymentRecordService;
import com.linhong.boot.management.service.TransferRecordsService;
import com.linhong.boot.system.model.entity.User;
import com.linhong.boot.system.model.vo.UserProfileVO;
import com.linhong.boot.system.service.UserService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;

@Tag(name = "1.案池")
@RestController
@RequestMapping("/api/v1/casepool")
@RequiredArgsConstructor
public class CaseManageController {

	private static final long MAX_FILE_SIZE = 10 * 1024 * 1024;


	private final CasePoolService casePoolService;
	private final RepaymentRecordService repaymentRecordService;
	private final KeyValueStoreService keyValueStoreService;
	private final CcWorksService worksService;
	private final UserService userService;
	private final TransferRecordsService transferRecordsService;
	private final FollowUpRecordsHistoryService historyService;
	private final FollowUpRecordsService followUpRecordsService;

	@Operation(summary = "案池列表")
	@GetMapping("/page")
	@Log(value = "案池列表", module = LogModuleEnum.CASEPOOL)
	public PageResult<CcUserVo> getPoolPage(CcUserQuery query) {
		String followUpStatus = query.getFollowUpStatus();
		List<String> followStatusList = new ArrayList<>();
		followStatusList.add(CaseEnum.CASE_POOL.getValue().toString());
		followStatusList.add(CaseEnum.NEW_CASE.getValue().toString());
		followStatusList.add(CaseEnum.TUI_HUI.getValue().toString());
		if(followUpStatus==null) {
			query.setFollowUpStatusList(followStatusList);
		}
		if(followUpStatus!= null && followUpStatus.equals(CaseEnum.CASE_LOCK.getValue().toString())) {
			query.setFollowUpStatus(null);
			query.setIsLock("1");
		} else if (followUpStatus != null && followUpStatus.equals(CaseEnum.CASE_UNLOCK.getValue().toString())) {
			// followUpStatusList 赋值 ('0', '1', '12')
			query.setFollowUpStatus(null);
			query.setFollowUpStatusList(new ArrayList<>(Arrays.asList("0", "1", "12")));
			query.setIsLock("0");
		}
		IPage<CcUserVo> result = casePoolService.getPoolPage(query);
		return PageResult.success(result);
	}

	@Operation(summary = "案件详情里面,案池目录栏列表")
	@GetMapping("/menuPage")
	public PageResult<CcUserVo> getMenuPagePage(CcUserQuery query) {
		String followUpStatus = query.getFollowUpStatus();
		List<String> followStatusList = new ArrayList<>();
		followStatusList.add(CaseEnum.CASE_POOL.getValue().toString());
		followStatusList.add(CaseEnum.NEW_CASE.getValue().toString());
		followStatusList.add(CaseEnum.TUI_HUI.getValue().toString());
		if(followUpStatus==null) {
			query.setFollowUpStatusList(followStatusList);
		}
		if(followUpStatus!= null && followUpStatus.equals(CaseEnum.CASE_LOCK.getValue().toString())) {
			query.setFollowUpStatus(null);
			query.setIsLock("1");
		}
		IPage<CcUserVo> result = casePoolService.getPoolPage(query);
		List<CcUserVo> list = result.getRecords();
		if(list!=null) {
			CcUserVo v = null;
			for (CcUserVo c : list) {
				if(c.getId().equals(query.getId())) {
					v = c;
					break;
				}
			}
			if(v!=null) {
//				list.remove(v);
//				list.add(0,v);
//				result.setRecords(list);
			}
		}
		return PageResult.success(result);
	}

	@Operation(summary = "作业清单列表")
	@GetMapping("/workPage")
	@Log(value = "作业清单", module = LogModuleEnum.CASEPOOL)
	public PageResult<CcUserVo> getPoolWorkPage(CcUserQuery query) {
		List<String> followStatusList = new ArrayList<>();
		followStatusList.add(CaseEnum.TUI_HUI.getValue().toString());
		IPage<CcUserVo> result = casePoolService.getWorkPage(query);
		return PageResult.success(result);
	}

	@Operation(summary = "换单记录列表")
	@GetMapping("/hdjlPage")
	public PageResult<TransferRecordsVO> huandanPage(CcUserQuery query) {
		IPage<TransferRecordsVO> result = casePoolService.getHuanPage(query);
		return PageResult.success(result);
	}

	@Operation(summary = "分配记录")
	@GetMapping("/allocatePage")
	@Log(value = "分配记录", module = LogModuleEnum.CASEPOOL)
	public Result<List<TransferRecords>> allocatePage(CcUserQuery query) {
		List<TransferRecords> result = casePoolService.getAallocatPage(query);
		return Result.success(result);
	}

	/**
	 * 锁定 : 取回到自己名下的意思
	 * 锁单列表. 可以进行锁定的案件
	 * @param query
	 * @return
	 */
	@Operation(summary = "锁定列表")
	@GetMapping("/lockCaseList")
	public Result<List<CcUser>> lockCaseList(CcUserQuery query) {
		LambdaQueryWrapper<CcUser> q = new LambdaQueryWrapper<>();
		q.ne(CcUser::getFollowUpStatus, CaseEnum.TUI_AN.getValue().toString());
		q.eq(StrUtils.isNotNull(query.getCustomerIndexNumber()), CcUser::getCustomerIndexNumber, query.getCustomerIndexNumber());
		q.eq(StrUtils.isNotNull(query.getCardholderCode()),CcUser::getCardholderCode, query.getCardholderCode());
		List<CcUser> lockList = casePoolService.getBaseMapper().selectList(q);
		for (CcUser c : lockList) {
			if(c.getUserOrDept()==null||c.getUserOrDept().equals("")) {
				c.setUserOrDept("案池");
			}else {
				UserProfileVO u = userService.getUserProfile(Long.valueOf(c.getUserOrDept()));
				c.setUserOrDept(u.getNickname());
			}
			c.setFollowUpStatus(CaseEnum.fromValue(Integer.valueOf(c.getFollowUpStatus())));
		}
		return Result.success(lockList);
	}

	/*
	 * 锁定 : 取回的意思        客户定的名字
	 * 只能锁定继续跟进状态的案件, 锁定成功后,案件回到操作人作业清单中
	 * 判断案件是否参与轮转(isLock is null). 是否轮转在案池配置中操作.
	 * @param u
	 * @return
	 */
	@Operation(summary = "锁定")
	@PutMapping(value = "/caseLock")
	@Log(value = "锁单", module = LogModuleEnum.CASEPOOL)
	@Transactional
	public Result<Void> caseLock(@RequestBody CcUserQuery u) {
		if(u.getId() == null)
			return Result.failed("案件数据异常(ERROR:ID)");
		int result = 0;
		Long userId = SecurityUtils.getUserId();
		String u_newUser_deptId = SecurityUtils.getDeptId();
		String scope_level = TransferRecordEnum.SUO_DIN.getValue().toString();
		CcUser c = casePoolService.getBaseMapper().selectById(u.getId());
		if(getCaseisLock(c))
			return Result.failed("锁定失败:该案件不参与轮转");
		if(!getCaseLock(c))
			return Result.failed("锁定失败:案件状态不可锁定");
		LambdaQueryWrapper<CcUserWork> kq = new LambdaQueryWrapper<>();
		kq.eq(CcUserWork::getCcId, u.getId());
		CcUserWork ck = worksService.getBaseMapper().selectOne(kq);
		Long cId = u.getId();//案件id
		String old_user_id = "案池";//案件上一个负责人
		String old_user_dept_id = "案池";
		if(ck!=null) {
			old_user_id = ck.getUserId().toString();//案件上一个负责人
			User ud = userService.getBaseMapper().selectById(old_user_id);
			if(ud!=null) {
				old_user_dept_id = ud.getDeptId();
			}
			LambdaUpdateWrapper<CcUserWork> cuw = new LambdaUpdateWrapper<>();
			cuw.set(CcUserWork::getWorkStatus, null);
			cuw.set(CcUserWork::getUserId, userId);
			cuw.eq(CcUserWork::getCcId, u.getId());
			result = worksService.getBaseMapper().update(cuw);
		}else {
			CcUserWork cksave = new CcUserWork();
			cksave.setUserId(userId);
			cksave.setCcId(u.getId());
			result = worksService.getBaseMapper().insert(cksave);
		}
		/*保存分配记录*/
		TransferRecords transferRecords = transferRecordsService.saveTransFerRecord(cId,old_user_id,old_user_dept_id,userId.toString(),u_newUser_deptId,userId.toString(),scope_level);
		transferRecordsService.getBaseMapper().insert(transferRecords);
		c.setUserOrDept(userId.toString());
		c.setFollowUpStatus(CaseEnum.NEW_CASE.getValue().toString());
		casePoolService.updateUserOrDept(c, cId);
		return Result.judge(result>0?true:false);
	}


	@Operation(summary = "配置锁定")
	@PostMapping("/caseConfig")
	public Result<Void> caseConfig(@RequestBody CcUserQuery c) {
		if(c.getIds()==null || c.getIds().size()<0) {
			return Result.failed("请勾选案件");
		}
		LambdaQueryWrapper<CcUser> query = new LambdaQueryWrapper<>();
		query.in(CcUser::getId, c.getIds());
		List<CcUser> lockList = casePoolService.getBaseMapper().selectList(query);
		for (CcUser cu : lockList) {
			cu.setIsLock("1");
		}
		boolean res = casePoolService.updateBatchById(lockList);
		return Result.judge(res);
	}

	@Operation(summary = "配置解锁")
	@PostMapping("/unCaseConfig")
	public Result<Void> unCaseConfig(@RequestBody CcUserQuery c) {
		if(c.getIds()==null || c.getIds().size()<0) {
			return Result.failed("请勾选案件");
		}
		LambdaQueryWrapper<CcUser> query = new LambdaQueryWrapper<>();
		query.in(CcUser::getId, c.getIds());
		List<CcUser> lockList = casePoolService.getBaseMapper().selectList(query);
		for (CcUser cu : lockList) {
			cu.setIsLock("0");
		}
		boolean res = casePoolService.updateBatchById(lockList);
		return Result.judge(res);
	}


	/**
	 * 判断案件是否能锁单
	 * 只有 案池 , 换回的案件 , 继续跟进 的案件可以锁定
	 * @param c
	 * @return true:成功     false:锁单失败
	 */
	public boolean getCaseLock(CcUser c) {
		if(c.getFollowUpStatus().equals(CaseEnum.FOLLOW_UP.getValue().toString()) ||
				c.getFollowUpStatus().equals(CaseEnum.CASE_POOL.getValue().toString()) ||
					c.getFollowUpStatus().equals(CaseEnum.TUI_HUI.getValue().toString()) ||
						(c.getUserOrDept().equals("")&&c.getFollowUpStatus().equals(CaseEnum.NEW_CASE.getValue().toString()))) {
			return true;
		}
			return false;
	}

	/**
	 * 判断案件是否被案池配置限制
	 * @param c
	 * @return true:已经被限制  false:正常通过.
	 */
	public boolean getCaseisLock(CcUser c) {
		if(c.getIsLock().equals("1")) {
			return true;
		}
		return false;
	}

	@Operation(summary = "状态记录表")
	@PostMapping("/keyValueList")
	public Result<List<KeyValueStore>> keyValueList(@RequestBody KeyValueStore query) {
		LambdaQueryWrapper<KeyValueStore> q = new LambdaQueryWrapper<>();
		q.eq(query.getCaseGroup()!=null, KeyValueStore::getCaseGroup,query.getCaseGroup());
		List<KeyValueStore> result = keyValueStoreService.getBaseMapper().selectList(q);
		return Result.success(result);
	}

	@Operation(summary = "下拉选项")
	@GetMapping("/getChangeList")
	public Result<List<CcUser>> changeList(CcUserQuery query) {
		LambdaQueryWrapper<CcUser> q = new LambdaQueryWrapper<>();
		if(query.getBatchNumber()!=null)
			q.select(CcUser::getBatchNumber).groupBy(CcUser::getBatchNumber);
		if(query.getEntrustAgeBracket()!=null)
			q.select(CcUser::getEntrustAgeBracket).groupBy(CcUser::getEntrustAgeBracket);
		if(query.getEntrustCommissionBracket()!=null)
			q.select(CcUser::getEntrustCommissionBracket).groupBy(CcUser::getEntrustCommissionBracket);
		if(query.getCaseType()!=null)
			q.select(CcUser::getCaseType).groupBy(CcUser::getCaseType);
		if(query.getCaseAgency()!=null)
			q.select(CcUser::getCaseAgency).groupBy(CcUser::getCaseAgency);
		q.ne(CcUser::getFollowUpStatus, CaseEnum.TUI_AN.getValue());
		List<CcUser> strList = casePoolService.getBaseMapper().selectList(q);
		if(strList==null) {
			strList = new ArrayList<>();
		}
		return Result.success(strList);
	}

	@Operation(summary = "还款记录列表")
	@GetMapping("/repaymentPage")
	@Log(value = "还款记录列表", module = LogModuleEnum.CASEPOOL)
	public PageResult<RepaymentRecords> getRepaymentPage(RepaymentQuery query) {
		IPage<RepaymentRecords> result = casePoolService.getRepaymentPage(query);
		List<RepaymentRecords> recordsList = result.getRecords();
		BigDecimal sum = repaymentRecordService.repaySumAmount(query);
		if(!recordsList.isEmpty()) {
			recordsList.get(0).setSumRepaymentAmount(sum);
			List<String> userList = recordsList.stream().map(RepaymentRecords::getOperatorId).collect(Collectors.toList());
			List<User> ulist = userService.getBaseMapper().selectBatchIds(userList);
			Map<Long, String> userMap = ulist.stream().collect(Collectors.toMap(User::getId, User::getNickname));
			for (RepaymentRecords r : recordsList) {
				String nickName = userMap.get(Long.valueOf(r.getOperatorId()));
				if(nickName!=null)
					r.setOperatorId(nickName);
				else
					r.setOperatorId("未知账户");
			}
		}
		return PageResult.success(result);
	}
	@Operation(summary = "案件详情,还款记录目录列表")
	@GetMapping("/repaymentMenuPage")
	public PageResult<RepaymentRecords> getRepaymentMenuPage(RepaymentQuery query) {
		IPage<RepaymentRecords> result = casePoolService.getRepaymentPage(query);
		List<RepaymentRecords> recordsList = result.getRecords();
		BigDecimal sum = repaymentRecordService.repaySumAmount(query);
		if(!recordsList.isEmpty()) {
			recordsList.get(0).setSumRepaymentAmount(sum);
			List<String> userList = recordsList.stream().map(RepaymentRecords::getOperatorId).collect(Collectors.toList());
			List<User> ulist = userService.getBaseMapper().selectBatchIds(userList);
			Map<Long, String> userMap = ulist.stream().collect(Collectors.toMap(User::getId, User::getNickname));
			RepaymentRecords v = null;
			for (RepaymentRecords r : recordsList) {
				String nickName = userMap.get(Long.valueOf(r.getOperatorId()));
				if(nickName!=null) {
					r.setOperatorId(nickName);
				}else {
					r.setOperatorId("未知账户");
				}
				if(r.getId().equals(query.getId())) {
					v = r;
				}
			}
			if(v!=null) {
//				recordsList.remove(v);
//				recordsList.add(0,v);
//				result.setRecords(recordsList);
			}
		}
		return PageResult.success(result);
	}



	@Operation(summary = "还款记录删除")
	@PutMapping(value = "/deleteRepayment")
	public Result<Void> deleteRepayment(@RequestBody RepaymentQuery r) {
		if(r.getIds() == null ||  r.getIds().isEmpty())
			return Result.failed("还款数据异常(ERROR:ID)");
//		RepaymentRecords recrord = new RepaymentRecords();
//		recrord.setId(r.getId());
//		recrord.setIsDelete("1");
		/* 改为物理删除remove */
		boolean result = repaymentRecordService.removeBatchByIds(r.getIds());
//		boolean result = repaymentRecordService.updateById(recrord);
		return Result.judge(result);
	}

	@Operation(summary = "获取案件详情")
	@GetMapping("/getCaseDetail")
	public Result<CcUserVo> getCaseDetail(@RequestParam Long id) {
		if(id == null)
			return Result.failed("案件数据异常(ERROR:ID)");
		CcUserVo vo = casePoolService.getPoolDetail(id);
		if(vo.getFollowUpStatus()!=null && vo.getFollowUpStatus().equals(CaseEnum.TUI_AN.getLabel()))
			return Result.failed("已退案");
		return Result.success(vo);
	}

	@Operation(summary = "修改案件")
	@PutMapping(value = "/updateCase")
	public Result<Void> updateUser(@RequestBody CcUser u) {
		if(u.getId() == null)
			return Result.failed("案件数据异常(ERROR:ID)");
		boolean result = casePoolService.updateUserOrDept(u, u.getId());
		return Result.judge(result);
	}



	@Operation(summary = "导入案池")
	@PostMapping("/import")
	public Result<String> CaseImport(@RequestParam String caseAgency,MultipartFile file) throws IOException {
		long size =  file.getSize();
		if(size > MAX_FILE_SIZE)
			return Result.failed("文件大小超过10M");
		if(caseAgency==null || caseAgency.equals(""))
			return Result.failed("请输入委案机构");
		CcUserImportListener listener = new CcUserImportListener();
		listener.setCaseAgency(caseAgency);
		String msg = ExcelUtils.importExcel(file.getInputStream(), CcUserDTO.class, listener);
		return Result.success(msg);
	}

	@Operation(summary = "导出案池")
	@GetMapping("/export")
	public void export(CcUserExcQuery query, HttpServletResponse response) throws IOException {
		/**
		 * 待优化, 多sheet页
		 * 导出后数据进行逻辑删除   未做
		 */
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
		LocalDateTime localDateTime = LocalDateTime.now();
		String time = formatter.format(localDateTime);
		String fileName = "案件"+time+".xlsx";
		response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
		response.setHeader("Content-Disposition",
				"attachment; filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8));
		List<CcUser> exportList = casePoolService.getPoolExt(query);
		EasyExcel.write(response.getOutputStream(), CcUserExtDTO.class).sheet("pool").doWrite(exportList);
	}

	@Operation(summary = "导出案池-前端版")
	@PostMapping("/CaseExport")
	public Result<CcUserExport> CaseExport(@RequestBody CcUserExcQuery query) throws IOException {
		String isTuiAn = query.getIsTuiAn(); // 是否为退案导出 , 退案导出, 需要进行逻辑删除  跟进状态=99
		String followUpStatus = query.getFollowUpStatus();
		List<String> followStatusList = new ArrayList<>();
		followStatusList.add(CaseEnum.CASE_POOL.getValue().toString());
		followStatusList.add(CaseEnum.NEW_CASE.getValue().toString());
		followStatusList.add(CaseEnum.TUI_HUI.getValue().toString());
		if(followUpStatus==null) {
			query.setFollowUpStatusList(followStatusList);
		}
		List<ExcelTitleVo> title = new ArrayList<>();
		ExcelTitleVo v1 = new ExcelTitleVo("原催收人","oldUserOrDept");
		ExcelTitleVo v2 = new ExcelTitleVo("现催收人","userOrDept");
		ExcelTitleVo v3 = new ExcelTitleVo("客户索引号","customerIndexNumber");

		List<CcUser> ccUserList = new ArrayList<>();
		CcUserExport ex = new CcUserExport();
		String fileName = query.getFileName()==null?"未命名":query.getFileName();
		ex.setFileName(fileName);
		if(query.getIds()!=null && query.getIds().size() > 0) {
			ccUserList =  casePoolService.getBaseMapper().selectBatchIds(query.getIds());
			CcUser linshi  = ccUserList.get(0);
			/*表头*/
			title = ReflectiveCopierUtils.getNonEmptyFieldNames(linshi);
		}else{
			/*表头及其属性*/
			title = ReflectiveCopierUtils.getNonEmptyFieldNames(query);
			ccUserList = casePoolService.getPoolExt(query);
		}

		/*查询原催收人 现催收人*/
		List<String> ids = ccUserList.stream().map(CcUser::getId).map(Object::toString).collect(Collectors.toList());
		List<TransferRecords> reList = transferRecordsService.selectUserOrDept(ids);
		List<User> userList = userService.getBaseMapper().selectList(null);
		Map<Long,String> mapUserName = userList.stream().collect(Collectors.toMap(User::getId, User::getNickname));
		Map<Long, TransferRecords> mapById = null;
		if (reList != null) {
		    mapById = reList.stream().collect(Collectors.toMap(TransferRecords::getId, record -> record, (existing, replacement) -> existing));
		}
		for (CcUser cu : ccUserList) {
		    if (mapById != null && mapById.containsKey(cu.getId())) {
		        TransferRecords r = mapById.get(cu.getId());
		        String soureName = r.getSourceUser(); //源催收人
		        String tarName = r.getTargetUser(); // 现催收人
		        String operator = r.getOperator();

		        if (!soureName.equals("案池") && mapUserName.containsKey(Long.valueOf(soureName))) {
		            soureName = mapUserName.get(Long.valueOf(soureName));
		        }
		        if (!tarName.equals("案池") && mapUserName.containsKey(Long.valueOf(tarName))) {
		            tarName = mapUserName.get(Long.valueOf(tarName));
		        }
		        if (mapUserName.containsKey(Long.valueOf(operator))) {
		            operator = mapUserName.get(Long.valueOf(operator));
		        }

		        cu.setOldUserOrDept(soureName != null && soureName.equals("案池") ? operator : soureName);
		        cu.setUserOrDept("案池"); //2025.01.11 根据客户要求修改. 案池导出现催收人一律写案池
		    } else {
		        cu.setOldUserOrDept("案池");
		        cu.setUserOrDept("案池");
		    }
		}


		title.add(v1);title.add(v2);title.add(v3);
		ex.setTitleList(title);
		ex.setList(ccUserList);
		if(isTuiAn!=null && isTuiAn.equals("1")) {
			List<CcUser> tuiAnList = new ArrayList<>();
			LambdaQueryWrapper<CcUserWork> workQuery = new LambdaQueryWrapper<>();
			workQuery.in(CcUserWork::getCcId, ids);
			worksService.getBaseMapper().delete(workQuery);
			for (CcUser ccUser : ccUserList) {
				CcUser tuiAnUser = new CcUser();
				tuiAnUser.setId(ccUser.getId());
//				tuiAnUser.setUserOrDept("");
				tuiAnUser.setIsLock("0");
				tuiAnUser.setFollowUpStatus(CaseEnum.TUI_AN.getValue().toString());
				tuiAnList.add(tuiAnUser);
			}
			if(!tuiAnList.isEmpty()) {
				casePoolService.updateBatchById(tuiAnList);
			}
			/*跟进记录转为历史跟进记录*/
			LambdaQueryWrapper<FollowUpRecords> followQuery = new LambdaQueryWrapper<>();
			followQuery.in(FollowUpRecords::getUserId, ids);
			List<FollowUpRecords> recordsList = followUpRecordsService.getBaseMapper().selectList(followQuery);
			if(recordsList.size()>0) {
				List<FollowUpRecordsHistory> historyRecords = new ArrayList<>();
				try {
					for (FollowUpRecords r : recordsList) {
						FollowUpRecordsHistory h = new FollowUpRecordsHistory();
						ReflectiveCopierUtils.copy(r, h);
						historyRecords.add(h);
					}
				} catch (Exception e) {
					e.printStackTrace();
				}
				historyService.saveBatchHistory(historyRecords);
				followUpRecordsService.getBaseMapper().deleteBatchIds(recordsList);
			}
		}
		return Result.success(ex);
	}
	@Operation(summary = "批量退案")
	@PostMapping("/caseTuiAn")
	@Log(value = "批量退案", module = LogModuleEnum.CASEPOOL)
	@Transactional
	public Result<String> caseTuiAn(@RequestBody CcUserExcQuery query) throws IOException {
		// 参数校验
		if(query.getIds() == null || query.getIds().isEmpty()) {
			return Result.failed("请选择要退案的案件");
		}

		// 查询要退案的案件
		List<CcUser> ccUserList = casePoolService.getBaseMapper().selectBatchIds(query.getIds());
		if(ccUserList.isEmpty()) {
			return Result.failed("未找到要退案的案件");
		}

		// 检查案件状态，已退案的案件不能再次退案
		List<CcUser> alreadyWithdrawn = ccUserList.stream()
			.filter(u -> CaseEnum.TUI_AN.getValue().toString().equals(u.getFollowUpStatus()))
			.collect(Collectors.toList());

		if(!alreadyWithdrawn.isEmpty()) {
			String withdrawnCases = alreadyWithdrawn.stream()
				.map(CcUser::getCustomerIndexNumber)
				.collect(Collectors.joining(", "));
			return Result.failed("以下案件已经退案，无法重复操作：" + withdrawnCases);
		}

		// 构建要更新的案件列表
		List<CcUser> updateList = ccUserList.stream().map(u -> {
			CcUser ccUser = new CcUser();
			ccUser.setId(u.getId());
			ccUser.setFollowUpStatus(CaseEnum.TUI_AN.getValue().toString());
			ccUser.setIsLock("0"); // 退案时解除锁定
			return ccUser;
		}).collect(Collectors.toList());

		// 删除相关的作业清单记录
		List<String> caseIds = query.getIds().stream().map(Object::toString).collect(Collectors.toList());
		LambdaQueryWrapper<CcUserWork> workQuery = new LambdaQueryWrapper<>();
		workQuery.in(CcUserWork::getCcId, caseIds);
		worksService.getBaseMapper().delete(workQuery);

		// 将跟进记录转为历史跟进记录
		LambdaQueryWrapper<FollowUpRecords> followQuery = new LambdaQueryWrapper<>();
		followQuery.in(FollowUpRecords::getUserId, caseIds);
		List<FollowUpRecords> recordsList = followUpRecordsService.getBaseMapper().selectList(followQuery);
		if(!recordsList.isEmpty()) {
			List<FollowUpRecordsHistory> historyRecords = new ArrayList<>();
			try {
				for (FollowUpRecords r : recordsList) {
					FollowUpRecordsHistory h = new FollowUpRecordsHistory();
					ReflectiveCopierUtils.copy(r, h);
					historyRecords.add(h);
				}
				historyService.saveBatchHistory(historyRecords);
				followUpRecordsService.getBaseMapper().deleteBatchIds(recordsList);
			} catch (Exception e) {
				e.printStackTrace();
				return Result.failed("转移跟进记录失败：" + e.getMessage());
			}
		}

		// 批量更新案件状态
		boolean res = casePoolService.updateBatchById(updateList);

		if(res) {
			return Result.success("成功退案 " + updateList.size() + " 个案件");
		} else {
			return Result.failed("批量退案失败");
		}
	}

	@Operation(summary = "批量标注(批量修改跟进状态)")
	@PostMapping("/batchUpdateStatus")
	@Log(value = "批量标注", module = LogModuleEnum.CASEPOOL)
	@Transactional
	public Result<String> batchUpdateStatus(@RequestBody CcUserExcQuery query) {
		// 参数校验
		if(query.getIds() == null || query.getIds().isEmpty()) {
			return Result.failed("请选择要标注的案件");
		}

		if(query.getFollowUpStatus() == null || query.getFollowUpStatus().trim().isEmpty()) {
			return Result.failed("请选择要修改的跟进状态");
		}

		// 验证跟进状态是否有效
		try {
			Integer statusValue = Integer.valueOf(query.getFollowUpStatus());
			CaseEnum.fromValue(statusValue); // 验证状态值是否存在
		} catch (Exception e) {
			return Result.failed("无效的跟进状态");
		}

		// 查询要标注的案件
		List<CcUser> ccUserList = casePoolService.getBaseMapper().selectBatchIds(query.getIds());
		if(ccUserList.isEmpty()) {
			return Result.failed("未找到要标注的案件");
		}

		// 检查是否有已退案的案件，已退案的案件不能修改状态
		List<CcUser> withdrawnCases = ccUserList.stream()
			.filter(u -> CaseEnum.TUI_AN.getValue().toString().equals(u.getFollowUpStatus()))
			.collect(Collectors.toList());

		if(!withdrawnCases.isEmpty()) {
			String withdrawnCaseNumbers = withdrawnCases.stream()
				.map(CcUser::getCustomerIndexNumber)
				.collect(Collectors.joining(", "));
			return Result.failed("以下案件已退案，无法修改状态：" + withdrawnCaseNumbers);
		}

		// 构建要更新的案件列表
		List<CcUser> updateList = ccUserList.stream()
			.filter(u -> !CaseEnum.TUI_AN.getValue().toString().equals(u.getFollowUpStatus()))
			.map(u -> {
				CcUser ccUser = new CcUser();
				ccUser.setId(u.getId());
				ccUser.setFollowUpStatus(query.getFollowUpStatus());
				ccUser.setLastFollowUpDate(new Date()); // 更新最后跟进时间
				return ccUser;
			}).collect(Collectors.toList());

		if(updateList.isEmpty()) {
			return Result.failed("没有可以更新的案件");
		}

		// 批量更新案件状态
		boolean res = casePoolService.updateBatchById(updateList);

		if(res) {
			String statusLabel = CaseEnum.fromValue(Integer.valueOf(query.getFollowUpStatus()));
			return Result.success("成功将 " + updateList.size() + " 个案件标注为：" + statusLabel);
		} else {
			return Result.failed("批量标注失败");
		}
	}

	/*重点关注性能优化,目前单条更新案件状态*/
	@Operation(summary = "分配案件")
	@PostMapping(value = "/allocate")
	public Result<Void> autoAllocate(@RequestBody(required = false) CcUserQuery query) {
		int idsNumber = query.getIds()!=null ?query.getIds().size():0;
		int caseNumber = query.getCIdNum();
		int userNumber = query.getUIds().size();
		query.setPageNum(1);
		if(caseNumber <= 0 && idsNumber <= 0)
			return Result.failed("案件数量必须大于0");
		if(userNumber <= 0)
			return Result.failed("没有选择员工");
//		if(idsNumber > 0) {
			/*查询锁定*/
//			String st = this.getLockResult(query.getIds());
//			if(st != null)
//				return Result.failed(st+"订单为锁定状态");
//		}
		/*设置需要分配的案件数量*/
		query.setPageSize(query.getCIdNum());
		boolean result = casePoolService.allocate(query);
		return Result.judge(result);
	}

	@Operation(summary = "换单")
	@PostMapping(value = "/change")
	public Result<String> changeCase(@RequestBody ChangeCaseQuery query) {
		if(query.getCcIds() ==null || query.getCcIds().size() == 0)
			return Result.failed("至少更换一单");
		if(query.getUserId()==null)
			return Result.failed("数据异常,用户id为空");
		Long scUserId = SecurityUtils.getUserId(); // 查询当前登录人id
		/* 判断是否为案件负责人本人换单 */
		if(!scUserId.toString().equals(query.getUserId()))
			return Result.failed("非案件负责人操作,拒绝换单");
		String st = this.getMarkChangeResult(query.getCcIds());
		if(st != null)
			return Result.failed(st+"订单没有开启换单");
		// 查询解锁的单 件 TODO
		LambdaQueryWrapper<CcUser> q = new LambdaQueryWrapper<>();
		q.eq(CcUser::getIsLock, "0"); // 未锁定
//		cu.follow_up_status IN ('0', '1', '12') AND (cu.user_Or_Dept IS NULL OR cu.user_or_dept = '')
		q.in(CcUser::getFollowUpStatus, Arrays.asList("0", "1", "12"))
				.and(wrapper -> wrapper.isNull(CcUser::getUserOrDept).or().eq(CcUser::getUserOrDept, ""));
		List<CcUser> cList = casePoolService.getBaseMapper().selectList(q);
//		boolean isLock = cList.stream().anyMatch(ccUser -> ccUser.getIsLock().equals("1"));
		boolean isLock = cList.size() <= 0 || query.getCcIds().size() > cList.size();
		if(isLock) {
			return Result.failed("存在禁止轮转案件");
		}

		Integer res = casePoolService.changeCase(query);
		if(res==CaseEnum.CHANGE_SUCCESS.getValue())
			return Result.success("换单成功");
		else
			return Result.failed("换单失败,案件不足");
	}


	/**
	 * 输入案件id.list 返回null则没有锁定案件. 返回字符串为锁定的案件字符串[xxx,xxx]
	 * @param ids
	 * @return
	 */
	public String getLockResult(List<Long> ids) {
		/*判断是否为锁定案件*/
		String st = null;
		LambdaQueryWrapper<CcUser> q = new LambdaQueryWrapper<>();
		q.eq(CcUser::getIsLock, "1").in(CcUser::getId, ids);
			List<CcUser> locklist = casePoolService.getBaseMapper().selectList(q);
			if(locklist!=null && locklist.size()>0) {
				st = locklist.stream().map(CcUser::getId).map(Object::toString).collect(Collectors.joining(",","[","]"));
			}
		return st;
	}

	/**
	 * 输入案件id.list. 返回字符串为没有换单权限的案件字符串[xxx,xxx]
	 * @param ids
	 * @return
	 */
	public String getMarkChangeResult(List<Long> ids) {
		/*判断是否为锁定案件*/
		String st = null;
		LambdaQueryWrapper<CcUser> q = new LambdaQueryWrapper<>();
		// isMarkChange=-1 没有换单权限
		q.eq(CcUser::getIsMarkChange, "-1").in(CcUser::getId, ids);
			List<CcUser> locklist = casePoolService.getBaseMapper().selectList(q);
			if(locklist!=null && locklist.size()>0) {
				st = locklist.stream().map(CcUser::getCustomerIndexNumber).collect(Collectors.joining(",","[","]"));
			}
		return st;
	}




}
