package com.linhong.boot.management.controller;

import java.util.List;

import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.linhong.boot.common.annotation.Log;
import com.linhong.boot.common.annotation.RepeatSubmit;
import com.linhong.boot.common.enums.LogModuleEnum;
import com.linhong.boot.common.result.Result;
import com.linhong.boot.management.model.entity.CcUser;
import com.linhong.boot.management.model.entity.EmergencyContacts;
import com.linhong.boot.management.service.CasePoolService;
import com.linhong.boot.management.service.EmergencyContactsService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;

@Tag(name = "2.紧急联系人")
@RestController
@RequestMapping("/api/v1/emergency")
@RequiredArgsConstructor
public class EmergencyContactsController {
	
	private final EmergencyContactsService contactsService;
	
	private final CasePoolService casePoolService;
	
	@Operation(summary = "新增紧急联系人")
	@PostMapping("/save")
	@RepeatSubmit
	@Log(value = "新增紧急联系人", module = LogModuleEnum.CASEPOOL)
	public Result<?> saveEmergency(@RequestBody EmergencyContacts contacts) {
		if(contacts.getUserId()==null)
			return Result.failed("数据异常:(莫得案件ID)");
		CcUser casePool = casePoolService.getById(contacts.getUserId());
		if(casePool == null)
			return Result.failed("数据异常:(该案件不存在,id:"+contacts.getUserId()+")");
		boolean result = contactsService.save(contacts);
		return Result.judge(result);
	}

	@Operation(summary = "紧急联系人详情")
	@GetMapping("/detail")
	public Result<EmergencyContacts> emergencyDetail(@RequestParam Long id) {
		EmergencyContacts contacts = contactsService.getById(id);
		return Result.success(contacts);
	}

	@Operation(summary = "修改紧急联系人")
	@PutMapping(value = "/modify")
	public Result<Void> emergencyModify(@RequestBody EmergencyContacts contacts) {
		if(contacts.getId()==null)
			return Result.failed("数据异常,紧急联系人ID为空");
		boolean result = contactsService.saveOrUpdate(contacts);
		return Result.judge(result);
	}

	@Operation(summary = "删除紧急联系人")
	@DeleteMapping("/delete")
	public Result<Void> emergencyDelete(@RequestParam Long id) {
		if(id == null)
			Result.failed("数据异常,紧急联系人ID为空");
		boolean result = contactsService.removeById(id);
		return Result.judge(result);
	}
	
	@Operation(summary = "根据案件ID查询紧急联系人")
	@GetMapping("/findByPoolId")
	public Result<List<EmergencyContacts>> emergencyOfPool(@RequestParam Long id) {
		if(id == null)
			Result.failed("查询数据异常,紧急联系人ID为空");
		QueryWrapper<EmergencyContacts> emQueryWrapper = new QueryWrapper<>();
		emQueryWrapper.eq("user_id", id);
		List<EmergencyContacts> contacts = contactsService.getBaseMapper().selectList(emQueryWrapper);
		return Result.success(contacts);
	}
	
}
