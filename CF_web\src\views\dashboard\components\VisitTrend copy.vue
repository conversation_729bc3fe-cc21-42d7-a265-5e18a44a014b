<!--  线 + 柱混合图 -->
<template>
  <el-card>
    <template #header>
      <div class="flex-x-between">
        <div class="flex-y-center">
          业绩排名
          <!-- <el-tooltip effect="dark" content="点击试试下载" placement="bottom">
            <el-icon
              class="cursor-pointer hover:color-#4080FF ml-1"
              name="el-icon-download"
              @click="handleDownloadChart"
            >
              <Download />
            </el-icon>
          </el-tooltip> -->
        </div>

        <el-radio-group v-model="dataRange" size="small" @change="handleDateRangeChange">
          <el-radio-button label="本月" :value="1" />
          <el-radio-button label="上月" :value="2" />
        </el-radio-group>
      </div>
    </template>

    <div :id="id" :class="className" :style="{ height, width }" />
  </el-card>
</template>

<script setup lang="ts">
import * as echarts from "echarts";

import statementAPI from "@/api/dataSystem/statement";
const dataRange = ref(1);
const chart: Ref<echarts.ECharts | null> = ref(null);

const props = defineProps({
  id: {
    type: String,
    default: "VisitTrend",
  },
  className: {
    type: String,
    default: "",
  },
  width: {
    type: String,
    default: "200px",
    required: true,
  },
  height: {
    type: String,
    default: "200px",
    required: true,
  },
});

/** 设置图表  */
const setChartOptions = (xAxis: any, series: any) => {
  if (!chart.value) {
    return;
  }
  const options = {
    tooltip: {
      trigger: "axis",
      axisPointer: { type: "cross" },
    },
    legend: {},
    xAxis: {
      type: "category",
      data: xAxis,
    },
    yAxis: {
      type: "value",
    },
    series: [
      {
        data: series,
        type: "bar",
      },
    ],
  };

  chart.value.setOption(options);
};

/** 计算起止时间范围 */
const calculateDateRange = () => {
  const now = new Date();

  const endDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());

  const days = dataRange.value === 1 ? 7 : 30;
  const startDate = new Date(endDate);
  startDate.setDate(startDate.getDate() - days);

  // 手动调整日期为当地时间的 23:59:59
  const adjustDateToLocal = (date: Date) => {
    date.setHours(23, 59, 59, 999);
    return date;
  };

  const adjustedEndDate = adjustDateToLocal(new Date(endDate));
  const adjustedStartDate = adjustDateToLocal(new Date(startDate));
  const formattedStartDate = adjustedStartDate.toISOString().split("T")[0];
  const formattedEndDate = adjustedEndDate.toISOString().split("T")[0];

  return { startDate: formattedStartDate, endDate: formattedEndDate };
};

// 获取排行榜信息
const loadData = () => {
  let apiName = dataRange.value === 1 ? "reportNowMonthRank" : "reportLastMonthRank";
  statementAPI[apiName]().then((data: any) => {
    console.log(data);
    let xAxis: any = [];
    let series: any = [];
    data.forEach((item: any) => {
      xAxis.push(item.userName);
      series.push(item.userPoints);
    });
    setChartOptions(xAxis, series);
  });
};

const handleDateRangeChange = () => {
  loadData();
};

/** 下载图表 */
const handleDownloadChart = () => {
  if (!chart.value) {
    return;
  }

  // 获取画布图表地址信息
  const img = new Image();
  img.src = chart.value.getDataURL({
    type: "png",
    pixelRatio: 1,
    backgroundColor: "#fff",
  });
  // 当图片加载完成后，生成 URL 并下载
  img.onload = () => {
    const canvas = document.createElement("canvas");
    canvas.width = img.width;
    canvas.height = img.height;
    const ctx = canvas.getContext("2d");
    if (ctx) {
      ctx.drawImage(img, 0, 0, img.width, img.height);
      const link = document.createElement("a");
      link.download = "访问趋势.png";
      link.href = canvas.toDataURL("image/png", 0.9);
      document.body.appendChild(link);
      link.click();
      link.remove();
    }
  };
};

/** 窗口大小变化时，重置图表大小 */
const handleResize = () => {
  setTimeout(() => {
    if (chart.value) {
      chart.value.resize();
    }
  }, 100);
};

/** 初始化图表  */
onMounted(() => {
  chart.value = markRaw(echarts.init(document.getElementById(props.id) as HTMLDivElement));
  loadData();
  window.addEventListener("resize", handleResize);
});

onBeforeUnmount(() => {
  window.removeEventListener("resize", handleResize);
});

onActivated(() => {
  handleResize();
});
</script>
<style lang="scss" scoped></style>
