<template>
  <div class="ranking-card">
    <div class="card-header">
      <div class="header-left">
        <div class="trophy-icon">🏆</div>
        <span class="header-title">员工排行榜</span>
      </div>
      <div class="header-right">
        <div class="period-buttons">
          <button
            class="period-btn"
            :class="{ active: period === 'personal' }"
            @click="changePeriod('personal')"
          >
            个人
          </button>
          <button
            class="period-btn"
            :class="{ active: period === 'team' }"
            @click="changePeriod('team')"
          >
            团队
          </button>
        </div>
      </div>
    </div>

    <div class="ranking-content">
      <el-skeleton :loading="loading" :rows="5" animated>
        <template #template>
          <div v-for="i in 5" :key="i" class="ranking-item skeleton">
            <el-skeleton-item variant="circle" style="width: 32px; height: 32px;" />
            <div class="item-content">
              <el-skeleton-item variant="text" style="width: 60%;" />
              <el-skeleton-item variant="text" style="width: 40%; margin-top: 4px;" />
            </div>
            <el-skeleton-item variant="text" style="width: 80px;" />
          </div>
        </template>

        <template #default>
          <div v-if="rankingList.length === 0" class="empty-data">
            <div class="empty-icon">📊</div>
            <div class="empty-text">暂无排行榜数据</div>
          </div>
          <template v-else>
            <div v-for="(item, index) in rankingList" :key="index" class="ranking-item">
              <div class="rank-number" :class="getRankClass(item.rank)">
                {{ item.rank }}
              </div>

              <div class="user-avatar">
                <div class="avatar-placeholder">
                  {{ (item.userName || '?').charAt(0) }}
                </div>
              </div>

              <div class="user-info">
                <div class="user-name">{{ item.userName }}</div>
                <div class="user-dept">{{ item.deptName }}</div>
              </div>

              <div class="amount-section">
                <div class="amount">¥{{ formatAmount(item.amount) }}</div>
                <div class="progress-section">
                  <div class="progress-bar">
                    <div
                      class="progress-fill"
                      :style="{
                        width: getProgressWidth(item) + '%',
                        backgroundColor: getProgressColor(item.rank)
                      }"
                    ></div>
                  </div>
                  <div class="progress-text">{{ item.recoveryRate?.toFixed(2) || '0.00' }}%</div>
                </div>
              </div>
            </div>
          </template>
        </template>
      </el-skeleton>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";

interface RankingItem {
  rank: number;
  userName: string;
  deptName: string;
  amount: number;
  avatar?: string;
  recoveryRate?: number;
  entrustTotalAmount?: number;
}

interface Props {
  rankingList: RankingItem[];
  loading?: boolean;
}

const props = defineProps<Props>();
const emit = defineEmits<{
  periodChange: [period: 'personal' | 'team'];
}>();

const period = ref<'personal' | 'team'>('personal');

const changePeriod = (newPeriod: 'personal' | 'team') => {
  period.value = newPeriod;
  emit('periodChange', newPeriod);
};

const getRankClass = (rank: number) => {
  if (rank === 1) return 'rank-first';
  if (rank === 2) return 'rank-second';
  if (rank === 3) return 'rank-third';
  return 'rank-normal';
};

const formatAmount = (amount: number) => {
  if (amount >= 10000) {
    return (amount / 10000).toFixed(1) + 'W';
  }
  return amount.toLocaleString();
};

const getProgressWidth = (item: RankingItem) => {
  if (!props.rankingList.length) return 0;

  // 现在统一按回收率计算进度条宽度
  const maxRate = Math.max(...props.rankingList.map(rankItem => rankItem.recoveryRate || 0));
  if (maxRate === 0) return 0;
  return ((item.recoveryRate || 0) / maxRate) * 100;
};

const getProgressColor = (rank: number) => {
  if (rank === 1) return '#4080ff';
  if (rank === 2) return '#23c343';
  if (rank === 3) return '#ff9a2e';
  return '#9c88ff';
};
</script>

<style lang="scss" scoped>
.ranking-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(15px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  padding: 20px;
  height: 30vh; /* 增加高度以容纳三个人的数据 */
  min-height: 320px; /* 设置最小高度确保能显示三个人 */
  display: flex;
  flex-direction: column;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px; /* 减少底部边距 */
  flex-shrink: 0; /* 防止头部被压缩 */

  .header-left {
    display: flex;
    align-items: center;
    gap: 8px;

    .trophy-icon {
      font-size: 18px;
    }

    .header-title {
      font-size: 16px;
      font-weight: 600;
      color: rgba(255, 255, 255, 0.95);
    }
  }

  .header-right {
    .period-buttons {
      display: flex;
      gap: 0;

      .period-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 8px 16px;
        border: none;
        background: rgba(255, 255, 255, 0.15);
        color: rgba(255, 255, 255, 0.8);
        font-size: 13px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
        border-radius: 6px;
        margin: 0 2px;

        &:hover {
          background: rgba(255, 255, 255, 0.2);
          color: rgba(255, 255, 255, 0.95);
        }

        &.active {
          padding: 10px 20px;
          font-size: 14px;
          font-weight: 600;
          color: white;
          border-radius: 8px;
          margin: 0;

          &:first-child {
            background: #fbbf24;
          }

          &:last-child {
            background: #fbbf24;
          }
        }
      }
    }
  }
}

.ranking-content {
  flex: 1; /* 占用剩余空间 */
  overflow-y: auto;
  min-height: 0; /* 允许flex子项收缩 */

  /* 自定义滚动条样式 */
  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;

    &:hover {
      background: rgba(255, 255, 255, 0.5);
    }
  }
}

.ranking-item {
  display: flex;
  align-items: center;
  padding: 12px 0; /* 减少内边距让内容更紧凑 */
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  gap: 12px; /* 减少间距 */

  &:last-child {
    border-bottom: none;
  }

  &.skeleton {
    .item-content {
      flex: 1;
    }
  }

  .rank-number {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 16px;
    color: white;
    flex-shrink: 0;

    &.rank-first {
      background: linear-gradient(135deg, #ffd700, #ffa500);
    }

    &.rank-second {
      background: linear-gradient(135deg, #c0c0c0, #a9a9a9);
    }

    &.rank-third {
      background: linear-gradient(135deg, #cd7f32, #b8860b);
    }

    &.rank-normal {
      background: rgba(255, 255, 255, 0.3);
    }
  }

  .user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    flex-shrink: 0;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .avatar-placeholder {
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, #4080ff, #6366f1);
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
      font-size: 16px;
    }
  }

  .user-info {
    flex: 1;
    margin-left: 8px;

    .user-name {
      font-size: 15px; /* 稍微减小字体 */
      font-weight: 600;
      color: rgba(255, 255, 255, 0.95);
      margin-bottom: 2px; /* 减少间距 */
    }

    .user-dept {
      font-size: 13px; /* 稍微减小字体 */
      color: rgba(255, 255, 255, 0.7);
    }
  }

  .amount-section {
    text-align: right;
    min-width: 140px;

    .amount {
      font-size: 14px;
      font-weight: 600;
      color: rgba(255, 255, 255, 0.8);
      margin-bottom: 4px; /* 减少间距 */
    }

    .progress-section {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      gap: 12px;

      .progress-bar {
        width: 100px;
        height: 8px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 4px;
        overflow: hidden;

        .progress-fill {
          height: 100%;
          border-radius: 4px;
          transition: width 0.3s ease;
        }
      }

      .progress-text {
        font-size: 14px;
        font-weight: 600;
        color: rgba(255, 255, 255, 0.9);
        min-width: 40px;
      }
    }
  }
}

.empty-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: rgba(255, 255, 255, 0.6);

  .empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
  }

  .empty-text {
    font-size: 16px;
    font-weight: 500;
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .ranking-card {
    height: 40vh;
    min-height: 300px;
  }
}

@media (max-width: 768px) {
  .ranking-card {
    height: auto;
    min-height: 280px;
    padding: 16px;
  }

  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    margin-bottom: 16px;
  }

  .ranking-item {
    padding: 10px 0;
    gap: 10px;

    .user-info {
      .user-name {
        font-size: 14px;
      }

      .user-dept {
        font-size: 12px;
      }
    }

    .amount-section {
      min-width: 120px;

      .amount {
        font-size: 13px;
      }
    }
  }
}
</style>
