package com.linhong.boot.management.model.entity;

public class CcUserWork {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cc_user_work.user_id
     *
     * @mbg.generated Mon Nov 25 22:00:16 HKT 2024
     */
    private Long userId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cc_user_work.cc_id
     *
     * @mbg.generated Mon Nov 25 22:00:16 HKT 2024
     */
    private Long ccId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cc_user_work.work_status
     *
     * @mbg.generated Mon Nov 25 22:00:16 HKT 2024
     */
    private String workStatus;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cc_user_work.user_id
     *
     * @return the value of cc_user_work.user_id
     *
     * @mbg.generated Mon Nov 25 22:00:16 HKT 2024
     */
    public Long getUserId() {
        return userId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cc_user_work.user_id
     *
     * @param userId the value for cc_user_work.user_id
     *
     * @mbg.generated Mon Nov 25 22:00:16 HKT 2024
     */
    public void setUserId(Long userId) {
        this.userId = userId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cc_user_work.cc_id
     *
     * @return the value of cc_user_work.cc_id
     *
     * @mbg.generated Mon Nov 25 22:00:16 HKT 2024
     */
    public Long getCcId() {
        return ccId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cc_user_work.cc_id
     *
     * @param ccId the value for cc_user_work.cc_id
     *
     * @mbg.generated Mon Nov 25 22:00:16 HKT 2024
     */
    public void setCcId(Long ccId) {
        this.ccId = ccId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cc_user_work.work_status
     *
     * @return the value of cc_user_work.work_status
     *
     * @mbg.generated Mon Nov 25 22:00:16 HKT 2024
     */
    public String getWorkStatus() {
        return workStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cc_user_work.work_status
     *
     * @param workStatus the value for cc_user_work.work_status
     *
     * @mbg.generated Mon Nov 25 22:00:16 HKT 2024
     */
    public void setWorkStatus(String workStatus) {
        this.workStatus = workStatus == null ? null : workStatus.trim();
    }
}