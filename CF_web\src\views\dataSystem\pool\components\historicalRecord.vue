<template>
  <!-- 历史记录 -->

  <el-dialog
    v-model="props.show"
    width="1200"
    align-center
    class="historical-record-dialog"
    @close="handleCloseDialog"
  >
    <!-- 自定义绿色标题 -->
    <template #header>
      <div class="dialog-header">
        <el-icon class="header-icon"><Notebook /></el-icon>
        <span class="header-title">历史记录</span>
      </div>
    </template>
    <el-row :gutter="24">
      <el-col :span="24">
        <el-table
          ref="dataTableRef"
          v-loading="loading"
          :data="list"
          highlight-current-row
          style="width: 100%"
          :header-cell-style="headerCellStyle"
        >
          <el-table-column
            v-for="item in reminderMune"
            :key="item.prop"
            :label="item.label"
            :prop="item.prop"
            :width="item.width || ''"
            min-width="100"
            show-overflow-tooltip
          />
          <!-- 隐藏操作列 -->
          <!-- <el-table-column
            v-hasPerm="['sys:poolDetails:historicalRecordDelete']"
            fixed="right"
            label="操作"
            width="100"
          >
            <template #default="scope">
              <el-button
                type="primary"
                size="small"
                link
                icon="delete"
                @click="historicalRecordDelete(scope.row.id)"
              >
                删除
              </el-button>
            </template>
          </el-table-column> -->
        </el-table>

        <!-- 分页组件 -->
        <div class="pagination-container">
          <pagination
            v-if="total > 0"
            v-model:total="total"
            v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            @pagination="handleQuery"
          />
        </div>
      </el-col>
    </el-row>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCloseDialog">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ElMessage, ElMessageBox, ElButton, ElLoading } from "element-plus";
import { Notebook } from "@element-plus/icons-vue";
import followHistoryApi from "@/api/dataSystem/followHistory";
import pagination from "@/components/Pagination/index.vue";

import { reminderMune } from "../fieldsSys";

const props = defineProps({
  show: {
    type: Boolean,
    required: true,
  },
  // ids
  ids: {
    type: Number,
    required: 0,
  },
});
watch(
  () => props.show,
  (val) => {
    if (val) handleQuery();
  }
);
const emit = defineEmits(["update:show", "refresh"]);

const loading = ref(false);
const list = ref([]);
const total = ref(0);

// 分页参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
});

// 表头样式 - 强制设置浅绿色
const headerCellStyle = () => {
  return {
    backgroundColor: "#d9f7be !important",
    background: "#d9f7be !important",
    color: "#262626 !important",
    fontWeight: "600 !important",
    textAlign: "center !important",
    borderRight: "1px solid #b7eb8f !important",
    borderBottom: "1px solid #b7eb8f !important",
    padding: "12px 8px !important",
    fontSize: "14px !important",
  };
};

/**
 * 获取历史记录
 *
 */
const handleQuery = () => {
  loading.value = true;
  followHistoryApi
    .followHistoryPage({
      id: props.ids,
      pageNum: queryParams.pageNum,
      pageSize: queryParams.pageSize,
    })
    .then((data: any) => {
      list.value = data.list;
      total.value = data.total;
      loading.value = false;
    })
    .catch(() => {
      loading.value = false;
    });
};
// 删除
const historicalRecordDelete = (id: number) => {
  ElMessageBox.confirm("确认删除该记录?", "提示", {
    confirmButtonText: "确认",
    cancelButtonText: "取消",
    type: "warning",
    draggable: true,
  }).then(() => {
    loading.value = true;
    followHistoryApi
      .followHistoryDelete({
        id: id,
      })
      .then((e) => {
        ElMessage({
          message: "删除成功",
          type: "success",
        });
        loading.value = false;
        handleQuery();
      });
  });
};
//关闭弹窗
const handleCloseDialog = () => {
  console.log("关闭");
  emit("update:show", false);
};
</script>

<style lang="scss" scoped>
/* 历史记录弹框样式 - 绿色主题 */
.historical-record-dialog {
  :deep(.el-dialog) {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(82, 196, 26, 0.15);
  }

  :deep(.el-dialog__header) {
    padding: 0;
    margin: 0;
    background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
  }

  :deep(.el-dialog__body) {
    padding: 24px;
    background-color: white;
  }

  :deep(.el-dialog__footer) {
    padding: 16px 24px;
    background-color: white;
    border-top: 1px solid #f0f0f0;
  }
}

/* 对话框标题 - 整行绿色背景，图标+文字靠左显示 */
.dialog-header {
  display: flex;
  align-items: center;
  justify-content: flex-start; /* 靠左显示 */
  padding: 16px 24px;
  background: #52c41a; /* 纯绿色背景，整行都是绿色 */
  color: white;
  width: 100%;
  box-sizing: border-box;
}

.header-icon {
  font-size: 18px;
  margin-right: 8px;
  color: white;
}

.header-title {
  font-size: 16px;
  font-weight: 600;
  color: white;
  text-align: left;
}

.close-btn {
  color: white !important;
  font-size: 18px;
  padding: 4px;

  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
  }
}

/* 表格样式 - 按照图片样式 */
:deep(.el-table) {
  border: none; /* 取消所有边框 */
  border-radius: 6px;
  overflow: hidden;
}

/* 表头浅绿色样式 - 使用更强的选择器 */
.historical-record-dialog :deep(.el-table__header) {
  background-color: #d9f7be !important;
}

.historical-record-dialog :deep(.el-table__header th) {
  background-color: #d9f7be !important;
  background: #d9f7be !important;
  color: #262626 !important;
  font-weight: 600 !important;
  border: none !important; /* 取消表头边框 */
  padding: 12px 8px !important;
  font-size: 14px !important;
  text-align: center !important;
}

/* 额外的表头样式覆盖 */
.historical-record-dialog :deep(.el-table thead th) {
  background-color: #d9f7be !important;
  background: #d9f7be !important;
}

.historical-record-dialog :deep(.el-table th.el-table__cell) {
  background-color: #d9f7be !important;
  background: #d9f7be !important;
}

/* 更强的表头样式覆盖 */
.historical-record-dialog :deep(.el-table .el-table__header-wrapper .el-table__header thead th) {
  background-color: #d9f7be !important;
  background: #d9f7be !important;
}

.historical-record-dialog :deep(.el-table .el-table__header-wrapper thead th.el-table__cell) {
  background-color: #d9f7be !important;
  background: #d9f7be !important;
}

/* 全局表头样式强制覆盖 */
.historical-record-dialog :deep(th) {
  background-color: #d9f7be !important;
  background: #d9f7be !important;
}

/* 取消表格所有边框 */
.historical-record-dialog :deep(.el-table) {
  &::before {
    display: none !important; /* 移除表格底部边框 */
  }

  &::after {
    display: none !important; /* 移除表格右侧边框 */
  }

  .el-table__inner-wrapper::before {
    display: none !important; /* 移除表格顶部边框 */
  }

  .el-table__border-left-patch {
    display: none !important; /* 移除左侧边框补丁 */
  }

  .el-table__border-right-patch {
    display: none !important; /* 移除右侧边框补丁 */
  }

  .el-table__fixed::before,
  .el-table__fixed-right::before {
    display: none !important; /* 移除固定列边框 */
  }
}

:deep(.el-table__body) {
  tr {
    background-color: white;

    &:nth-child(even) {
      background-color: #f6ffed; /* 浅绿色斑马纹 */
    }

    &:hover {
      background-color: #d9f7be !important;
    }
  }

  td {
    border: none !important; /* 取消所有单元格边框 */
    color: #262626;
    padding: 12px 8px;
    font-size: 13px;
    text-align: center;
  }
}

/* 分页容器 */
.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
  padding: 16px 0;
  background-color: white;
}

/* 分页组件绿色主题 - 按照图片样式 */
:deep(.el-pagination) {
  .el-pagination__total {
    color: #666;
    font-weight: normal;
    font-size: 14px;
  }

  .el-pagination__jump {
    color: #666;
    font-size: 14px;
  }

  .el-pager li {
    background-color: white;
    border: 1px solid #d9d9d9;
    color: #666;
    min-width: 32px;
    height: 32px;
    line-height: 30px;
    margin: 0 2px;
    border-radius: 4px;
    font-size: 14px;

    &:hover {
      background-color: #f6ffed;
      border-color: #52c41a;
      color: #52c41a;
    }

    &.is-active {
      background-color: #52c41a;
      border-color: #52c41a;
      color: white;
    }
  }

  .btn-prev,
  .btn-next {
    background-color: white;
    border: 1px solid #d9d9d9;
    color: #666;
    min-width: 32px;
    height: 32px;
    border-radius: 4px;
    margin: 0 2px;

    &:hover {
      background-color: #f6ffed;
      border-color: #52c41a;
      color: #52c41a;
    }

    &:disabled {
      background-color: #f5f5f5;
      border-color: #d9d9d9;
      color: #bfbfbf;
      cursor: not-allowed;
    }
  }

  .el-select {
    margin: 0 8px;

    .el-input__wrapper {
      border-color: #d9d9d9;
      border-radius: 4px;
      height: 32px;

      &:hover {
        border-color: #52c41a;
      }

      &.is-focus {
        border-color: #52c41a;
        box-shadow: 0 0 0 2px rgba(82, 196, 26, 0.2);
      }
    }
  }

  .el-pagination__sizes {
    margin-right: 16px;
  }

  .el-pagination__classifier {
    margin: 0 8px;
    color: #666;
  }
}

/* 底部按钮 */
.dialog-footer {
  text-align: center;
  background-color: white;
}

:deep(.dialog-footer .el-button) {
  background-color: #52c41a;
  border-color: #52c41a;
  color: white;
  padding: 8px 24px;
  border-radius: 4px;
  font-weight: 500;

  &:hover {
    background-color: #389e0d;
    border-color: #389e0d;
  }
}
</style>
