package com.linhong.boot.management.model.vo;

import java.math.BigDecimal;
import java.util.Date;

import com.linhong.boot.management.model.entity.RepaymentRecords;

import lombok.Data;

@Data
public class RepaymentRecordVo extends RepaymentRecords{

	private BigDecimal SumRepaymentAmount;

	private Integer id;

	private Long ccId;

	private String outsourceSerialNumber;

	private String batchNumber;

	private String cardholderCode;

	private String customerName;

	private String customerIndexNumber;

	private String caseType;

	private BigDecimal repaymentAmount;

	private Date repaymentDate;

	private String deptName;

	private String operatorId;

}