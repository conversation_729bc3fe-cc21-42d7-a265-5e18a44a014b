import { store } from "@/store";
import { getLoginPageConfig, type LoginPageConfig } from "@/api/system/login-config";

export const useLoginConfigStore = defineStore("loginConfig", () => {
  // 使用持久化存储，这样刷新页面后配置不会丢失
  const loginConfig = useStorage<LoginPageConfig>("loginConfig", {
    logo: "",
    systemTitle: "CF-作业系统", // 默认标题
    cultureTitle: "",
    cultureItems: [],
  });

  /**
   * 获取登录页配置
   */
  function fetchLoginConfig() {
    return new Promise<LoginPageConfig>((resolve, reject) => {
      console.log("Fetching login page config...");
      getLoginPageConfig()
        .then((data) => {
          console.log("Login page config received:", data);
          if (data) {
            // 更新存储的配置
            loginConfig.value = data;
            console.log("Login config updated in store:", loginConfig.value);
            resolve(data);
          } else {
            console.error("No data received from getLoginPageConfig");
            reject("Failed to get login page config");
          }
        })
        .catch((error) => {
          console.error("Error fetching login page config:", error);
          reject(error);
        });
    });
  }

  return {
    loginConfig,
    fetchLoginConfig,
  };
});

/**
 * 用于在组件外部使用 LoginConfig Store
 */
export function useLoginConfigStoreHook() {
  return useLoginConfigStore(store);
}
