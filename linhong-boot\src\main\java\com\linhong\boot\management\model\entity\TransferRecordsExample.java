package com.linhong.boot.management.model.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class TransferRecordsExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table transfer_records
     *
     * @mbg.generated Mon Dec 02 17:42:49 CST 2024
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table transfer_records
     *
     * @mbg.generated Mon Dec 02 17:42:49 CST 2024
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table transfer_records
     *
     * @mbg.generated Mon Dec 02 17:42:49 CST 2024
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table transfer_records
     *
     * @mbg.generated Mon Dec 02 17:42:49 CST 2024
     */
    public TransferRecordsExample() {
        oredCriteria = new ArrayList<>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table transfer_records
     *
     * @mbg.generated Mon Dec 02 17:42:49 CST 2024
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table transfer_records
     *
     * @mbg.generated Mon Dec 02 17:42:49 CST 2024
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table transfer_records
     *
     * @mbg.generated Mon Dec 02 17:42:49 CST 2024
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table transfer_records
     *
     * @mbg.generated Mon Dec 02 17:42:49 CST 2024
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table transfer_records
     *
     * @mbg.generated Mon Dec 02 17:42:49 CST 2024
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table transfer_records
     *
     * @mbg.generated Mon Dec 02 17:42:49 CST 2024
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table transfer_records
     *
     * @mbg.generated Mon Dec 02 17:42:49 CST 2024
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table transfer_records
     *
     * @mbg.generated Mon Dec 02 17:42:49 CST 2024
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table transfer_records
     *
     * @mbg.generated Mon Dec 02 17:42:49 CST 2024
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table transfer_records
     *
     * @mbg.generated Mon Dec 02 17:42:49 CST 2024
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table transfer_records
     *
     * @mbg.generated Mon Dec 02 17:42:49 CST 2024
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andSourceOrganizationIsNull() {
            addCriterion("source_organization is null");
            return (Criteria) this;
        }

        public Criteria andSourceOrganizationIsNotNull() {
            addCriterion("source_organization is not null");
            return (Criteria) this;
        }

        public Criteria andSourceOrganizationEqualTo(String value) {
            addCriterion("source_organization =", value, "sourceOrganization");
            return (Criteria) this;
        }

        public Criteria andSourceOrganizationNotEqualTo(String value) {
            addCriterion("source_organization <>", value, "sourceOrganization");
            return (Criteria) this;
        }

        public Criteria andSourceOrganizationGreaterThan(String value) {
            addCriterion("source_organization >", value, "sourceOrganization");
            return (Criteria) this;
        }

        public Criteria andSourceOrganizationGreaterThanOrEqualTo(String value) {
            addCriterion("source_organization >=", value, "sourceOrganization");
            return (Criteria) this;
        }

        public Criteria andSourceOrganizationLessThan(String value) {
            addCriterion("source_organization <", value, "sourceOrganization");
            return (Criteria) this;
        }

        public Criteria andSourceOrganizationLessThanOrEqualTo(String value) {
            addCriterion("source_organization <=", value, "sourceOrganization");
            return (Criteria) this;
        }

        public Criteria andSourceOrganizationLike(String value) {
            addCriterion("source_organization like", value, "sourceOrganization");
            return (Criteria) this;
        }

        public Criteria andSourceOrganizationNotLike(String value) {
            addCriterion("source_organization not like", value, "sourceOrganization");
            return (Criteria) this;
        }

        public Criteria andSourceOrganizationIn(List<String> values) {
            addCriterion("source_organization in", values, "sourceOrganization");
            return (Criteria) this;
        }

        public Criteria andSourceOrganizationNotIn(List<String> values) {
            addCriterion("source_organization not in", values, "sourceOrganization");
            return (Criteria) this;
        }

        public Criteria andSourceOrganizationBetween(String value1, String value2) {
            addCriterion("source_organization between", value1, value2, "sourceOrganization");
            return (Criteria) this;
        }

        public Criteria andSourceOrganizationNotBetween(String value1, String value2) {
            addCriterion("source_organization not between", value1, value2, "sourceOrganization");
            return (Criteria) this;
        }

        public Criteria andTargetOrganizationIsNull() {
            addCriterion("target_organization is null");
            return (Criteria) this;
        }

        public Criteria andTargetOrganizationIsNotNull() {
            addCriterion("target_organization is not null");
            return (Criteria) this;
        }

        public Criteria andTargetOrganizationEqualTo(String value) {
            addCriterion("target_organization =", value, "targetOrganization");
            return (Criteria) this;
        }

        public Criteria andTargetOrganizationNotEqualTo(String value) {
            addCriterion("target_organization <>", value, "targetOrganization");
            return (Criteria) this;
        }

        public Criteria andTargetOrganizationGreaterThan(String value) {
            addCriterion("target_organization >", value, "targetOrganization");
            return (Criteria) this;
        }

        public Criteria andTargetOrganizationGreaterThanOrEqualTo(String value) {
            addCriterion("target_organization >=", value, "targetOrganization");
            return (Criteria) this;
        }

        public Criteria andTargetOrganizationLessThan(String value) {
            addCriterion("target_organization <", value, "targetOrganization");
            return (Criteria) this;
        }

        public Criteria andTargetOrganizationLessThanOrEqualTo(String value) {
            addCriterion("target_organization <=", value, "targetOrganization");
            return (Criteria) this;
        }

        public Criteria andTargetOrganizationLike(String value) {
            addCriterion("target_organization like", value, "targetOrganization");
            return (Criteria) this;
        }

        public Criteria andTargetOrganizationNotLike(String value) {
            addCriterion("target_organization not like", value, "targetOrganization");
            return (Criteria) this;
        }

        public Criteria andTargetOrganizationIn(List<String> values) {
            addCriterion("target_organization in", values, "targetOrganization");
            return (Criteria) this;
        }

        public Criteria andTargetOrganizationNotIn(List<String> values) {
            addCriterion("target_organization not in", values, "targetOrganization");
            return (Criteria) this;
        }

        public Criteria andTargetOrganizationBetween(String value1, String value2) {
            addCriterion("target_organization between", value1, value2, "targetOrganization");
            return (Criteria) this;
        }

        public Criteria andTargetOrganizationNotBetween(String value1, String value2) {
            addCriterion("target_organization not between", value1, value2, "targetOrganization");
            return (Criteria) this;
        }

        public Criteria andSourceUserIsNull() {
            addCriterion("source_user is null");
            return (Criteria) this;
        }

        public Criteria andSourceUserIsNotNull() {
            addCriterion("source_user is not null");
            return (Criteria) this;
        }

        public Criteria andSourceUserEqualTo(String value) {
            addCriterion("source_user =", value, "sourceUser");
            return (Criteria) this;
        }

        public Criteria andSourceUserNotEqualTo(String value) {
            addCriterion("source_user <>", value, "sourceUser");
            return (Criteria) this;
        }

        public Criteria andSourceUserGreaterThan(String value) {
            addCriterion("source_user >", value, "sourceUser");
            return (Criteria) this;
        }

        public Criteria andSourceUserGreaterThanOrEqualTo(String value) {
            addCriterion("source_user >=", value, "sourceUser");
            return (Criteria) this;
        }

        public Criteria andSourceUserLessThan(String value) {
            addCriterion("source_user <", value, "sourceUser");
            return (Criteria) this;
        }

        public Criteria andSourceUserLessThanOrEqualTo(String value) {
            addCriterion("source_user <=", value, "sourceUser");
            return (Criteria) this;
        }

        public Criteria andSourceUserLike(String value) {
            addCriterion("source_user like", value, "sourceUser");
            return (Criteria) this;
        }

        public Criteria andSourceUserNotLike(String value) {
            addCriterion("source_user not like", value, "sourceUser");
            return (Criteria) this;
        }

        public Criteria andSourceUserIn(List<String> values) {
            addCriterion("source_user in", values, "sourceUser");
            return (Criteria) this;
        }

        public Criteria andSourceUserNotIn(List<String> values) {
            addCriterion("source_user not in", values, "sourceUser");
            return (Criteria) this;
        }

        public Criteria andSourceUserBetween(String value1, String value2) {
            addCriterion("source_user between", value1, value2, "sourceUser");
            return (Criteria) this;
        }

        public Criteria andSourceUserNotBetween(String value1, String value2) {
            addCriterion("source_user not between", value1, value2, "sourceUser");
            return (Criteria) this;
        }

        public Criteria andTargetUserIsNull() {
            addCriterion("target_user is null");
            return (Criteria) this;
        }

        public Criteria andTargetUserIsNotNull() {
            addCriterion("target_user is not null");
            return (Criteria) this;
        }

        public Criteria andTargetUserEqualTo(String value) {
            addCriterion("target_user =", value, "targetUser");
            return (Criteria) this;
        }

        public Criteria andTargetUserNotEqualTo(String value) {
            addCriterion("target_user <>", value, "targetUser");
            return (Criteria) this;
        }

        public Criteria andTargetUserGreaterThan(String value) {
            addCriterion("target_user >", value, "targetUser");
            return (Criteria) this;
        }

        public Criteria andTargetUserGreaterThanOrEqualTo(String value) {
            addCriterion("target_user >=", value, "targetUser");
            return (Criteria) this;
        }

        public Criteria andTargetUserLessThan(String value) {
            addCriterion("target_user <", value, "targetUser");
            return (Criteria) this;
        }

        public Criteria andTargetUserLessThanOrEqualTo(String value) {
            addCriterion("target_user <=", value, "targetUser");
            return (Criteria) this;
        }

        public Criteria andTargetUserLike(String value) {
            addCriterion("target_user like", value, "targetUser");
            return (Criteria) this;
        }

        public Criteria andTargetUserNotLike(String value) {
            addCriterion("target_user not like", value, "targetUser");
            return (Criteria) this;
        }

        public Criteria andTargetUserIn(List<String> values) {
            addCriterion("target_user in", values, "targetUser");
            return (Criteria) this;
        }

        public Criteria andTargetUserNotIn(List<String> values) {
            addCriterion("target_user not in", values, "targetUser");
            return (Criteria) this;
        }

        public Criteria andTargetUserBetween(String value1, String value2) {
            addCriterion("target_user between", value1, value2, "targetUser");
            return (Criteria) this;
        }

        public Criteria andTargetUserNotBetween(String value1, String value2) {
            addCriterion("target_user not between", value1, value2, "targetUser");
            return (Criteria) this;
        }

        public Criteria andAssignmentTypeIsNull() {
            addCriterion("assignment_type is null");
            return (Criteria) this;
        }

        public Criteria andAssignmentTypeIsNotNull() {
            addCriterion("assignment_type is not null");
            return (Criteria) this;
        }

        public Criteria andAssignmentTypeEqualTo(String value) {
            addCriterion("assignment_type =", value, "assignmentType");
            return (Criteria) this;
        }

        public Criteria andAssignmentTypeNotEqualTo(String value) {
            addCriterion("assignment_type <>", value, "assignmentType");
            return (Criteria) this;
        }

        public Criteria andAssignmentTypeGreaterThan(String value) {
            addCriterion("assignment_type >", value, "assignmentType");
            return (Criteria) this;
        }

        public Criteria andAssignmentTypeGreaterThanOrEqualTo(String value) {
            addCriterion("assignment_type >=", value, "assignmentType");
            return (Criteria) this;
        }

        public Criteria andAssignmentTypeLessThan(String value) {
            addCriterion("assignment_type <", value, "assignmentType");
            return (Criteria) this;
        }

        public Criteria andAssignmentTypeLessThanOrEqualTo(String value) {
            addCriterion("assignment_type <=", value, "assignmentType");
            return (Criteria) this;
        }

        public Criteria andAssignmentTypeLike(String value) {
            addCriterion("assignment_type like", value, "assignmentType");
            return (Criteria) this;
        }

        public Criteria andAssignmentTypeNotLike(String value) {
            addCriterion("assignment_type not like", value, "assignmentType");
            return (Criteria) this;
        }

        public Criteria andAssignmentTypeIn(List<String> values) {
            addCriterion("assignment_type in", values, "assignmentType");
            return (Criteria) this;
        }

        public Criteria andAssignmentTypeNotIn(List<String> values) {
            addCriterion("assignment_type not in", values, "assignmentType");
            return (Criteria) this;
        }

        public Criteria andAssignmentTypeBetween(String value1, String value2) {
            addCriterion("assignment_type between", value1, value2, "assignmentType");
            return (Criteria) this;
        }

        public Criteria andAssignmentTypeNotBetween(String value1, String value2) {
            addCriterion("assignment_type not between", value1, value2, "assignmentType");
            return (Criteria) this;
        }

        public Criteria andOperatorIsNull() {
            addCriterion("operator is null");
            return (Criteria) this;
        }

        public Criteria andOperatorIsNotNull() {
            addCriterion("operator is not null");
            return (Criteria) this;
        }

        public Criteria andOperatorEqualTo(String value) {
            addCriterion("operator =", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorNotEqualTo(String value) {
            addCriterion("operator <>", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorGreaterThan(String value) {
            addCriterion("operator >", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorGreaterThanOrEqualTo(String value) {
            addCriterion("operator >=", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorLessThan(String value) {
            addCriterion("operator <", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorLessThanOrEqualTo(String value) {
            addCriterion("operator <=", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorLike(String value) {
            addCriterion("operator like", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorNotLike(String value) {
            addCriterion("operator not like", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorIn(List<String> values) {
            addCriterion("operator in", values, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorNotIn(List<String> values) {
            addCriterion("operator not in", values, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorBetween(String value1, String value2) {
            addCriterion("operator between", value1, value2, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorNotBetween(String value1, String value2) {
            addCriterion("operator not between", value1, value2, "operator");
            return (Criteria) this;
        }

        public Criteria andOperationTimeIsNull() {
            addCriterion("operation_time is null");
            return (Criteria) this;
        }

        public Criteria andOperationTimeIsNotNull() {
            addCriterion("operation_time is not null");
            return (Criteria) this;
        }

        public Criteria andOperationTimeEqualTo(Date value) {
            addCriterion("operation_time =", value, "operationTime");
            return (Criteria) this;
        }

        public Criteria andOperationTimeNotEqualTo(Date value) {
            addCriterion("operation_time <>", value, "operationTime");
            return (Criteria) this;
        }

        public Criteria andOperationTimeGreaterThan(Date value) {
            addCriterion("operation_time >", value, "operationTime");
            return (Criteria) this;
        }

        public Criteria andOperationTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("operation_time >=", value, "operationTime");
            return (Criteria) this;
        }

        public Criteria andOperationTimeLessThan(Date value) {
            addCriterion("operation_time <", value, "operationTime");
            return (Criteria) this;
        }

        public Criteria andOperationTimeLessThanOrEqualTo(Date value) {
            addCriterion("operation_time <=", value, "operationTime");
            return (Criteria) this;
        }

        public Criteria andOperationTimeIn(List<Date> values) {
            addCriterion("operation_time in", values, "operationTime");
            return (Criteria) this;
        }

        public Criteria andOperationTimeNotIn(List<Date> values) {
            addCriterion("operation_time not in", values, "operationTime");
            return (Criteria) this;
        }

        public Criteria andOperationTimeBetween(Date value1, Date value2) {
            addCriterion("operation_time between", value1, value2, "operationTime");
            return (Criteria) this;
        }

        public Criteria andOperationTimeNotBetween(Date value1, Date value2) {
            addCriterion("operation_time not between", value1, value2, "operationTime");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table transfer_records
     *
     * @mbg.generated do_not_delete_during_merge Mon Dec 02 17:42:49 CST 2024
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table transfer_records
     *
     * @mbg.generated Mon Dec 02 17:42:49 CST 2024
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}