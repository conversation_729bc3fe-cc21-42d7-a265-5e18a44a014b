package com.linhong.boot.management.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.linhong.boot.management.model.entity.RepaymentRecords;
import com.linhong.boot.management.model.entity.RepaymentRecordsExample;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface RepaymentRecordsMapper extends BaseMapper<RepaymentRecords> {
	
	
	/**
	 * 上个月还款排名
	 * @return
	 */
	List<RepaymentRecords> userRank();
	/**
	 * 这个月还款排名
	 * @return
	 */
	List<RepaymentRecords> userRankNow();

	/**
	 * 上个月团队还款排名
	 * @return
	 */
	List<RepaymentRecords> userRankByTeam();

	/**
	 * 这个月团队还款排名
	 * @return
	 */
	List<RepaymentRecords> userRankNowByTeam();


    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table repayment_records
     *
     * @mbg.generated Wed Dec 04 09:54:44 CST 2024
     */
    long countByExample(RepaymentRecordsExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table repayment_records
     *
     * @mbg.generated Wed Dec 04 09:54:44 CST 2024
     */
    int deleteByExample(RepaymentRecordsExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table repayment_records
     *
     * @mbg.generated Wed Dec 04 09:54:44 CST 2024
     */
    int deleteByPrimaryKey(Integer id);


    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table repayment_records
     *
     * @mbg.generated Wed Dec 04 09:54:44 CST 2024
     */
    int insertSelective(RepaymentRecords row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table repayment_records
     *
     * @mbg.generated Wed Dec 04 09:54:44 CST 2024
     */
    List<RepaymentRecords> selectByExample(RepaymentRecordsExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table repayment_records
     *
     * @mbg.generated Wed Dec 04 09:54:44 CST 2024
     */
    RepaymentRecords selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table repayment_records
     *
     * @mbg.generated Wed Dec 04 09:54:44 CST 2024
     */
    int updateByExampleSelective(@Param("row") RepaymentRecords row, @Param("example") RepaymentRecordsExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table repayment_records
     *
     * @mbg.generated Wed Dec 04 09:54:44 CST 2024
     */
    int updateByExample(@Param("row") RepaymentRecords row, @Param("example") RepaymentRecordsExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table repayment_records
     *
     * @mbg.generated Wed Dec 04 09:54:44 CST 2024
     */
    int updateByPrimaryKeySelective(RepaymentRecords row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table repayment_records
     *
     * @mbg.generated Wed Dec 04 09:54:44 CST 2024
     */
    int updateByPrimaryKey(RepaymentRecords row);
}