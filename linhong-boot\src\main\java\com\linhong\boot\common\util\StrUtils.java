package com.linhong.boot.common.util;

public class StrUtils {
	
	private static final String CHINESE_REGEX = "[\\u4E00-\\u9FFF]";

	/**
	 * 非空返回true
	 * @param o
	 * @return boolean
	 */
	public static boolean isNotNull(String val) {
		if(val!=null && !val.trim().isEmpty())
			return true;
		return false;
	}
	
	/**
	 * 检查是否是中文
	 * @param input
	 * @return 中文返回true
	 */
	public static boolean isChineseAndNotLong(String input) {

        if (input == null || input.isEmpty()) {
            return false;
        }

        boolean containsChinese = input.matches(".*" + CHINESE_REGEX + ".*");
        
        try {
            Long.valueOf(input);
            return false; // 可以被解析为Long，所以返回false
        } catch (NumberFormatException e) {
            e.printStackTrace();
        }

        return containsChinese;
    }
	
}
