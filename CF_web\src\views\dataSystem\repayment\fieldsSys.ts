import { queryMode, followUpStatus } from "@/enums/optionsConfig/pool";
import { userOrDept, caseType, createBatchNumberField } from "@/views/dataSystem/pool/fieldsSys";

// 为还款管理页面创建独立的批次号实例
const repaymentBatchNumberInstance = createBatchNumberField();
const repaymentBatchNumber = repaymentBatchNumberInstance.batchNumber;
// 筛选字段 - 按照还款管理.html的排序
const screenList: any = [
  // 1. 作业员/组织
  JSON.parse(JSON.stringify(userOrDept)),
  // 2. 委托机构
  {
    label: "委托机构",
    type: "input",
    options: [],
    fieldKey: "key5-key",
    field: "outsourceSerialNumber",
    placeholder: "请输入",
    rules: "",
  },
  // 3. 批次号
  repaymentBatchNumber,
  // 4. 客户索引号
  {
    label: "客户索引号",
    type: "input",
    options: queryMode,
    fieldKey: "key13-key",
    field: "customerIndexNumber",
    placeholder: "请输入",
    rules: "",
  },
  // 5. 客户姓名
  {
    label: "客户姓名",
    type: "input",
    options: queryMode,
    fieldKey: "key4-key",
    field: "customerName",
    placeholder: "请输入",
    rules: "",
  },
  // 6. 案件类型
  caseType,
  // 7. 持卡人代码
  {
    label: "持卡人代码",
    type: "input",
    options: queryMode,
    fieldKey: "key6-key",
    field: "cardholderCode",
    placeholder: "请输入",
    rules: "",
  },
  // 8. 还款时间
  {
    label: "还款时间",
    type: "date-picker",
    options: [],
    field: "entrustStartDate",
    placeholder: "请选择",
    rules: "",
    formType: "daterange",
    size: "default",
  },
];

// 列表表头数据 - 按照还款管理.html的排序
const listFields = [
  { label: "客户姓名", prop: "customerName" },
  { label: "委托机构", prop: "outsourceSerialNumber" },
  { label: "批次号", prop: "batchNumber" },
  { label: "客户索引号", prop: "customerIndexNumber", width: "180px" },
  { label: "持卡人代码", prop: "cardholderCode" },
  { label: "案件类型", prop: "caseType" },
  { label: "还款金额", prop: "repaymentAmount" },
  { label: "还款时间", prop: "repaymentDate" },
  { label: "组织", prop: "deptName" },
  { label: "作业员", prop: "operatorId" },
  { label: "工号", prop: "employeeNumber" },
];

export {
  screenList,
  listFields,
  repaymentBatchNumberInstance
};
