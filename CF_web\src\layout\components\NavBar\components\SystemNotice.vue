<template>
  <div class="system-notice-container" v-if="hasNotices">
    <div class="notice-content">
      <div class="scrolling-text">
        <span class="notice-text">警告：今日有{{ pendingTasksCount }}个待处理事项</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import NoticeAPI from "@/api/system/notice";

// 待处理事项计数
const pendingTasksCount = ref(0);

// 是否显示通知
const hasNotices = computed(() => pendingTasksCount.value > 0);

// 获取待处理事项数量
const getPendingTasksCount = async () => {
  try {
    const response = await NoticeAPI.getMyNoticePage({
      pageNum: 1,
      pageSize: 9999,
    });
    pendingTasksCount.value = response.total || 0;
    console.log("获取到待处理事项数量:", pendingTasksCount.value);
  } catch (error) {
    console.error("获取待处理事项数量失败:", error);
    pendingTasksCount.value = 0;
  }
};

// 组件挂载时获取待处理事项数量
onMounted(async () => {
  await getPendingTasksCount();
});
</script>

<style lang="scss" scoped>
.system-notice-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.notice-content {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background-color: #8b4513; /* 深棕色背景 */
  color: white;
  padding: 0 16px;
  overflow: hidden;
}

.scrolling-text {
  width: 100%;
  overflow: hidden;
  white-space: nowrap;

  .notice-text {
    display: inline-block;
    font-size: 14px;
    font-weight: 500;
    white-space: nowrap;
    animation: scroll-left 15s linear infinite;
  }
}

/* 滚动动画 */
@keyframes scroll-left {
  0% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(-100%);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .notice-content {
    padding: 0 8px;
  }

  .scrolling-text .notice-text {
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .notice-content {
    padding: 0 4px;
  }

  .scrolling-text .notice-text {
    font-size: 11px;
  }
}
</style>
