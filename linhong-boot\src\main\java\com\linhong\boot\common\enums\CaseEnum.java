package com.linhong.boot.common.enums;

import com.linhong.boot.common.base.IBaseEnum;

import lombok.Getter;

/**
 * 状态枚举
 */
@Getter
public enum CaseEnum implements IBaseEnum<Integer> {

	/**
	 * 案池
	 */
	CASE_POOL(0, "案池"),
	/**
	 * 新案
	 */
	NEW_CASE(1, "新案"),
	/**
	 * 继续跟进
	 */
	FOLLOW_UP(2, "继续跟进"),
	/**
	 * 重点跟进
	 */
	PRIORITY_FOLLOW_UP(3, "重点跟进"),
	/**
	 * 谈方案
	 */
	DISCUSS_PLAN(4, "谈方案"),
	/**
	 * 线下分期
	 */
	OFFLINE_INSTALLMENT(5, "线下分期"),
	/**
	 * 个性化分期
	 */
	PERSONALIZED_INSTALLMENT(6, "个性化分期"),
	/**
	 * 待减免
	 */
	WAITING_FOR_REDUCTION(7, "待减免"),
	/**
	 * 要求退案
	 */
	REQUEST_WITHDRAWAL(8, "要求退案"),
	/**
	 * 保留案件
	 */
	RETAIN_CASE(9, "保留案件"),
	/**
	 * 投诉倾向
	 */
	COMPLAINT_TENDENCY(10, "投诉倾向"),
	KONG_HAO(11, "空号停机"),
	TUI_HUI(12, "退回案池"),
	CASE_LOCK(13,"配置锁定"),
	CASE_UNLOCK(14,"配置解锁"),
	
	/**
	 * 是否换单
	 */
	CHANGE_N(-1,"否"),
	CHANGE_Y(1,"是"),
	
	/**
	 * 换单失败,案件不足
	 */
	CHANGE_FAILED(101, "换单失败,案件不足"),
	/**
	 * 换单成功
	 */
	CHANGE_SUCCESS(102, "换单成功"),
	/**
	 * 已退案
	 */
	TUI_AN(99,"退案");

    private final Integer value;


    private final String label;

    CaseEnum(Integer value, String label) {
        this.value = value;
        this.label = label;
    }
    
    /**
     * 根据value获取label
     * @param value
     * @return status.getLabel
     */
    public static String fromValue(int value) {
        for (CaseEnum status : CaseEnum.values()) {
            if (status.getValue() == value) {
                return status.getLabel();
            }
        }
        return "未知参数"+value;
    }

    /**
     * 根据label获取value
     * @param label
     * @return
     */
    public static Integer fromLabel(String label) {
        for (CaseEnum status : CaseEnum.values()) {
            if (status.getLabel().equals(label)) {
                return status.getValue();
            }
        }
        throw new IllegalArgumentException("CaseEnum fromLabel 未知参数: " + label);
    }

    
}
