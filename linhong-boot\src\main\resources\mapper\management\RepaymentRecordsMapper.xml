<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.linhong.boot.management.mapper.RepaymentRecordsMapper">
  <resultMap id="BaseResultMap" type="com.linhong.boot.management.model.entity.RepaymentRecords">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 04 09:54:44 CST 2024.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="cc_id" jdbcType="INTEGER" property="ccId" />
    <result column="outsource_serial_number" jdbcType="VARCHAR" property="outsourceSerialNumber" />
    <result column="batch_number" jdbcType="VARCHAR" property="batchNumber" />
    <result column="cardholder_code" jdbcType="VARCHAR" property="cardholderCode" />
    <result column="customer_name" jdbcType="VARCHAR" property="customerName" />
    <result column="customer_index_number" jdbcType="VARCHAR" property="customerIndexNumber" />
    <result column="case_type" jdbcType="VARCHAR" property="caseType" />
    <result column="repayment_amount" jdbcType="DECIMAL" property="repaymentAmount" />
    <result column="repayment_date" jdbcType="DATE" property="repaymentDate" />
    <result column="dept_name" jdbcType="VARCHAR" property="deptName" />
    <result column="operator_id" jdbcType="VARCHAR" property="operatorId" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
     <result column="is_delete" jdbcType="CHAR" property="isDelete" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 04 09:54:44 CST 2024.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 04 09:54:44 CST 2024.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 04 09:54:44 CST 2024.
    -->
    id, cc_id, outsource_serial_number, batch_number, cardholder_code, customer_name, 
    customer_index_number, case_type, repayment_amount, repayment_date, dept_name, operator_id, 
    created_at, updated_at
  </sql>
  
  <select id="userRank" resultType="com.linhong.boot.management.model.entity.RepaymentRecords">
  	SELECT
		u.operator_id AS operatorId,
		u.repayment_amount AS repaymentAmount,
		u.entrust_total_amount AS entrustTotalAmount,
		CASE
			WHEN u.entrust_total_amount > 0 THEN ROUND((u.repayment_amount / u.entrust_total_amount) * 100, 2)
			ELSE 0
		END AS recoveryRate
	FROM (
		SELECT
			rr.operator_id,
			SUM(rr.repayment_amount) AS repayment_amount,
			SUM(COALESCE(cu.entrust_total_amount, 0)) AS entrust_total_amount
		FROM repayment_records rr
		LEFT JOIN cc_user cu ON rr.cc_id = cu.id
		WHERE DATE(rr.repayment_date) &gt;= DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL 1 MONTH), '%Y-%m-01')
		AND DATE(rr.repayment_date) &lt; DATE_FORMAT(CURDATE(), '%Y-%m-01')
		GROUP BY rr.operator_id
		HAVING SUM(COALESCE(cu.entrust_total_amount, 0)) > 0
	) u
	ORDER BY CASE
		WHEN u.entrust_total_amount > 0 THEN ROUND((u.repayment_amount / u.entrust_total_amount) * 100, 2)
		ELSE 0
	END DESC
  </select>
  
  <select id="userRankNow" resultType="com.linhong.boot.management.model.entity.RepaymentRecords">
  	SELECT
		u.operator_id AS operatorId,
		u.repayment_amount AS repaymentAmount,
		u.entrust_total_amount AS entrustTotalAmount,
		CASE
			WHEN u.entrust_total_amount > 0 THEN ROUND((u.repayment_amount / u.entrust_total_amount) * 100, 2)
			ELSE 0
		END AS recoveryRate
	FROM (
		SELECT
			rr.operator_id,
			SUM(rr.repayment_amount) AS repayment_amount,
			SUM(COALESCE(cu.entrust_total_amount, 0)) AS entrust_total_amount
		FROM repayment_records rr
		LEFT JOIN cc_user cu ON rr.cc_id = cu.id
		WHERE DATE(rr.repayment_date) &gt;= DATE_FORMAT(CURDATE(), '%Y-%m-01')
		AND DATE(rr.repayment_date) &lt;= CURDATE()
		GROUP BY rr.operator_id
		HAVING SUM(COALESCE(cu.entrust_total_amount, 0)) > 0
	) u
	ORDER BY CASE
		WHEN u.entrust_total_amount > 0 THEN ROUND((u.repayment_amount / u.entrust_total_amount) * 100, 2)
		ELSE 0
	END DESC
  </select>

  <!-- 上个月团队回收率排名 -->
  <select id="userRankByTeam" resultType="com.linhong.boot.management.model.entity.RepaymentRecords">
  	SELECT
		u.dept_name AS operatorId,
		u.repayment_amount AS repaymentAmount,
		u.entrust_total_amount AS entrustTotalAmount,
		CASE
			WHEN u.entrust_total_amount > 0 THEN ROUND((u.repayment_amount / u.entrust_total_amount) * 100, 2)
			ELSE 0
		END AS recoveryRate
	FROM (
		SELECT
			d.name AS dept_name,
			SUM(rr.repayment_amount) AS repayment_amount,
			SUM(COALESCE(cu.entrust_total_amount, 0)) AS entrust_total_amount
		FROM repayment_records rr
		LEFT JOIN cc_user cu ON rr.cc_id = cu.id
		LEFT JOIN sys_user su ON rr.operator_id = su.id
		LEFT JOIN sys_dept d ON su.dept_id = d.id
		WHERE DATE(rr.repayment_date) &gt;= DATE_FORMAT(CURDATE(), '%Y-%m-01')
		AND DATE(rr.repayment_date) &lt;= CURDATE()
		AND d.name IS NOT NULL
		GROUP BY d.name
		HAVING SUM(COALESCE(cu.entrust_total_amount, 0)) > 0
	) u
	ORDER BY CASE
		WHEN u.entrust_total_amount > 0 THEN ROUND((u.repayment_amount / u.entrust_total_amount) * 100, 2)
		ELSE 0
	END DESC
  </select>

  <!-- 本月团队回收率排名 -->
  <select id="userRankNowByTeam" resultType="com.linhong.boot.management.model.entity.RepaymentRecords">
  	SELECT
		u.dept_name AS operatorId,
		u.repayment_amount AS repaymentAmount,
		u.entrust_total_amount AS entrustTotalAmount,
		CASE
			WHEN u.entrust_total_amount > 0 THEN ROUND((u.repayment_amount / u.entrust_total_amount) * 100, 2)
			ELSE 0
		END AS recoveryRate
	FROM (
		SELECT
			d.name AS dept_name,
			SUM(rr.repayment_amount) AS repayment_amount,
			SUM(COALESCE(cu.entrust_total_amount, 0)) AS entrust_total_amount
		FROM repayment_records rr
		LEFT JOIN cc_user cu ON rr.cc_id = cu.id
		LEFT JOIN sys_user su ON rr.operator_id = su.id
		LEFT JOIN sys_dept d ON su.dept_id = d.id
		WHERE DATE(rr.repayment_date) &gt;= DATE_FORMAT(CURDATE(), '%Y-%m-01')
		AND DATE(rr.repayment_date) &lt;= CURDATE()
		AND d.name IS NOT NULL
		GROUP BY d.name
		HAVING SUM(COALESCE(cu.entrust_total_amount, 0)) > 0
	) u
	ORDER BY CASE
		WHEN u.entrust_total_amount > 0 THEN ROUND((u.repayment_amount / u.entrust_total_amount) * 100, 2)
		ELSE 0
	END DESC
  </select>

  
  
  <select id="selectByExample" parameterType="com.linhong.boot.management.model.entity.RepaymentRecordsExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 04 09:54:44 CST 2024.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from repayment_records
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 04 09:54:44 CST 2024.
    -->
    select 
    <include refid="Base_Column_List" />
    from repayment_records
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 04 09:54:44 CST 2024.
    -->
    delete from repayment_records
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.linhong.boot.management.model.entity.RepaymentRecordsExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 04 09:54:44 CST 2024.
    -->
    delete from repayment_records
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <!-- 
  <insert id="insert" parameterType="com.linhong.boot.management.model.entity.RepaymentRecords">
    insert into repayment_records (id, cc_id, outsource_serial_number, 
      batch_number, cardholder_code, customer_name, 
      customer_index_number, case_type, repayment_amount, 
      repayment_date, dept_name, operator_id, 
      created_at, updated_at)
    values (#{id,jdbcType=INTEGER}, #{ccId,jdbcType=INTEGER}, #{outsourceSerialNumber,jdbcType=VARCHAR}, 
      #{batchNumber,jdbcType=VARCHAR}, #{cardholderCode,jdbcType=VARCHAR}, #{customerName,jdbcType=VARCHAR}, 
      #{customerIndexNumber,jdbcType=VARCHAR}, #{caseType,jdbcType=VARCHAR}, #{repaymentAmount,jdbcType=DECIMAL}, 
      #{repaymentDate,jdbcType=DATE}, #{deptName,jdbcType=VARCHAR}, #{operatorId,jdbcType=VARCHAR}, 
      #{createdAt,jdbcType=TIMESTAMP}, #{updatedAt,jdbcType=TIMESTAMP})
  </insert>
   -->
  <insert id="insertSelective" parameterType="com.linhong.boot.management.model.entity.RepaymentRecords">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 04 09:54:44 CST 2024.
    -->
    insert into repayment_records
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="ccId != null">
        cc_id,
      </if>
      <if test="outsourceSerialNumber != null">
        outsource_serial_number,
      </if>
      <if test="batchNumber != null">
        batch_number,
      </if>
      <if test="cardholderCode != null">
        cardholder_code,
      </if>
      <if test="customerName != null">
        customer_name,
      </if>
      <if test="customerIndexNumber != null">
        customer_index_number,
      </if>
      <if test="caseType != null">
        case_type,
      </if>
      <if test="repaymentAmount != null">
        repayment_amount,
      </if>
      <if test="repaymentDate != null">
        repayment_date,
      </if>
      <if test="deptName != null">
        dept_name,
      </if>
      <if test="operatorId != null">
        operator_id,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="ccId != null">
        #{ccId,jdbcType=INTEGER},
      </if>
      <if test="outsourceSerialNumber != null">
        #{outsourceSerialNumber,jdbcType=VARCHAR},
      </if>
      <if test="batchNumber != null">
        #{batchNumber,jdbcType=VARCHAR},
      </if>
      <if test="cardholderCode != null">
        #{cardholderCode,jdbcType=VARCHAR},
      </if>
      <if test="customerName != null">
        #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="customerIndexNumber != null">
        #{customerIndexNumber,jdbcType=VARCHAR},
      </if>
      <if test="caseType != null">
        #{caseType,jdbcType=VARCHAR},
      </if>
      <if test="repaymentAmount != null">
        #{repaymentAmount,jdbcType=DECIMAL},
      </if>
      <if test="repaymentDate != null">
        #{repaymentDate,jdbcType=DATE},
      </if>
      <if test="deptName != null">
        #{deptName,jdbcType=VARCHAR},
      </if>
      <if test="operatorId != null">
        #{operatorId,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.linhong.boot.management.model.entity.RepaymentRecordsExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 04 09:54:44 CST 2024.
    -->
    select count(*) from repayment_records
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 04 09:54:44 CST 2024.
    -->
    update repayment_records
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=INTEGER},
      </if>
      <if test="row.ccId != null">
        cc_id = #{row.ccId,jdbcType=INTEGER},
      </if>
      <if test="row.outsourceSerialNumber != null">
        outsource_serial_number = #{row.outsourceSerialNumber,jdbcType=VARCHAR},
      </if>
      <if test="row.batchNumber != null">
        batch_number = #{row.batchNumber,jdbcType=VARCHAR},
      </if>
      <if test="row.cardholderCode != null">
        cardholder_code = #{row.cardholderCode,jdbcType=VARCHAR},
      </if>
      <if test="row.customerName != null">
        customer_name = #{row.customerName,jdbcType=VARCHAR},
      </if>
      <if test="row.customerIndexNumber != null">
        customer_index_number = #{row.customerIndexNumber,jdbcType=VARCHAR},
      </if>
      <if test="row.caseType != null">
        case_type = #{row.caseType,jdbcType=VARCHAR},
      </if>
      <if test="row.repaymentAmount != null">
        repayment_amount = #{row.repaymentAmount,jdbcType=DECIMAL},
      </if>
      <if test="row.repaymentDate != null">
        repayment_date = #{row.repaymentDate,jdbcType=DATE},
      </if>
      <if test="row.deptName != null">
        dept_name = #{row.deptName,jdbcType=VARCHAR},
      </if>
      <if test="row.operatorId != null">
        operator_id = #{row.operatorId,jdbcType=VARCHAR},
      </if>
      <if test="row.createdAt != null">
        created_at = #{row.createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updatedAt != null">
        updated_at = #{row.updatedAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 04 09:54:44 CST 2024.
    -->
    update repayment_records
    set id = #{row.id,jdbcType=INTEGER},
      cc_id = #{row.ccId,jdbcType=INTEGER},
      outsource_serial_number = #{row.outsourceSerialNumber,jdbcType=VARCHAR},
      batch_number = #{row.batchNumber,jdbcType=VARCHAR},
      cardholder_code = #{row.cardholderCode,jdbcType=VARCHAR},
      customer_name = #{row.customerName,jdbcType=VARCHAR},
      customer_index_number = #{row.customerIndexNumber,jdbcType=VARCHAR},
      case_type = #{row.caseType,jdbcType=VARCHAR},
      repayment_amount = #{row.repaymentAmount,jdbcType=DECIMAL},
      repayment_date = #{row.repaymentDate,jdbcType=DATE},
      dept_name = #{row.deptName,jdbcType=VARCHAR},
      operator_id = #{row.operatorId,jdbcType=VARCHAR},
      created_at = #{row.createdAt,jdbcType=TIMESTAMP},
      updated_at = #{row.updatedAt,jdbcType=TIMESTAMP}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.linhong.boot.management.model.entity.RepaymentRecords">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 04 09:54:44 CST 2024.
    -->
    update repayment_records
    <set>
      <if test="ccId != null">
        cc_id = #{ccId,jdbcType=INTEGER},
      </if>
      <if test="outsourceSerialNumber != null">
        outsource_serial_number = #{outsourceSerialNumber,jdbcType=VARCHAR},
      </if>
      <if test="batchNumber != null">
        batch_number = #{batchNumber,jdbcType=VARCHAR},
      </if>
      <if test="cardholderCode != null">
        cardholder_code = #{cardholderCode,jdbcType=VARCHAR},
      </if>
      <if test="customerName != null">
        customer_name = #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="customerIndexNumber != null">
        customer_index_number = #{customerIndexNumber,jdbcType=VARCHAR},
      </if>
      <if test="caseType != null">
        case_type = #{caseType,jdbcType=VARCHAR},
      </if>
      <if test="repaymentAmount != null">
        repayment_amount = #{repaymentAmount,jdbcType=DECIMAL},
      </if>
      <if test="repaymentDate != null">
        repayment_date = #{repaymentDate,jdbcType=DATE},
      </if>
      <if test="deptName != null">
        dept_name = #{deptName,jdbcType=VARCHAR},
      </if>
      <if test="operatorId != null">
        operator_id = #{operatorId,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.linhong.boot.management.model.entity.RepaymentRecords">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 04 09:54:44 CST 2024.
    -->
    update repayment_records
    set cc_id = #{ccId,jdbcType=INTEGER},
      outsource_serial_number = #{outsourceSerialNumber,jdbcType=VARCHAR},
      batch_number = #{batchNumber,jdbcType=VARCHAR},
      cardholder_code = #{cardholderCode,jdbcType=VARCHAR},
      customer_name = #{customerName,jdbcType=VARCHAR},
      customer_index_number = #{customerIndexNumber,jdbcType=VARCHAR},
      case_type = #{caseType,jdbcType=VARCHAR},
      repayment_amount = #{repaymentAmount,jdbcType=DECIMAL},
      repayment_date = #{repaymentDate,jdbcType=DATE},
      dept_name = #{deptName,jdbcType=VARCHAR},
      operator_id = #{operatorId,jdbcType=VARCHAR},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>