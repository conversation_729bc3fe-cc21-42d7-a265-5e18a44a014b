package ${packageName}.${moduleName}.${subpackageName};

import ${packageName}.common.base.BasePageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
#if(${hasLocalDateTime})
import java.time.LocalDateTime;
import java.util.List;
#end
#if(${hasBigDecimal})
import java.math.BigDecimal;
#end

/**
 * $!{businessName}分页查询对象
 *
 * <AUTHOR>
 * @since ${date}
 */
@Schema(description ="$!{businessName}查询对象")
@Getter
@Setter
public class ${entityName}Query extends BasePageQuery {

#if($fieldConfigs)
    #foreach($fieldConfig in ${fieldConfigs})
        #if($fieldConfig.isShowInQuery)
            #if("$!fieldConfig.fieldComment" != "")
    @Schema(description = "${fieldConfig.fieldComment}")
            #end
            #if($fieldConfig.formType == "DATE" || $fieldConfig.formType == "DATE_TIME")
                #if($fieldConfig.queryType == "BETWEEN")
    private List<String> ${fieldConfig.fieldName};
                #else
    private ${fieldConfig.fieldType} ${fieldConfig.fieldName};
                #end
            #else
    private ${fieldConfig.fieldType} ${fieldConfig.fieldName};
            #end
         #end
    #end
#end
}
