package com.linhong.boot.system.constant;

/**
 * 登录页配置常量
 *
 * <AUTHOR>
 */
public class LoginConfigConstants {

    /**
     * 配置键常量
     */
    public static class ConfigKeys {
        /** 系统LOGO */
        public static final String SYSTEM_LOGO = "system_logo";
        
        /** 系统标题 */
        public static final String SYSTEM_TITLE = "system_title";
        
        /** 企业文化标题 */
        public static final String CULTURE_TITLE = "culture_title";
    }

    /**
     * 配置类型常量
     */
    public static class ConfigTypes {
        /** 文本类型 */
        public static final String TEXT = "TEXT";
        
        /** 图片类型 */
        public static final String IMAGE = "IMAGE";
    }

    /**
     * 状态常量
     */
    public static class Status {
        /** 启用 */
        public static final Integer ENABLED = 1;
        
        /** 禁用 */
        public static final Integer DISABLED = 0;
    }

    /**
     * 默认配置值
     */
    public static class DefaultValues {
        /** 默认系统LOGO */
        public static final String DEFAULT_LOGO = "/images/logo.png";
        
        /** 默认系统标题 */
        public static final String DEFAULT_SYSTEM_TITLE = "CF-作业系统";
        
        /** 默认企业文化标题 */
        public static final String DEFAULT_CULTURE_TITLE = "企业文化";
        
        /** 默认企业文化内容 */
        public static final String[][] DEFAULT_CULTURE_ITEMS = {
            {"😊", "托底人生，共筑未来!"},
            {"😊", "新百年基业，成万千大众!"},
            {"👑", "万千大众共生日，便民县业永同时!"},
            {"📚", "做大、做强、做优秀、做永恒!"}
        };
    }

    /**
     * 缓存相关常量
     */
    public static class Cache {
        /** 缓存键 */
        public static final String CACHE_KEY = "login:config";
        
        /** 锁键 */
        public static final String LOCK_KEY = "login:config:lock";
        
        /** 缓存过期时间（分钟） */
        public static final int CACHE_EXPIRE_MINUTES = 10;
        
        /** 锁过期时间（秒） */
        public static final int LOCK_EXPIRE_SECONDS = 30;
    }

    /**
     * 文件上传相关常量
     */
    public static class Upload {
        /** LOGO文件最大大小（字节） */
        public static final long MAX_LOGO_SIZE = 2 * 1024 * 1024; // 2MB
        
        /** 允许的图片类型 */
        public static final String[] ALLOWED_IMAGE_TYPES = {
            "image/png", 
            "image/jpeg", 
            "image/jpg", 
            "image/svg+xml"
        };
    }
}
