<template>
  <div class="details-container">
    <el-row :gutter="16" class="main-layout">
      <!-- 左侧内容区域 -->
      <el-col :span="19" class="left-content">
        <!-- 案件详情 -->
        <el-card shadow="never" class="task-basic-card">
          <div class="case-detail-header">
            <div class="case-detail-title">
              <el-icon class="case-detail-icon"><InfoFilled /></el-icon>
              <span>案件详情</span>
            </div>
          </div>
          <el-divider class="case-detail-divider" />
          <el-row :gutter="12" class="basic-info-row">
            <el-col
              v-for="item in basicInformationMune"
              v-show="!item.hasAuth"
              :key="item.prop"
              :span="4"
              class="basic-info-col"
            >
              <div class="cell_item">
                <div class="cell_label">
                  {{ item.label }}
                </div>
                <div class="cell_value">
                  <template v-if="item.isEditior">
                    <el-input
                      v-model.trim="detailData[item.prop]"
                      type="text"
                      :size="'small'"
                      placeholder="可手动输入"
                      clearable
                      :disabled="pagesType != 2"
                      @change="saveDetail"
                    />
                  </template>
                  <template v-else-if="item.prop == 'caseType'">
                    {{ caseTypeName(detailData[item.prop]) || "" }}
                  </template>
                  <template v-else>{{ detailData[item.prop] }}</template>
                </div>
              </div>
            </el-col>
          </el-row>
          
          <!-- 职业信息区域 - 参考案件详情.html中的sub-info区域设计，使用浅灰色背景 -->
          <div class="occupation-info-section">
            <el-row :gutter="12">
              <el-col
                v-for="item in occupationInformationMune"
                v-show="!item.hasAuth"
                :key="item.prop"
                :span="4"
              >
                <div class="occupation-cell_item">
                  <div class="occupation-cell_label">
                    {{ item.label }}
                  </div>
                  <div class="occupation-cell_value">
                    <template v-if="item.isEditior">
                      <el-input
                        v-model.trim="detailData[item.prop]"
                        type="text"
                        :size="'small'"
                        placeholder="可手动输入"
                        clearable
                        :disabled="pagesType != 2"
                        @change="saveDetail"
                      />
                    </template>
                    <template v-else-if="item.prop == 'caseType'">
                      {{ caseTypeName(detailData[item.prop]) || "" }}
                    </template>
                    <!-- 委托时月龄分档和账户号后七位字段的特殊处理 -->
                    <template v-else-if="item.prop === 'entrustAgeBracket' || item.prop === 'accountNumberLast7'">
                      <el-tooltip
                        v-if="isMultipleData(detailData[item.prop])"
                        effect="dark"
                        placement="top"
                        :content="detailData[item.prop] || '--'"
                      >
                        <span class="truncated-text" style="cursor: pointer">
                          {{ getFirstData(detailData[item.prop]) }}...
                        </span>
                      </el-tooltip>
                      <span v-else>{{ detailData[item.prop] || "--" }}</span>
                    </template>
                    <template v-else>{{ detailData[item.prop] || "--" }}</template>
                  </div>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-card>

        <!-- 跟进记录栏 -->
        <el-card shadow="never" class="follow-up-card">
          <el-row :gutter="10" class="follow-up-row">
            <!-- 左侧选项卡按钮 -->
            <el-col :span="4" class="action-tabs">
              <div class="tab-button tab-button-blue" @click="contactShow = true">
                <el-icon><User /></el-icon>
                <span>联系人</span>
              </div>
              <div class="tab-button tab-button-green" @click="addStatisticsShow = true">
                <el-icon><Notebook /></el-icon>
                <span>新增统计</span>
              </div>
              <div
                class="tab-button tab-button-red"
                @click.stop="allocationRecordsShow = !allocationRecordsShow"
              >
                <el-icon><TrendCharts /></el-icon>
                <span>分派记录</span>
              </div>
              <div class="tab-button tab-button-purple" @click.stop="historicalRecordShow = true">
                <el-icon><Clock /></el-icon>
                <span>历史记录</span>
              </div>
            </el-col>
            <!-- 右侧表单 -->
            <el-col :span="20" class="form-container">
              <eleForm
                ref="eleFormRef"
                :formValue="eleFormValue"
                :formItemList="followUpFormList"
                :operateOpen="false"
                :formBox="{
                  spanItem: 6,
                  formContent: 24,
                  operate: 0,
                  operateItem: 0,
                  operateGrid: 'start'
                }"
                @handleSubmit="followUpFun"
                @fromChange="followUpFromChange"
              />
            </el-col>
          </el-row>
        </el-card>

        <!-- 催记表单 -->
        <el-card shadow="never" class="reminder-card">
          <el-table
            ref="dataTableRef"
            v-loading="loading2"
            :data="followUpRecordsList"
            highlight-current-row
            border
            style="width: 100%"
          >
            <el-table-column
              v-for="item in reminderMune"
              :key="item.prop"
              :label="item.label"
              :prop="item.prop"
              :width="item.width || ''"
              min-width="80"
              :show-overflow-tooltip="!item.noEllipsis"
            >
              <!-- 跟进时间鼠标悬停展示详细时间 -->
              <template v-if="item.prop == 'updatedAt'" #default="scope">
                <el-tooltip effect="dark" placement="top">
                  <template #content>
                    <div>详细时间：{{ scope.row.updatedAt || '--' }}</div>
                  </template>
                  <span style="cursor: pointer">
                    {{ formatShortDate(scope.row.updatedAt) || '--' }}
                  </span>
                </el-tooltip>
              </template>
              
              <template v-if="item.prop == 'calledNumber'" #default="scope">
                <el-tooltip effect="dark" placement="top">
                  <template #content>
                    <div>被叫号码：{{ scope.row.calledNumber || '--' }}</div>
                  </template>
                  <el-button
                    link
                    type="primary"
                    @click="calledNumberFun(scope.row.calledNumber, 'calledNumber')"
                  >
                    {{ scope.row.calledNumber }}
                  </el-button>
                </el-tooltip>
              </template>
              <template v-if="item.prop == 'callerNumber'" #default="scope">
                <el-tooltip effect="dark" placement="top">
                  <template #content>
                    <div>主叫号码：{{ scope.row.callerNumber || '--' }}</div>
                  </template>
                  <el-button
                    link
                    type="primary"
                    @click="calledNumberFun(scope.row.callerNumber, 'callerNumber')"
                  >
                    {{ scope.row.callerNumber }}
                  </el-button>
                </el-tooltip>
              </template>
              
              <!-- 还款时间显示 -->
              <template v-if="item.prop == 'repaymentDate'" #default="scope">
                <el-tooltip effect="dark" placement="top">
                  <template #content>
                    <div>还款时间：{{ scope.row.repaymentDate || '--' }}</div>
                  </template>
                  <span style="cursor: pointer">
                    {{ formatShortDate(scope.row.repaymentDate) || '--' }}
                  </span>
                </el-tooltip>
              </template>

              <!-- 失联查询结果显示 -->
              <template v-if="item.prop == 'missingContactResult'" #default="scope">
                <el-tooltip effect="dark" placement="top">
                  <template #content>
                    <div>失联查询结果：{{ missingContactResultName(scope.row.missingContactResult) || '--' }}</div>
                  </template>
                  <span style="cursor: pointer">
                    {{ missingContactResultName(scope.row.missingContactResult) || '--' }}
                  </span>
                </el-tooltip>
              </template>
              
              <!-- 跟进记录显示 - 一行显示，超出省略号，悬停显示全部 -->
              <template v-if="item.prop == 'collectionNotes'" #default="scope">
                <el-tooltip effect="dark" placement="top">
                  <template #content>
                    <div style="max-width: 300px; white-space: pre-wrap; word-break: break-word;">
                      跟进记录：{{ scope.row.collectionNotes || '--' }}
                    </div>
                  </template>
                  <div class="collection-notes-cell" style="cursor: pointer">
                    {{ scope.row.collectionNotes || '--' }}
                  </div>
                </el-tooltip>
              </template>

              <template v-if="item.prop == 'isMarkedForChange'" #default="scope">
                <el-tooltip effect="dark" placement="top">
                  <template #content>
                    <div>是否标记换单：{{ getMarkedForChangeText(scope.row.isMarkedForChange) }}</div>
                  </template>
                  <span style="cursor: pointer">
                    {{ getMarkedForChangeText(scope.row.isMarkedForChange) }}
                  </span>
                </el-tooltip>
              </template>
              <template v-if="item.prop == 'collectionResult'" #default="scope">
                <el-tooltip effect="dark" placement="top">
                  <template #content>
                    <div>跟进状态：{{ collectionResultName(scope.row.collectionResult) || '--' }}</div>
                  </template>
                  <span style="cursor: pointer">
                    {{ collectionResultName(scope.row.collectionResult) }}
                  </span>
                </el-tooltip>
              </template>

              <!-- 其他字段的默认显示，添加tooltip -->
              <template v-if="!['updatedAt', 'calledNumber', 'callerNumber', 'repaymentDate', 'missingContactResult', 'collectionNotes', 'isMarkedForChange', 'collectionResult'].includes(item.prop)" #default="scope">
                <el-tooltip effect="dark" placement="top">
                  <template #content>
                    <div>{{ item.label }}：{{ scope.row[item.prop] || '--' }}</div>
                  </template>
                  <span style="cursor: pointer">
                    {{ scope.row[item.prop] || '--' }}
                  </span>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column
              v-hasPerm="['sys:poolDetails:followUpDelete']"
              fixed="right"
              label="操作"
              width="100"
            >
              <template #default="scope">
                <el-button
                  type="danger"
                  size="small"
                  icon="Delete"
                  class="delete-button"
                  @click="followUpDelete(scope.row.id)"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <div class="pagination-container">
            <pagination
              v-if="followUpTotal > 0"
              v-model:total="followUpTotal"
              v-model:page="queryParams2.pageNum"
              v-model:limit="queryParams2.pageSize"
              :page-sizes="[10, 20, 50, 100, 200, 500]"
              @change="fetchPageData"
              @pagination="followUpRecordsFindByPoolId"
            />
          </div>
        </el-card>
      </el-col>

      <!-- 右侧目录区域 -->
      <el-col :span="5" class="right-directory">
        <div class="directory-container">
          <!-- 目录标题 -->
          <div class="directory-header">
            <el-icon class="directory-icon"><Menu /></el-icon>
            <span class="directory-title">目录</span>
          </div>
          <div class="directory-divider" />

          <!-- 表头 -->
          <div class="directory-table-header">
            <div class="header-cell customer-header sortable" @click="sortDirectory('customerName')">
              <span>客户姓名</span>
              <el-icon class="sort-icon">
                <CaretTop v-if="sortField === 'customerName' && sortOrder === 'asc'" />
                <CaretBottom v-else-if="sortField === 'customerName' && sortOrder === 'desc'" />
                <DCaret v-else />
              </el-icon>
            </div>
            <div class="header-cell amount-header sortable" @click="sortDirectory('entrustAmount')">
              <span>委托金额</span>
              <el-icon class="sort-icon">
                <CaretTop v-if="sortField === 'entrustAmount' && sortOrder === 'asc'" />
                <CaretBottom v-else-if="sortField === 'entrustAmount' && sortOrder === 'desc'" />
                <DCaret v-else />
              </el-icon>
            </div>
            <div class="header-cell days-header sortable" @click="sortDirectory('days')">
              <span>未跟进天数</span>
              <el-icon class="sort-icon">
                <CaretTop v-if="sortField === 'days' && sortOrder === 'asc'" />
                <CaretBottom v-else-if="sortField === 'days' && sortOrder === 'desc'" />
                <DCaret v-else />
              </el-icon>
            </div>
          </div>

          <!-- 目录列表 -->
          <div v-loading="loading" class="directory-content">
            <div
              v-for="(item, index) in roleList"
              :key="item.id"
              class="directory-item"
              @click="handleOpenDialog(item.id)"
            >
              <div class="directory-row">
                <div class="row-cell customer-cell">{{ item.customerName || "——" }}</div>
                <div class="row-cell amount-cell">¥{{ formatAmount(item.entrustAmount) }}</div>
                <div class="row-cell days-cell">
                  <span class="days" :class="getDaysClass(item.days)">{{ item.days || 0 }}天</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 分页控件 -->
          <div class="directory-pagination">
            <button class="nav-btn" @click="pageTurning(1)">上一页</button>
            <span class="pagination-text">{{ roleTotal }}条</span>
            <button class="nav-btn" @click="pageTurning(2)">下一页</button>
            <div class="pagination-info">
              <select class="page-size-select" v-model="currentPageSize" @change="handlePageSizeChange">
                <option value="10">10</option>
                <option value="20">20</option>
                <option value="30">30</option>
              </select>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
    <!-- 联系人 -->
    <contactPerson v-model:show="contactShow" v-model:ids="ids" />
    <!-- 分配记录 -->
    <allocationRecords v-model:show="allocationRecordsShow" v-model:ids="ids" />
    <!-- 任务预测 -->
    <taskPrediction ref="taskPredictionRef" v-model:show="taskPredictionShow" v-model:ids="ids" />
    <!-- 历史记录 -->
    <historicalRecord
      ref="historicalRecordRef"
      v-model:show="historicalRecordShow"
      v-model:ids="ids"
    />
    <!-- 新增统计 -->
    <addStatistics v-model:show="addStatisticsShow" v-model:ids="ids" />
  </div>
</template>

<script setup lang="ts">
import { ElMessage, ElMessageBox, ElButton, ElLoading } from "element-plus";
import {
  InfoFilled,
  User,
  Notebook,
  TrendCharts,
  Clock,
  Delete,
  Menu,
  CaretTop,
  CaretBottom,
  DCaret,
} from "@element-plus/icons-vue";

defineOptions({
  name: "Pool",
  inheritAttrs: false,
});
import {
  catalogueMune,
  reminderMune,
  connectionMune,
  basicInformationMune,
  occupationInformationMune,
  followUpList,
  caseType,
} from "./fieldsSys";
import eleForm from "@/components/EleComponents/ele-form.vue";
import dataSystemAPI from "@/api/dataSystem/pool";
import RoleAPI, { RolePageVO, RoleForm, RolePageQuery } from "@/api/system/role";
import contactPerson from "./components/contactPerson.vue";
import allocationRecords from "./components/allocationRecords.vue";
import taskPrediction from "./components/taskPrediction.vue";
import historicalRecord from "./components/historicalRecord.vue";
import addStatistics from "./components/addStatistics.vue";
import { removeEmptyValues } from "@/utils/commit";
import { useAppStore, useUserStore, useArchitectureStoreHook } from "@/store";
import { $subtract, $divide } from "@/utils/calculate";
const architectureStore = useArchitectureStoreHook();
const userStore = useUserStore();
const router = useRouter();
const appStore = useAppStore();
// 关闭测边栏
appStore.closeSideBar();
const loading = ref(false);
// 详情id
const ids: any = ref(null);

// 详情
const detailData: any = ref({});
const findByPoolList: any = ref([]);
const succinctDetail = ref(true); // 默认展示全部
//分配记录
const allocationRecordsShow = ref(false);
// 目录回收按钮
const recycle = ref(false);
// 目录数据
const roleList = ref<[]>();
const roleTotal = ref(0);
const roleListHeight = ref(100);
const directoryTableHeight = ref(100);

// 联系人弹窗
const contactShow = ref(false);
// 新增统计弹窗
const addStatisticsShow = ref(false);

// 目录排序状态
const sortField = ref('');
const sortOrder = ref('');

//标志当前案子
const tableRowClassName = ({ row, rowIndex }: any) => {
  if (row.id === ids.value) {
    return "res-nova";
  }
  return "";
};

const formBox = ref({
  spanItem: 4.6, // 每项占比 - 一行显示5个字段 (24/5 = 4.8，稍微调小适应间距)
  formContent: 24, // 表单父级占比
  operate: 3, // 操作父容器占比
  operateItem: 12,
  operateGrid: " start",
});
const queryParams = ref({
  pageNum: 1,
  pageSize: 20, // 默认20条，与select默认值对应
});

// 目录分页条数
const currentPageSize = ref<number>(20);
/**
 * 页面来源
 * 1 - 案池
 * 2 - 作业清单
 * 3 - 还款管理
 * 4 - 换单记录
 */
const pagesType: any = ref(1);
watch(
  succinctDetail,
  (val) => {
    roleListHeight.value = 0;
    roleList.value = [];
    setTimeout(() => {
      pagesSizeEditor();
    }, 0);
  },
  { deep: true, immediate: false }
);
const pagesSizeEditor = () => {
  // 计算目录表格高度，让其占满右侧区域
  const appMainEl = document.querySelectorAll(".app-main")[0] as HTMLElement;
  if (appMainEl) {
    // 减去页面边距、卡片头部、分页等高度
    directoryTableHeight.value = appMainEl.offsetHeight - 200;
  }
  handleQuery();
};
// 详情
const handleOpenDialog = (id?: any) => {
  router.push({
    path: "/dataSystem/pool/poolDetails",
    query: { id, pagesType: pagesType.value, search: JSON.stringify(queryParams.value) },
  });
  setTimeout(() => {
    // window.location.reload();
    // init();
    resetFollowUp();
  }, 0);
};
// 查询
function handleQuery() {
  loading.value = true;
  let apiName: any = "";
  console.log("查询目录");
  if (pagesType.value == 1) apiName = "casepoolMenuPage";
  if (pagesType.value == 2) apiName = "workWorkMunePage";
  if (pagesType.value == 3) apiName = "casepoolRepaymentMenuPage";
  if (pagesType.value == 4) apiName = "casepoolMenuPage"; // 换单记录使用案池的API

  dataSystemAPI[apiName]({
    ...queryParams.value,
    id: ids.value,
  })
    .then((data: any) => {
      roleList.value = data.list;
      roleTotal.value = data.total;
    })
    .finally(() => {
      loading.value = false;
    });
}
// 目录切换
const itemTurning = (type: number) => {
  const arr = roleList.value;
  const itemIndex = arr?.findIndex((i) => i.id == ids.value);

  let num = 0;
  if (type == 1) {
    if (itemIndex == 0) {
      return ElMessage({
        message: "没有上一条数据了",
        type: "error",
      });
    }
    num = itemIndex - 1;
  } else {
    if (itemIndex >= arr.length - 1) {
      return ElMessage({
        message: "没有下一条数据了",
        type: "error",
      });
    }
    num = itemIndex + 1;
  }

  handleOpenDialog(arr[num].id);
};
// 目录翻页
const pageTurning = (type: number) => {
  const { pageNum } = queryParams.value;
  if (type == 1) {
    if (pageNum == 1) {
      return ElMessage({
        message: "没有上一页了",
        type: "error",
      });
    }
    queryParams.value.pageNum = pageNum - 1;
  } else {
    if (roleTotal.value <= roleList.value.length) {
      return ElMessage({
        message: "没有下一页了",
        type: "error",
      });
    }
    queryParams.value.pageNum = pageNum + 1;
  }
  handleQuery();
};

// 分页条数变化处理
const handlePageSizeChange = () => {
  queryParams.value.pageSize = Number(currentPageSize.value);
  queryParams.value.pageNum = 1; // 重置到第一页
  console.log('分页条数变化:', currentPageSize.value, '查询参数:', queryParams.value);
  handleQuery();
};
//保存
const saveDetail = () => {
  let obj = basicInformationMune.filter((item) => item.isEditior);
  let detailObj: any = {};
  obj.forEach((item) => {
    detailObj[item.prop] = detailData.value[item.prop];
  });
  dataSystemAPI
    .casepoolUpdateCase({
      ...detailObj,
      id: ids.value,
    })
    .then((e) => {
      ElMessage({
        message: "保存成功",
        type: "success",
      });
      getCaseDetail();
    });
};

//获取详情
const getCaseDetail = () => {
  dataSystemAPI
    .getCaseDetail({
      id: ids.value,
    })
    .then((data: any) => {
      detailData.value = data;
      eleFormValue.value.isMarkedForChange = data["isMarkChange"] == 1 ? true : false;
      
      // 为回款金额字段动态添加验证规则
      const repaymentAmountField = followUpFormList.value.find((e: any) => e.field == "repaymentAmount");
      if (repaymentAmountField && data.entrustTotalAmount) {
        const maxAmount = parseFloat(data.entrustTotalAmount) || 0;
        repaymentAmountField.rules = [
          {
            validator: (rule: any, value: any, callback: any) => {
              if (!value || value === '') {
                callback(); // 允许空值
                return;
              }
              const inputAmount = parseFloat(value);
              if (isNaN(inputAmount)) {
                callback(new Error('请输入有效的金额'));
                return;
              }
              if (inputAmount < 0) {
                callback(new Error('回款金额不能为负数'));
                return;
              }
              if (inputAmount > maxAmount) {
                callback(new Error(`回款金额不能超过委托金额 ¥${maxAmount.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`));
                return;
              }
              callback();
            },
            trigger: 'blur'
          }
        ];
      }
    });
};

/*--------------跟进相关----------------*/
const loading2 = ref(false);
const queryParams2 = reactive<RolePageQuery>({
  pageNum: 1,
  pageSize: 10,
});
const followUpRecordsList: any = ref([]);
const followUpTotal: any = ref(0);
const eleFormRef: any = ref(null);
const eleFormValue: any = ref({
  missingContactResult: "", // 默认为空，将在获取跟进记录后设置为最新记录的值
});
const followUpFormList = ref(followUpList);

// 筛选参数 - 用于筛选跟进记录列表  
const filterParams = ref({
  missingContactResult: "", // 移除默认的"all"值
});
//跟进号码赋值
const calledNumberFun = (phone: Number, key: string) => {
  // eleFormValue.value[key] = phone;
  eleFormRef.value.setFormVlaue({
    [key]: phone,
  });
};
// form表单值
const followUpFromChange = (data: any) => {
  console.log("=== 表单值变化 ===", data);

  // 只有在作业清单页面（pagesType == 2）才处理换单字段的业务逻辑
  if (pagesType.value == 2) {
    if (data["collectionResult"] == "2") {
      followUpFormList.value.find((e: any) => e.label == "是否标记换单").disabled = false;
    } else {
      if (data["collectionResult"]) eleFormRef.value.setFormVlaue({ isMarkedForChange: false });
      followUpFormList.value.find((e: any) => e.label == "是否标记换单").disabled = true;
    }
  }
  // 如果不是作业清单页面，换单字段保持禁用状态（已在初始化时设置）

  // 移除了失联查询结果等字段变化时的筛选功能，不再自动重新获取跟进记录列表
};
//获取跟进列表
const followUpRecordsFindByPoolId = () => {
  loading2.value = true;

  // 构建查询参数，移除了失联查询结果的筛选功能
  let queryData: any = {
    ...queryParams2,
    userId: ids.value,
  };

  console.log("查询跟进记录参数:", queryData);

  dataSystemAPI
    .followUpRecordsFindByPoolId(queryData)
    .then((data: any) => {
      followUpRecordsList.value = data.list;
      followUpTotal.value = data.total;

      // 调试：打印是否标记换单字段的值
      if (data.list && data.list.length > 0) {
        console.log("跟进记录数据示例:", data.list[0]);
        console.log("isMarkedForChange字段值:", data.list.map(item => ({
          id: item.id,
          isMarkedForChange: item.isMarkedForChange,
          type: typeof item.isMarkedForChange
        })));
      }

      // 设置失联查询结果默认值为最新一条记录的失联查询结果
      if (data.list && data.list.length > 0 && data.list[0].missingContactResult) {
        const latestMissingContactResult = data.list[0].missingContactResult;
        eleFormRef.value?.setFormVlaue({
          missingContactResult: latestMissingContactResult
        });
        console.log("设置失联查询结果默认值为:", latestMissingContactResult);
      }
    })
    .finally(() => {
      loading2.value = false;
    });
};
// 新增跟进
const followUpFun = (from: any) => {
  let obj = removeEmptyValues(from);
  obj.isMarkedForChange = obj.isMarkedForChange ? 1 : 0;
  
  // 业务逻辑验证：回款金额不能超过委托金额
  if (obj.repaymentAmount && detailData.value.entrustTotalAmount) {
    const repaymentAmount = parseFloat(obj.repaymentAmount);
    const maxAmount = parseFloat(detailData.value.entrustTotalAmount);
    
    if (!isNaN(repaymentAmount) && !isNaN(maxAmount) && repaymentAmount > maxAmount) {
      ElMessage({
        message: `回款金额不能超过委托金额 ¥${maxAmount.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`,
        type: "error",
      });
      return; // 阻止提交
    }
  }
  
  console.log(obj);

  dataSystemAPI
    .followUpRecordsSave({
      ...obj,
      userId: ids.value,
    })
    .then((e: any) => {
      ElMessage({
        message: "新增成功",
        type: "success",
      });

      eleFormRef.value.resetFields();
      followUpRecordsFindByPoolId();
    });
};
// 重置 跟进记录
const resetFollowUp = () => {
  loading2.value = true;
  eleFormRef.value.resetFields();
  setTimeout(() => {
    loading2.value = false;
  }, 0);
};
// 删除跟进
const followUpDelete = (id: number) => {
  ElMessageBox.confirm("确认删除该跟进?", "提示", {
    confirmButtonText: "确认",
    cancelButtonText: "取消",
    type: "warning",
    draggable: true,
  }).then(() => {
    loading2.value = true;
    dataSystemAPI
      .followUpRecordsDelete({
        id: id,
      })
      .then((e) => {
        ElMessage({
          message: "删除成功",
          type: "success",
        });
        loading2.value = false;
        followUpRecordsFindByPoolId();
      });
  });
};
const route = useRoute();

// 案件类型
const caseTypeName = computed(() => {
  return (type: any) => {
    const { options } = caseType;
    let name = false;
    if (options && options.length > 0) {
      name = options.find((t: any) => t.value == type)?.label || "-";
    }
    return name;
  };
});
// 跟进结果
const collectionResultName = computed(() => {
  return (type: any) => {
    const { options } = followUpFormList.value.find((e: any) => e.field == "collectionResult");
    let name = "";
    if (options && options.length > 0) {
      if (type == 1) {
        name = "新案";
      } else {
        name = options.find((t: any) => t.value == type)?.label || "-";
      }
    }
    return name;
  };
});

// 失联查询结果
const missingContactResultName = computed(() => {
  return (type: any) => {
    const missingContactField = followUpFormList.value.find((e: any) => e.field == "missingContactResult");
    if (!missingContactField || !missingContactField.options) {
      return type || "--";
    }
    const { options } = missingContactField;
    let name = "";
    if (options && options.length > 0) {
      name = options.find((t: any) => t.value == type)?.label || type || "--";
    }
    return name;
  };
});

// 是否标记换单文字映射
const getMarkedForChangeText = (value: any) => {
  console.log("getMarkedForChangeText 接收到的值:", value, "类型:", typeof value);
  if (value === 1 || value === "1") {
    return "是";
  } else if (value === -1 || value === "-1") {
    return "否";
  } else {
    return "--"; // 处理null、undefined或其他值
  }
};

/**
 * 任务预测相关
 */
const taskPredictionShow = ref(false);
const taskPredictionRef: any = ref(null);
/**
 * 历史记录
 */
const historicalRecordShow = ref(false);
const historicalRecordRef = ref(null);

const init = async () => {
  let { query } = toRefs(route);
  if (query.value) {
    ids.value = query.value.id;
    pagesType.value = query.value.pagesType || 1;
    if (query.value.search) {
      queryParams.value = JSON.parse(query.value.search);
      // 确保currentPageSize与queryParams.pageSize同步
      currentPageSize.value = queryParams.value.pageSize || 20;
    }

    // 先设置选项
    followUpFormList.value.find((e: any) => e.field == "collectionResult").options =
      await architectureStore.casepoolKeyValueList("follow_status");
    const missingContactOptions = await architectureStore.casepoolKeyValueList("lost_status");
    // 设置失联查询结果选项，不包含"全部"选项
    followUpFormList.value.find((e: any) => e.field == "missingContactResult").options = missingContactOptions;

    // 根据页面来源设置表单项的禁用状态
    // 只有作业清单（pagesType == 2）才能编辑，其他页面都禁用
    const isDisabled = pagesType.value != 2;
    followUpFormList.value.forEach((item: any) => {
      // 所有表单项都根据页面来源设置禁用状态
      item.disabled = isDisabled;
    });

    console.log("followUpFormList", followUpFormList.value);
    console.log(
      "失联查询结果选项:",
      followUpFormList.value.find((e: any) => e.field == "missingContactResult").options
    );

    // 然后获取跟进记录
    followUpRecordsFindByPoolId();
    getCaseDetail();
  }

  nextTick(() => {
    pagesSizeEditor();
  });
};
/**
 * 获取表格内容可用高度
 * **/
const contentHeader = ref(500);

// 获取表格高度
const getTableHeight = () => {
  // 表格高度完全由CSS控制，使用calc(100vh - 150px)最大化显示区域
  // 适应各种缩放比例，包括125%等高DPI场景
  contentHeader.value = 500;
  console.log("表格高度由CSS控制，最大化显示区域适应各种缩放比例");
};

// 根据天数返回对应的样式类 - 所有未跟进天数都显示红色
const getDaysClass = (days: number) => {
  return "days-red";
};

// 格式化金额显示
const formatAmount = (amount: any) => {
  if (!amount) return "0.00";
  return parseFloat(amount).toLocaleString("zh-CN", {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  });
};

// 目录排序功能
const sortDirectory = (field: string) => {
  if (sortField.value === field) {
    // 同一字段：切换排序顺序 asc -> desc -> 无排序
    if (sortOrder.value === 'asc') {
      sortOrder.value = 'desc';
    } else if (sortOrder.value === 'desc') {
      sortField.value = '';
      sortOrder.value = '';
    } else {
      sortOrder.value = 'asc';
    }
  } else {
    // 不同字段：设置为升序
    sortField.value = field;
    sortOrder.value = 'asc';
  }
  
  // 重新获取数据
  handleQuery();
};

// 格式化短日期显示 - 用于跟进时间和还款时间的简短显示
const formatShortDate = (dateString: string) => {
  if (!dateString) return "--";
  
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return "--";
    
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    
    return `${year}/${month}/${day}`;
  } catch (error) {
    return "--";
  }
};

// 判断是否为多数据（用逗号、分号或其他分隔符分隔）
const isMultipleData = (value: string) => {
  if (!value || typeof value !== 'string') return false;
  // 检查是否包含常见的分隔符
  return value.includes(',') || value.includes('，') || value.includes(';') || value.includes('；');
};

// 获取第一个数据项
const getFirstData = (value: string) => {
  if (!value || typeof value !== 'string') return '--';
  // 按照常见分隔符分割，取第一个
  const separators = [',', '，', ';', '；'];
  for (const separator of separators) {
    if (value.includes(separator)) {
      return value.split(separator)[0].trim();
    }
  }
  return value;
};

onUpdated(async () => {
  console.log("update");
  init();
});
onMounted(async () => {
  console.log("onMounted");
  init();
  nextTick(() => {
    getTableHeight();
  });
});
</script>
<style lang="scss" scoped>
/* 案件详情容器样式 */
.details-container {
  height: calc(100vh - 60px);
  padding: 12px;
  overflow: hidden;
  box-sizing: border-box;
}

.main-layout {
  height: 100%;
  overflow: hidden;
}

.left-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow-y: auto;
  padding-right: 8px;
}

.right-directory {
  height: 100%;
  padding-left: 0 !important;
}

.task-basic-card {
  margin-bottom: 8px;
  flex-shrink: 0;
}

.task-basic-card :deep(.el-card__body) {
  padding: 12px;
}

.case-detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.case-detail-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: bold;
  color: #2c3e50;
}

.case-detail-icon {
  margin-right: 8px;
  color: #409eff;
  font-size: 18px;
}

.case-detail-divider {
  margin: 8px 0;
}

.follow-up-card {
  margin-bottom: 8px;
  flex-shrink: 0;
}

.follow-up-card :deep(.el-card__body) {
  padding: 12px;
}

.follow-up-row {
  align-items: flex-start;
  margin: 0 !important;
}

.form-container {
  display: flex;
  flex-direction: column;
  padding-left: 12px !important;
  margin-left: 0 !important;
}

/* 优化表单内部布局，使字段更靠左 */
.form-container :deep(.el-row) {
  margin-left: 0 !important;
  margin-right: 0 !important;
}

.form-container :deep(.el-col) {
  padding-left: 4px !important;
  padding-right: 4px !important;
}

.follow-up-card :deep(.el-form-item) {
  margin-bottom: 8px;
  display: flex !important;
  flex-direction: row !important;
  align-items: center !important;
  justify-content: flex-start !important;
}

/* 跟进记录表单标签样式 - 左侧固定宽度，适中大小 */
.follow-up-card :deep(.el-form-item__label) {
  width: 120px !important;
  min-width: 120px !important;
  max-width: 120px !important;
  text-align: center !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  color: #2c3e50 !important;
  white-space: nowrap !important;
  margin-right: 8px !important;
  margin-bottom: 0 !important;
  padding: 0 !important;
  line-height: 40px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* 跟进记录表单内容区域 - 右侧自适应 */
.follow-up-card :deep(.el-form-item__content) {
  flex: 1 !important;
  max-width: none !important;
  margin-left: 0 !important;
  width: auto !important;
  display: flex !important;
  align-items: center !important;
  justify-content: flex-start !important;
}

.follow-up-card :deep(.el-input__wrapper) {
  min-height: 40px;
  width: 100% !important;
  display: flex !important;
  align-items: center !important;
}

.follow-up-card :deep(.el-input__inner) {
  text-align: center !important;
}

.follow-up-card :deep(.el-select) {
  width: 100% !important;
}

.follow-up-card :deep(.el-select .el-input__wrapper) {
  width: 100% !important;
  height: 40px !important;
  display: flex !important;
  align-items: center !important;
  box-sizing: border-box !important;
}

.follow-up-card :deep(.el-select .el-input__inner) {
  text-align: center !important;
  height: 40px !important;
  line-height: 40px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.follow-up-card :deep(.el-date-editor) {
  width: 100% !important;
}

.follow-up-card :deep(.el-date-editor .el-input__wrapper) {
  width: 100% !important;
  display: flex !important;
  align-items: center !important;
}

.follow-up-card :deep(.el-date-editor .el-input__inner) {
  text-align: center !important;
}

.follow-up-card :deep(.el-textarea__inner) {
  min-height: 120px;
}

.follow-up-header {
  margin-bottom: 16px;
}

.follow-up-header .extra_button {
  justify-content: flex-end;
}

.action-tabs {
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding-right: 10px !important;
  max-width: 190px;
  min-width: 190px;
}

.tab-button {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  color: white !important;
  font-weight: 500;
  font-size: 13px;
  min-height: 36px;
  width: 100%;
  background: white;
  border: 1px solid #e9ecef;
  margin-bottom: 4px;
  justify-content: center;
}

.tab-button span {
  color: white !important;
}

.tab-button .el-icon {
  color: white !important;
  margin-right: 5px;
  font-size: 14px;
}

.tab-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.tab-button-blue {
  background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
  color: white !important;
}

.tab-button-blue:hover {
  background: linear-gradient(135deg, #357abd 0%, #2e6ba8 100%);
  color: white !important;
}

.tab-button-green {
  background: linear-gradient(135deg, #7ed321 0%, #6bb91c 100%);
  color: white !important;
}

.tab-button-green:hover {
  background: linear-gradient(135deg, #6bb91c 0%, #5ca017 100%);
  color: white !important;
}

.tab-button-red {
  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
  color: white !important;
}

.tab-button-red:hover {
  background: linear-gradient(135deg, #c0392b 0%, #a93226 100%);
  color: white !important;
}

.tab-button-purple {
  background: linear-gradient(135deg, #9013fe 0%, #7b1fa2 100%);
  color: white !important;
}

.tab-button-purple:hover {
  background: linear-gradient(135deg, #7b1fa2 0%, #6a1b9a 100%);
  color: white !important;
}

/* 基本信息区域布局控制 - 每行显示5个字段 */
.basic-info-row {
  display: flex;
  flex-wrap: wrap;
}

.basic-info-col {
  flex: 0 0 20%; /* 每个字段占20%宽度，一行5个 */
  max-width: 20%;
  margin-bottom: 12px;
}

/* 响应式设计：在较小屏幕上调整布局 */
@media (max-width: 1200px) {
  .basic-info-col {
    flex: 0 0 25%; /* 小屏幕每行4个 */
    max-width: 25%;
  }
}

@media (max-width: 768px) {
  .basic-info-col {
    flex: 0 0 50%; /* 更小屏幕每行2个 */
    max-width: 50%;
  }
}

/* 职业信息区域样式 - 参考案件详情.html中的sub-info区域浅灰色背景设计 */
.occupation-info-section {
  background-color: #f1f3f5; /* 浅灰色背景，参考原HTML中sub-info区域 */
  padding: 8px 12px;
  margin-top: 16px;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.occupation-info-section .el-row {
  margin: 0 !important;
}

.occupation-info-section .el-col {
  padding: 0 !important;
}

.occupation-cell_item {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  padding: 2px 4px;
  min-height: 22px;
  background: transparent;
}

.occupation-cell_label {
  font-weight: 600;
  color: #2c3e50;
  white-space: nowrap;
  min-width: 80px;
  font-size: 10px; /* 职业部分文字大小 */
  margin-right: 6px;
}

.occupation-cell_value {
  color: #495057;
  font-size: 10px; /* 职业部分文字大小 */
  flex: 1;
  display: flex;
  align-items: center;
  word-break: break-all;
  background-color: transparent;
  border: none;
  border-radius: 4px;
  min-height: 18px;
}

.occupation-cell_value input {
  font-size: 10px; /* 职业部分文字大小 */
  background-color: #fff;
  border: 1px solid #e1e4e8;
  border-radius: 4px;
  padding: 2px 4px;
  height: 24px;
}

/* 职业信息区域输入框字体大小 */
.occupation-cell_value .el-input__inner {
  font-size: 10px !important;
}

/* 跟进记录单元格样式 - 一行显示，超出省略号 */
.collection-notes-cell {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: pointer;
  line-height: 1.4;
}

/* 截断文本样式 - 用于委托时月龄分档和账户号后七位 */
.truncated-text {
  display: inline-block;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #495057;
  font-size: 10px;
}

/* 跟进记录表格样式 */
.reminder-card {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0; /* 允许flex子项收缩 */
}

/* 优化卡片内容区域布局 */
.reminder-card :deep(.el-card__body) {
  display: flex;
  flex-direction: column;
  flex: 1;
  padding: 8px;
  min-height: 0; /* 允许收缩 */
}

/* 让表格容器占据主要空间 */
.reminder-card :deep(.el-table) {
  flex: 1;
  min-height: 0; /* 允许收缩 */
}

/* 让表格主体部分可滚动，最大化显示区域适应各种缩放比例 */
.reminder-card :deep(.el-table .el-table__body-wrapper) {
  overflow-y: auto;
  max-height: calc(100vh - 280px); /* 为压缩后的布局优化表格显示区域 */
  min-height: 350px; /* 确保显示足够多的数据行 */
}

/* 确保表头文字在一行显示不换行 */
.reminder-card :deep(.el-table .el-table__header-wrapper .el-table__header .el-table__cell) {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.reminder-card :deep(.el-table .el-table__header .el-table__cell .cell) {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 确保失联查询结果和是否标记换单字段内容完全展示 */
.reminder-card :deep(.el-table .el-table__body .el-table__row .el-table__cell) {
  padding: 6px 8px;
}

.reminder-card :deep(.el-table .el-table__body .el-table__row .el-table__cell .cell) {
  word-break: break-word;
  white-space: normal;
  line-height: 1.4;
}

/* 目录容器样式 */
.directory-container {
  height: calc(100vh - 84px);
  background-color: #233148;
  border-radius: 12px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 目录标题 */
.directory-header {
  background-color: #233148;
  padding: 16px 20px;
  display: flex;
  align-items: center;
  color: white;
  font-weight: bold;
  font-size: 16px;
}

.directory-icon {
  margin-right: 8px;
  font-size: 18px;
  color: white;
}

.directory-title {
  font-size: 16px;
  font-weight: bold;
  color: white;
}

/* 分割线 */
.directory-divider {
  height: 1px;
  background-color: rgba(255, 255, 255, 0.2);
  margin: 0 20px;
}

/* 表头样式 */
.directory-table-header {
  display: flex;
  padding: 12px 20px;
  background-color: rgba(255, 255, 255, 0.05);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.header-cell {
  color: white;
  font-size: 12px;
  font-weight: 500;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.header-cell.sortable {
  cursor: pointer;
  transition: all 0.2s ease;
}

.header-cell.sortable:hover {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.customer-header {
  flex: 1;
  justify-content: flex-start;
}

.amount-header {
  flex: 1;
  justify-content: center;
}

.days-header {
  flex: 0.8;
  justify-content: flex-end;
}

.sort-icon {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  transition: color 0.2s ease;
}

.header-cell.sortable:hover .sort-icon {
  color: rgba(255, 255, 255, 0.9);
}

/* 目录内容区域 */
.directory-content {
  flex: 1;
  overflow-y: auto;
  padding: 0;
}

/* 目录项 */
.directory-item {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
  background: transparent;
}

.directory-item:hover {
  background: rgba(255, 255, 255, 0.1);
}

.directory-item:last-child {
  border-bottom: none;
}

/* 数据行样式 */
.directory-row {
  display: flex;
  padding: 12px 20px;
  align-items: center;
}

.row-cell {
  font-size: 13px;
  font-weight: 500;
}

.customer-cell {
  flex: 1;
  color: #5dade2;
  text-align: left;
  cursor: pointer;
}

.customer-cell:hover {
  color: #85c1e9;
}

.amount-cell {
  flex: 1;
  color: white;
  text-align: center;
}

.days-cell {
  flex: 0.8;
  text-align: right;
}

.days {
  font-weight: 500;
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
}

/* 天数颜色样式 */
.days-red {
  background-color: transparent;
  color: #e74c3c;
  font-weight: 600;
}

.days-orange {
  background-color: transparent;
  color: #f39c12;
}

.days-yellow {
  background-color: transparent;
  color: #f1c40f;
}

.days-blue {
  background-color: transparent;
  color: #3498db;
}

/* 分页控件 - 紧凑布局 */
.directory-pagination {
  background-color: #233148;
  padding: 8px 16px; /* 减少padding */
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
}

.nav-btn {
  background: transparent;
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 4px 8px;
  border-radius: 3px;
  cursor: pointer;
  font-size: 11px;
  transition: all 0.3s ease;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
}

.nav-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.5);
}

.pagination-text {
  color: white;
  font-size: 11px; /* 减少字体大小 */
  flex: 1;
  text-align: center;
}

.pagination-info {
  display: flex;
  align-items: center;
}

.page-size-select {
  background-color: #233148;
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 3px;
  padding: 2px 6px; /* 减少padding */
  font-size: 11px; /* 减少字体大小 */
  height: 24px;
  width: 45px;
}

.flex-between {
  display: flex;
  justify-content: space-between;
}

.flex-center {
  display: flex;
  justify-content: center;
}

.flex-start {
  display: flex;
  align-items: center;
}

.extra_button {
  display: flex;
}

:deep .res-nova {
  --el-table-tr-bg-color: var(--el-color-success-light-9) !important;
}

:deep .el-descriptions__header {
  margin-bottom: 6px !important;
}

.cell_item {
  display: flex;
  margin-bottom: 6px;
  align-items: center;
}

.cell_label {
  min-width: 80px;
  padding-right: 10px;
  color: #2c3e50;
  font-weight: bold;
  font-size: 12px; /* 小两个字号 */
}

.cell_value {
  flex: 1;
  color: #2c3e50;
  font-size: 12px; /* 小两个字号 */
}

/* 基本信息区域输入框字体大小 */
.cell_value .el-input__inner {
  font-size: 12px !important;
}

.page_turning {
  margin-top: 16px;
  flex-shrink: 0;
}

/* 表格样式 */
:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table .el-table__header-wrapper) {
  border-radius: 8px 8px 0 0;
}

:deep(.el-table .el-table__header th) {
  background-color: #2c3e50 !important;
  color: white !important;
  font-weight: bold;
  border: none;
}

:deep(.el-table .el-table__header th:first-child) {
  border-top-left-radius: 8px;
}

:deep(.el-table .el-table__header th:last-child) {
  border-top-right-radius: 8px;
}

:deep(.el-table .el-table__body-wrapper) {
  border-radius: 0 0 8px 8px;
}

:deep(.el-table .el-table__body tr:last-child td:first-child) {
  border-bottom-left-radius: 8px;
}

:deep(.el-table .el-table__body tr:last-child td:last-child) {
  border-bottom-right-radius: 8px;
}

:deep(.el-table .el-table__border-left-patch) {
  background-color: #2c3e50;
}

:deep(.el-table .el-table__border-right-patch) {
  background-color: #2c3e50;
}

/* 分页居中 */
.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 8px; /* 减少上边距，让分页组件更贴近表格 */
  padding: 8px 0; /* 添加内边距保持视觉平衡 */
  flex-shrink: 0; /* 防止分页组件被压缩 */
  border-top: 1px solid #f0f0f0; /* 添加分割线 */
  background: #fafafa; /* 轻微的背景色区分 */
}

/* 删除按钮样式 */
.delete-button {
  background-color: #e74c3c !important;
  border-color: #e74c3c !important;
  color: white !important;
  border-radius: 4px !important;
  padding: 8px 12px !important;
  font-weight: 500 !important;
}

.delete-button:hover {
  background-color: #c0392b !important;
  border-color: #c0392b !important;
  color: white !important;
}

.delete-button:focus {
  background-color: #e74c3c !important;
  border-color: #e74c3c !important;
  color: white !important;
}

/* 被叫号码字段样式调整 */
.form-container :deep(.el-form-item) {
  margin-bottom: 12px; /* 减少表单项之间的间距 */
}

.form-container :deep(.el-form-item__label) {
  margin-bottom: 4px !important; /* 减少标签和输入框之间的距离 */
  padding: 0 !important;
  line-height: 1.2 !important;
}

.form-container :deep(.el-input__wrapper) {
  min-height: 28px !important; /* 减少输入框高度 */
  padding: 4px 8px !important; /* 调整内边距 */
}

.form-container :deep(.el-input__inner) {
  line-height: 1.2 !important; /* 调整行高 */
}
</style>
