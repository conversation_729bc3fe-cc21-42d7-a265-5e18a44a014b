package com.linhong.boot.system.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.linhong.boot.common.enums.RoleEnum;
import com.linhong.boot.common.result.Result;
import com.linhong.boot.system.model.entity.Notice;
import com.linhong.boot.system.model.entity.Role;
import com.linhong.boot.system.model.entity.User;
import com.linhong.boot.system.model.entity.UserRole;
import com.linhong.boot.system.service.NoticeService;
import com.linhong.boot.system.service.RoleService;
import com.linhong.boot.system.service.UserRoleService;
import com.linhong.boot.system.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 忘记密码控制器
 */
@Tag(name = "忘记密码接口")
@RestController
@RequestMapping("/api/v1/forget-password")
@RequiredArgsConstructor
public class ForgetPasswordController {

    private final UserService userService;
    private final RoleService roleService;
    private final UserRoleService userRoleService;
    private final NoticeService noticeService;

    @Operation(summary = "发送忘记密码请求")
    @PostMapping("/send-request")
    public Result<Void> sendForgetPasswordRequest(@RequestBody @Valid ForgetPasswordRequest request) {
        // 验证用户是否存在
        User user = userService.getOne(new LambdaQueryWrapper<User>()
                .eq(User::getUsername, request.getUsername())
                .eq(User::getIsDeleted, 0));
        
        if (user == null) {
            return Result.failed("用户不存在");
        }

        // 查询RSB角色
        Role rsbRole = roleService.getOne(new LambdaQueryWrapper<Role>()
                .eq(Role::getCode, RoleEnum.RSB.getValue())
                .eq(Role::getIsDeleted, 0));
        
        if (rsbRole == null) {
            return Result.failed("人事部门角色不存在");
        }

        // 查询RSB角色的用户
        List<UserRole> userRoles = userRoleService.list(new LambdaQueryWrapper<UserRole>()
                .eq(UserRole::getRoleId, rsbRole.getId()));
        
        if (userRoles.isEmpty()) {
            return Result.failed("人事部门暂无人员，请联系系统管理员");
        }

        // 获取RSB角色用户ID列表
        List<String> targetUserIds = userRoles.stream()
                .map(ur -> ur.getUserId().toString())
                .collect(Collectors.toList());

        // 创建通知实体，直接设置必要字段
        Notice notice = new Notice();
        notice.setTitle("用户 " + request.getUsername() + " 申请重置密码");
        notice.setContent("用户 " + request.getUsername() + " 在登录页面申请重置密码，请及时处理。\n\n" +
                "处理方式：\n" +
                "1. 在系统管理-用户管理中找到该用户\n" +
                "2. 点击重置密码按钮\n" +
                "3. 设置新密码并通知用户\n\n" +
                "用户名：" + request.getUsername());
        notice.setType(1); // 通知类型
        notice.setLevel("H"); // 高优先级
        notice.setTargetType(2); // 指定用户
        notice.setTargetUserIds(String.join(",", targetUserIds));
        notice.setPublishStatus(0); // 未发布状态
        notice.setCreateBy(user.getId()); // 设置创建人为申请用户

        // 保存通知
        boolean saveResult = noticeService.save(notice);
        if (saveResult) {
            // 立即发布通知，确保RSB角色用户能收到
            boolean publishResult = noticeService.publishNotice(notice.getId());
            if (publishResult) {
                return Result.success();
            } else {
                return Result.failed("通知发布失败");
            }
        }

        return Result.failed("发送请求失败");
    }

    /**
     * 忘记密码请求
     */
    public static class ForgetPasswordRequest {
        private String username;

        public String getUsername() {
            return username;
        }

        public void setUsername(String username) {
            this.username = username;
        }
    }
}
