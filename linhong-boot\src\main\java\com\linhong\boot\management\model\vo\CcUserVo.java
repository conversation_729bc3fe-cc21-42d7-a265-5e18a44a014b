package com.linhong.boot.management.model.vo;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.linhong.boot.common.enums.CaseEnum;
import com.linhong.boot.management.model.entity.CcUser;
import com.linhong.boot.management.model.entity.EmergencyContacts;
import com.linhong.boot.management.model.entity.FollowUpRecords;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class CcUserVo extends CcUser{
	
	/**
	 * 紧急联系人
	 */
	@Schema(description = "紧急联系人集")
	private List<EmergencyContacts> contacts;
	
	/**
	 * 跟进记录
	 */
	@Schema(description = "跟进记录集")
	private List<FollowUpRecords> records;
	
	/**
	 * 查询条数总委托金额
	 */ 
	private String sumEntrustTotalAmount;
	
	/**
	 * 查询条数总委托本金
	 */
	private String sumentrustPrincipalTotal; 


	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.id
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	private Long id;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.customer_name
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	private String customerName;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.cardholder_id_number
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	private String cardholderIdNumber;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.cardholder_code
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	private String cardholderCode;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.customer_index_number
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	private String customerIndexNumber;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.entrust_amount
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	private String entrustAmount;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.entrust_total_amount
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	private BigDecimal entrustTotalAmount;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.entrust_principal
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	private String entrustPrincipal;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.entrust_principal_total
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	private BigDecimal entrustPrincipalTotal;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.entrust_overdue_period
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	private String entrustOverduePeriod;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.target_period
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	private String targetPeriod;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.case_city
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	private String caseCity;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.last_follow_up_date
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	@JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
	private Date lastFollowUpDate;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.entrust_start_date
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	@JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
	private Date entrustStartDate;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.entrust_end_date
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	@JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
	private Date entrustEndDate;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.case_type
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	private String caseType;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.new_old_case_flag
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	private String newOldCaseFlag;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.batch_number
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	private String batchNumber;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.balance_ops
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	private BigDecimal balanceOps;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.principal_ops
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	private BigDecimal principalOps;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.current_overdue_period
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	private String currentOverduePeriod;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.household_city
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	private String householdCity;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.customer_age
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	private Integer customerAge;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.occupation
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	private String occupation;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.card_opening_date
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	@JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
	private Date cardOpeningDate;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.credit_limit
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	private BigDecimal creditLimit;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.last_payment_date
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	@JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
	private Date lastPaymentDate;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.skill_group
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	private String skillGroup;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.account_number_last7
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	private String accountNumberLast7;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.entrust_age_bracket
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	private String entrustAgeBracket;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.entrust_amount_bracket
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	private String entrustAmountBracket;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.entrust_commission_bracket
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	private String entrustCommissionBracket;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.rating_bracket
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	private String ratingBracket;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.is_litigation
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	private String isLitigation;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.customer_current_month_repayment
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	private BigDecimal customerCurrentMonthRepayment;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.customer_previous_day_repayment
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	private BigDecimal customerPreviousDayRepayment;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.case_grabbing_user
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	private String caseGrabbingUser;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.special_project_type
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	private String specialProjectType;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.retention_count
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	private Integer retentionCount;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.personalized_installment_status
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	private String personalizedInstallmentStatus;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.personalized_installment_fulfillment_status
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	private String personalizedInstallmentFulfillmentStatus;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.complaint_label
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	private String complaintLabel;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.personalized_fulfillment_label
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	private String personalizedFulfillmentLabel;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.litigation_label
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	private String litigationLabel;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.smart_voice_label
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	private String smartVoiceLabel;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.special_project_label
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	private String specialProjectLabel;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.is_litigation_for_commission
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	private String isLitigationForCommission;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.case_agency
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	private String caseAgency;
	/**
	 * 0:新案
	 * 1:继续跟进
	 * 2:重点跟进
	 * 3:谈方案
	 * 4:线下分期
	 * 5:个性化分期
	 * 6:待减免
	 * 7:要求退案
	 * 8:保留案件
	 * 9:投诉倾向
	 * 10:案池
	 * 
	 * 99:已退案
	 */
	private String followUpStatus;
	/* 催收人/组织 */
	private String userOrDept;
	/*外包序号*/
	private String outsourceSerialNumber;
	/*是否标记换单*/
	private String isMarkChange;
	/*失联查询结果*/
	private String lostContactQueryResult;
	/*未跟进天数*/
	private int unfollowedDays;
	/*工作单位*/
	private String workUnit;
	/*银行卡号*/
	private String bankCardNumber;
	/*是否锁定 0:否 1:是  默认否*/
	private String isLock;
	private String deptName;
	@TableField(exist = false)
	private String huanTime;
	@TableField(exist = false)
	private String jinNum;
	@TableField(exist = false)
	private String chuNum;
	@TableField(exist=false)
	private String assType;
	/**
	 * 0:新案
	 * 1:继续跟进
	 * 2:重点跟进
	 * 3:谈方案
	 * 4:线下分期
	 * 5:个性化分期
	 * 6:待减免
	 * 7:要求退案
	 * 8:保留案件
	 * 9:投诉倾向
	 * 10:案池
	 */
	public String getFollowUpStatus() {
		if(followUpStatus!=null) {
			return CaseEnum.fromValue(Integer.valueOf(followUpStatus));
		}
		return followUpStatus;
	}
	

}
