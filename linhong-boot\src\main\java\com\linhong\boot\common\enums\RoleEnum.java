package com.linhong.boot.common.enums;

import lombok.Getter;

/**
 * 状态枚举
 */
@Getter
public enum RoleEnum {

	/**
	 * 总负责人
	 */
	ADMIN("ADMIN", "总负责人"),
	/**
	 * 超级管理员
	 */
	ROOT("ROOT", "超级管理员"),
	/**
	 * 区域负责人
	 */
	QYZFZR("QYZFZR", "区域负责人"),
	/**
	 * 分公司总负责人
	 */
	FGSZFZR("FGSZFZR", "分公司总负责人"),
	/**
	 * 地州市级职场
	 */
	DZSJFZR("DZSJFZR", "地州市级职场"),
	/**
	 * 主管
	 */
	BMFZR("BMFZR", "主管"),
	/**
	 * 组长
	 */
	XZFZR("XZFZR", "组长"),
	/**
	 * 员工
	 */
	YG("YG", "员工"),
	/**
	 * 县级职场/战区经理
	 */
	XJFZR("XJFZR", "县级职场/战区经理"),
	/**
	 * 人事部
	 */
	RSB("RSB", "人事部");

    private final String value;


    private final String label;

    RoleEnum(String value, String label) {
        this.value = value;
        this.label = label;
    }
    
    /**
     * 根据value获取label
     * @param value
     * @return status.getLabel
     */
    public static String fromValue(String value) {
        for (RoleEnum status : RoleEnum.values()) {
            if (status.getValue().equals(value) ) {
                return status.getLabel();
            }
        }
        throw new IllegalArgumentException("RoleEnum未知参数: " + value);
    }

    /**
     * 根据label获取value
     * @param label
     * @return
     */
    public static String fromLabel(String label) {
        for (RoleEnum status : RoleEnum.values()) {
            if (status.getLabel().equals(label)) {
                return status.getValue();
            }
        }
        throw new IllegalArgumentException("RoleEnum未知参数: " + label);
    }

    
}
