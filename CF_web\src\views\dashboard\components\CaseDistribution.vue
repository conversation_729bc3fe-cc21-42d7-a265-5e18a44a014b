<template>
  <div class="case-distribution-card">
    <div class="card-header">
      <div class="header-left">
        <div class="case-icon">📊</div>
        <span class="header-title">案件分布</span>
      </div>
      <div class="header-right">
        <div class="period-buttons">
          <button
            class="period-btn"
            :class="{ active: selectedType === 'department' }"
            @click="changeType('department')"
          >
            部门
          </button>
          <button
            class="period-btn"
            :class="{ active: selectedType === 'personal' }"
            @click="changeType('personal')"
          >
            个人
          </button>
        </div>
      </div>
    </div>

    <div class="table-content">
      <el-skeleton :loading="loading" :rows="4" animated>
        <template #template>
          <div v-for="i in 4" :key="i" class="table-row skeleton">
            <el-skeleton-item variant="text" style="width: 80px;" />
            <el-skeleton-item variant="text" style="width: 60px;" />
            <el-skeleton-item variant="text" style="width: 100px;" />
            <div class="progress-skeleton">
              <el-skeleton-item variant="rect" style="width: 100%; height: 8px; border-radius: 4px;" />
            </div>
          </div>
        </template>

        <template #default>
          <div class="table-header">
            <div class="header-cell">团队/个人</div>
            <div class="header-cell">在案户数</div>
            <div class="header-cell">委托金额</div>
            <div class="header-cell">完成进度</div>
          </div>

          <div class="table-body">
            <div v-for="(item, index) in distributionData" :key="index" class="table-row">
              <div class="cell team-name">{{ item.category }}</div>
              <div class="cell case-count">{{ item.value }}</div>
              <div class="cell amount">¥{{ formatAmount(item.amount || 0) }}</div>
              <div class="cell progress-cell">
                <div class="progress-container">
                  <div class="progress-bar">
                    <div
                      class="progress-fill"
                      :style="{
                        width: item.progress + '%',
                        backgroundColor: item.color
                      }"
                    ></div>
                  </div>
                  <div class="progress-text">{{ item.progress }}%</div>
                </div>
              </div>
            </div>
          </div>
        </template>
      </el-skeleton>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";

interface CaseDistributionItem {
  category: string;
  value: number;
  amount?: number;
  progress?: number;
  color: string;
}

interface Props {
  distributionData: CaseDistributionItem[];
  loading?: boolean;
}

const props = defineProps<Props>();
const emit = defineEmits<{
  typeChange: [type: 'department' | 'personal'];
}>();

const selectedType = ref<'department' | 'personal'>('department');

const changeType = (newType: 'department' | 'personal') => {
  selectedType.value = newType;
  emit('typeChange', newType);
};

const formatAmount = (amount: number) => {
  if (amount >= 10000) {
    return (amount / 10000).toFixed(1) + '万';
  }
  return amount.toLocaleString();
};

</script>

<style lang="scss" scoped>
.case-distribution-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(15px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  padding: 20px;
  height: 40vh;
  display: flex;
  flex-direction: column;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-shrink: 0;

  .header-left {
    display: flex;
    align-items: center;
    gap: 8px;

    .case-icon {
      font-size: 18px;
    }

    .header-title {
      font-size: 16px;
      font-weight: 600;
      color: rgba(255, 255, 255, 0.95);
    }
  }

  .header-right {
    .period-buttons {
      display: flex;
      gap: 0;

      .period-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 8px 16px;
        border: none;
        background: rgba(255, 255, 255, 0.15);
        color: rgba(255, 255, 255, 0.8);
        font-size: 13px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
        border-radius: 6px;
        margin: 0 2px;

        &:hover {
          background: rgba(255, 255, 255, 0.2);
          color: rgba(255, 255, 255, 0.95);
        }

        &.active {
          padding: 10px 20px;
          font-size: 14px;
          font-weight: 600;
          color: white;
          border-radius: 8px;
          margin: 0;
          background: #fbbf24;
        }
      }
    }
  }
}

.table-content {
  flex: 1;
  overflow-y: auto;
  min-height: 0;

  .table-header {
    display: grid;
    grid-template-columns: 1fr 80px 120px 1fr;
    gap: 16px;
    padding: 12px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    margin-bottom: 16px;

    .header-cell {
      font-size: 14px;
      font-weight: 600;
      color: rgba(255, 255, 255, 0.9);
      text-align: left;

      &:last-child {
        text-align: center;
      }
    }
  }

  .table-body {
    .table-row {
      display: grid;
      grid-template-columns: 1fr 80px 120px 1fr;
      gap: 16px;
      padding: 16px 0;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      align-items: center;

      &:last-child {
        border-bottom: none;
      }

      &.skeleton {
        .progress-skeleton {
          display: flex;
          align-items: center;
          gap: 12px;
        }
      }

      .cell {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.95);

        &.team-name {
          font-weight: 600;
          color: #fbbf24;
        }

        &.case-count {
          font-weight: 600;
          text-align: center;
        }

        &.amount {
          font-weight: 600;
          text-align: right;
        }

        &.progress-cell {
          .progress-container {
            display: flex;
            align-items: center;
            gap: 12px;

            .progress-bar {
              flex: 1;
              height: 8px;
              background: rgba(255, 255, 255, 0.2);
              border-radius: 4px;
              overflow: hidden;

              .progress-fill {
                height: 100%;
                border-radius: 4px;
                transition: width 0.3s ease;
              }
            }

            .progress-text {
              font-size: 12px;
              font-weight: 600;
              color: rgba(255, 255, 255, 0.9);
              min-width: 35px;
              text-align: right;
            }
          }
        }
      }
    }
  }

  /* 自定义滚动条样式 */
  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;

    &:hover {
      background: rgba(255, 255, 255, 0.5);
    }
  }
}
</style>
