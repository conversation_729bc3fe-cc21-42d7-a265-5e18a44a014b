package com.linhong.boot.shared.codegen.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.linhong.boot.common.base.BaseEntity;

import lombok.Getter;
import lombok.Setter;

/**
 * 代码生成基础配置
 *
 * <AUTHOR>
 * @since 2.10.0
 */
@TableName(value = "gen_config")
@Getter
@Setter
public class GenConfig extends BaseEntity {

    /**
     * 表名
     */
    private String tableName;

    /**
     * 包名
     */
    private String packageName;

    /**
     * 模块名
     */
    private String moduleName;

    /**
     * 实体类名
     */
    private String entityName;

    /**
     * 业务名
     */
    private String businessName;

    /**
     * 父菜单ID
     */
    private Long parentMenuId;

    /**
     * 作者
     */
    private String author;
}