<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.linhong.boot.system.mapper.DictDataMapper">

    <!-- 获取字典数据分页列表 -->
    <select id="getDictDataPage" resultType="com.linhong.boot.system.model.vo.DictDataPageVO">
        SELECT
            id,
            dict_code,
            label,
            value,
            sort,
            status
        FROM
            sys_dict_data
        <where>
            <if test="queryParams.keywords!=null and queryParams.keywords.trim() neq ''">
               AND (
                value LIKE CONCAT('%', #{queryParams.keywords} ,'%')
                OR
                label LIKE CONCAT('%', #{queryParams.keywords} ,'%')
                )
            </if>
            <if test="queryParams.dictCode!=null and queryParams.dictCode.trim() neq ''">
                AND dict_code = #{queryParams.dictCode}
            </if>
        </where>
    </select>

    <!-- 根据字典编码获取字典数据列表 -->
    <select id="getDictDataList" resultType="com.linhong.boot.system.model.vo.DictVO$DictData">
        SELECT
            value,
            label,
            tag_type
        FROM
            sys_dict_data
        WHERE
            status = 1 AND dict_code = #{dictCode}
        ORDER BY
            sort ASC
    </select>

</mapper>
