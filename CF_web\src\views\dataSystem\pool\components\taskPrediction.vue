<template>
  <!-- 任务预测 -->
  <div>
    <el-dialog v-model="props.show" title="任务预测" width="1000" align-center @close="handleCloseDialog">
      <el-row :gutter="24">
        <el-col :span="24">
          <div>
            <el-table ref="dataTableRef" v-loading="loading" :data="list" highlight-current-row border
              style="width: 100%">
              <el-table-column v-for="item in allocationMune" :key="item.prop" :label="item.label" :prop="item.prop"
                :width="item.width || ''" min-width="100" show-overflow-tooltip />
              <el-table-column label="操作">
                <template #default="scope">
                  <el-button type="primary" size="small" link icon="delete" @click="deleteConnection(scope.row.id)">
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            <pagination v-if="total > 0"
              @change="handleQuery"  
              v-model:total="total" v-model:page="queryParams.pageNum"
              v-model:limit="queryParams.pageSize" :page-sizes="[10, 20, 50, 100, 200, 500]"
              @pagination="handleQuery" />
          </div>
        </el-col>
      </el-row>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="foundTaskShow = !foundTaskShow">新建任务预测</el-button>
          <el-button @click="handleCloseDialog">关闭</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog v-model="foundTaskShow" title="创建任务预测" width="500" align-center @close="foundTaskCloseDialog">
      <el-row :gutter="24">
        <el-col :span="24">
          <eleForm ref="eleFormRef" :formItemList="contactPersonList" :formBox="formBox" :operateOpen="false"
            @handleSubmit="formHandleSubmit" />
        </el-col>
      </el-row>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="formSubmit">创建</el-button>
          <el-button @click="foundTaskCloseDialog">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ElMessage, ElMessageBox, ElButton, ElLoading } from "element-plus";
import dataSystemAPI from "@/api/dataSystem/pool";
import eleForm from "@/components/EleComponents/ele-form.vue";

const props = defineProps({
  show: {
    type: Boolean,
    required: true,
  },
  // ids
  ids: {
    type: Number,
    required: 0,
  },
});
watch(
  () => props.show,
  (val) => {
    if (val) handleQuery();
  }
);
const emit = defineEmits(["update:show", "refresh"]);
// 创建任务预测
const foundTaskShow = ref(false);

const formBox = ref({
  spanItem: 24, // 每项占比
  formContent: 24, // 表单父级占比
  operate: 4, // 操作父容器占比
  operateItem: 12,
  operateGrid: " start",
});
const eleFormRef: any = ref(null);
const contactPersonList = ref([
  {
    label: "次月还款金额",
    type: "input",
    formType: "number",
    field: "nextMonthRepayment",
    placeholder: "请输入",
    labelWidth: "110px",
    rules: [{ required: true, message: "请输入次月还款金额", trigger: "blur" }],
  },
  {
    label: "预计还款时间",
    type: "date-picker",
    options: [],
    field: "expectedRepaymentDate",
    placeholder: "请选择",
    formType: "date",
    labelWidth: "110px",
    rules: [{ required: true, message: "请选择预计还款时间", trigger: "blur" }],
  },
  {
    label: "备注",
    type: "input",
    formType: "textarea",
    field: "remarks",
    placeholder: "请输入",
    labelWidth: "110px",
    rules: "",
  },
]);
// 关闭弹窗 重置表格
const foundTaskCloseDialog = () => {
  foundTaskShow.value = false;
  eleFormRef.value.resetFields();
};
const formSubmit = () => {
  eleFormRef.value.handleSubmit();
};
// 创建
const formHandleSubmit = (data: any) => {
  loading.value = true;
  dataSystemAPI
    .taskRepaySave({
      ...data,
      ccId: props.ids,
    })
    .then((e: any) => {
      ElMessage({
        message: "新增成功",
        type: "success",
      });
      loading.value = false;
      foundTaskCloseDialog();
      handleQuery();
    });
};

//催记表头数据
const allocationMune = ref([
  { label: "序号", prop: "id" },
  { label: "客户姓名", prop: "customerName" },
  { label: "客户索引号", prop: "customerIndexNumber", width: "180px" },
  { label: "催收人/组织", prop: "userOrDept" },

  { label: "次月还款金额", prop: "nextMonthRepayment" },
  { label: "预计还款时间", prop: "expectedRepaymentDate" },
  { label: "备注", prop: "remarks" },
]);

const loading = ref(false);
const list = ref([]);
const total: any = ref(0);
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
});
/**
 * 获取任务预测
 */
const handleQuery = () => {
  loading.value = true;
  dataSystemAPI
    .taskRepayPage({
      ...queryParams.value,
      ccId: props.ids,
    })
    .then((data: any) => {
      console.log(data);
      loading.value = false;
      list.value = data.list;
      total.value = data.total;
    });
};
// 删除任务预测
const deleteConnection = (id: number) => {
  ElMessageBox.confirm("确认删除任务预测?", "提示", {
    confirmButtonText: "确认",
    cancelButtonText: "取消",
    type: "warning",
    draggable: true,
  }).then(() => {
    loading.value = true;
    dataSystemAPI
      .taskRepayDelete({
        ccId: props.ids,
        id,
      })
      .then((e) => {
        ElMessage({
          message: "删除成功",
          type: "success",
        });
        loading.value = false;
        handleQuery();
      });
  });
};

//关闭弹窗
const handleCloseDialog = () => {
  console.log("关闭");
  emit("update:show", false);
};
</script>

<style lang="scss" scoped></style>
