package com.linhong.boot.management.model.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

public class TaskRepaymentPredictionsExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table task_repayment_predictions
     *
     * @mbg.generated Tue Dec 17 01:34:51 CST 2024
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table task_repayment_predictions
     *
     * @mbg.generated Tue Dec 17 01:34:51 CST 2024
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table task_repayment_predictions
     *
     * @mbg.generated Tue Dec 17 01:34:51 CST 2024
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table task_repayment_predictions
     *
     * @mbg.generated Tue Dec 17 01:34:51 CST 2024
     */
    public TaskRepaymentPredictionsExample() {
        oredCriteria = new ArrayList<>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table task_repayment_predictions
     *
     * @mbg.generated Tue Dec 17 01:34:51 CST 2024
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table task_repayment_predictions
     *
     * @mbg.generated Tue Dec 17 01:34:51 CST 2024
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table task_repayment_predictions
     *
     * @mbg.generated Tue Dec 17 01:34:51 CST 2024
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table task_repayment_predictions
     *
     * @mbg.generated Tue Dec 17 01:34:51 CST 2024
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table task_repayment_predictions
     *
     * @mbg.generated Tue Dec 17 01:34:51 CST 2024
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table task_repayment_predictions
     *
     * @mbg.generated Tue Dec 17 01:34:51 CST 2024
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table task_repayment_predictions
     *
     * @mbg.generated Tue Dec 17 01:34:51 CST 2024
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table task_repayment_predictions
     *
     * @mbg.generated Tue Dec 17 01:34:51 CST 2024
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table task_repayment_predictions
     *
     * @mbg.generated Tue Dec 17 01:34:51 CST 2024
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table task_repayment_predictions
     *
     * @mbg.generated Tue Dec 17 01:34:51 CST 2024
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table task_repayment_predictions
     *
     * @mbg.generated Tue Dec 17 01:34:51 CST 2024
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        protected void addCriterionForJDBCDate(String condition, Date value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value.getTime()), property);
        }

        protected void addCriterionForJDBCDate(String condition, List<Date> values, String property) {
            if (values == null || values.size() == 0) {
                throw new RuntimeException("Value list for " + property + " cannot be null or empty");
            }
            List<java.sql.Date> dateList = new ArrayList<>();
            Iterator<Date> iter = values.iterator();
            while (iter.hasNext()) {
                dateList.add(new java.sql.Date(iter.next().getTime()));
            }
            addCriterion(condition, dateList, property);
        }

        protected void addCriterionForJDBCDate(String condition, Date value1, Date value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value1.getTime()), new java.sql.Date(value2.getTime()), property);
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andCustomerNameIsNull() {
            addCriterion("customer_name is null");
            return (Criteria) this;
        }

        public Criteria andCustomerNameIsNotNull() {
            addCriterion("customer_name is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerNameEqualTo(String value) {
            addCriterion("customer_name =", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotEqualTo(String value) {
            addCriterion("customer_name <>", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameGreaterThan(String value) {
            addCriterion("customer_name >", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameGreaterThanOrEqualTo(String value) {
            addCriterion("customer_name >=", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameLessThan(String value) {
            addCriterion("customer_name <", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameLessThanOrEqualTo(String value) {
            addCriterion("customer_name <=", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameLike(String value) {
            addCriterion("customer_name like", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotLike(String value) {
            addCriterion("customer_name not like", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameIn(List<String> values) {
            addCriterion("customer_name in", values, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotIn(List<String> values) {
            addCriterion("customer_name not in", values, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameBetween(String value1, String value2) {
            addCriterion("customer_name between", value1, value2, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotBetween(String value1, String value2) {
            addCriterion("customer_name not between", value1, value2, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerIndexIsNull() {
            addCriterion("customer_index is null");
            return (Criteria) this;
        }

        public Criteria andCustomerIndexIsNotNull() {
            addCriterion("customer_index is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerIndexEqualTo(String value) {
            addCriterion("customer_index =", value, "customerIndex");
            return (Criteria) this;
        }

        public Criteria andCustomerIndexNotEqualTo(String value) {
            addCriterion("customer_index <>", value, "customerIndex");
            return (Criteria) this;
        }

        public Criteria andCustomerIndexGreaterThan(String value) {
            addCriterion("customer_index >", value, "customerIndex");
            return (Criteria) this;
        }

        public Criteria andCustomerIndexGreaterThanOrEqualTo(String value) {
            addCriterion("customer_index >=", value, "customerIndex");
            return (Criteria) this;
        }

        public Criteria andCustomerIndexLessThan(String value) {
            addCriterion("customer_index <", value, "customerIndex");
            return (Criteria) this;
        }

        public Criteria andCustomerIndexLessThanOrEqualTo(String value) {
            addCriterion("customer_index <=", value, "customerIndex");
            return (Criteria) this;
        }

        public Criteria andCustomerIndexLike(String value) {
            addCriterion("customer_index like", value, "customerIndex");
            return (Criteria) this;
        }

        public Criteria andCustomerIndexNotLike(String value) {
            addCriterion("customer_index not like", value, "customerIndex");
            return (Criteria) this;
        }

        public Criteria andCustomerIndexIn(List<String> values) {
            addCriterion("customer_index in", values, "customerIndex");
            return (Criteria) this;
        }

        public Criteria andCustomerIndexNotIn(List<String> values) {
            addCriterion("customer_index not in", values, "customerIndex");
            return (Criteria) this;
        }

        public Criteria andCustomerIndexBetween(String value1, String value2) {
            addCriterion("customer_index between", value1, value2, "customerIndex");
            return (Criteria) this;
        }

        public Criteria andCustomerIndexNotBetween(String value1, String value2) {
            addCriterion("customer_index not between", value1, value2, "customerIndex");
            return (Criteria) this;
        }

        public Criteria andOperatorIdIsNull() {
            addCriterion("operator_id is null");
            return (Criteria) this;
        }

        public Criteria andOperatorIdIsNotNull() {
            addCriterion("operator_id is not null");
            return (Criteria) this;
        }

        public Criteria andOperatorIdEqualTo(String value) {
            addCriterion("operator_id =", value, "operatorId");
            return (Criteria) this;
        }

        public Criteria andOperatorIdNotEqualTo(String value) {
            addCriterion("operator_id <>", value, "operatorId");
            return (Criteria) this;
        }

        public Criteria andOperatorIdGreaterThan(String value) {
            addCriterion("operator_id >", value, "operatorId");
            return (Criteria) this;
        }

        public Criteria andOperatorIdGreaterThanOrEqualTo(String value) {
            addCriterion("operator_id >=", value, "operatorId");
            return (Criteria) this;
        }

        public Criteria andOperatorIdLessThan(String value) {
            addCriterion("operator_id <", value, "operatorId");
            return (Criteria) this;
        }

        public Criteria andOperatorIdLessThanOrEqualTo(String value) {
            addCriterion("operator_id <=", value, "operatorId");
            return (Criteria) this;
        }

        public Criteria andOperatorIdLike(String value) {
            addCriterion("operator_id like", value, "operatorId");
            return (Criteria) this;
        }

        public Criteria andOperatorIdNotLike(String value) {
            addCriterion("operator_id not like", value, "operatorId");
            return (Criteria) this;
        }

        public Criteria andOperatorIdIn(List<String> values) {
            addCriterion("operator_id in", values, "operatorId");
            return (Criteria) this;
        }

        public Criteria andOperatorIdNotIn(List<String> values) {
            addCriterion("operator_id not in", values, "operatorId");
            return (Criteria) this;
        }

        public Criteria andOperatorIdBetween(String value1, String value2) {
            addCriterion("operator_id between", value1, value2, "operatorId");
            return (Criteria) this;
        }

        public Criteria andOperatorIdNotBetween(String value1, String value2) {
            addCriterion("operator_id not between", value1, value2, "operatorId");
            return (Criteria) this;
        }

        public Criteria andOperatorNameIsNull() {
            addCriterion("operator_name is null");
            return (Criteria) this;
        }

        public Criteria andOperatorNameIsNotNull() {
            addCriterion("operator_name is not null");
            return (Criteria) this;
        }

        public Criteria andOperatorNameEqualTo(String value) {
            addCriterion("operator_name =", value, "operatorName");
            return (Criteria) this;
        }

        public Criteria andOperatorNameNotEqualTo(String value) {
            addCriterion("operator_name <>", value, "operatorName");
            return (Criteria) this;
        }

        public Criteria andOperatorNameGreaterThan(String value) {
            addCriterion("operator_name >", value, "operatorName");
            return (Criteria) this;
        }

        public Criteria andOperatorNameGreaterThanOrEqualTo(String value) {
            addCriterion("operator_name >=", value, "operatorName");
            return (Criteria) this;
        }

        public Criteria andOperatorNameLessThan(String value) {
            addCriterion("operator_name <", value, "operatorName");
            return (Criteria) this;
        }

        public Criteria andOperatorNameLessThanOrEqualTo(String value) {
            addCriterion("operator_name <=", value, "operatorName");
            return (Criteria) this;
        }

        public Criteria andOperatorNameLike(String value) {
            addCriterion("operator_name like", value, "operatorName");
            return (Criteria) this;
        }

        public Criteria andOperatorNameNotLike(String value) {
            addCriterion("operator_name not like", value, "operatorName");
            return (Criteria) this;
        }

        public Criteria andOperatorNameIn(List<String> values) {
            addCriterion("operator_name in", values, "operatorName");
            return (Criteria) this;
        }

        public Criteria andOperatorNameNotIn(List<String> values) {
            addCriterion("operator_name not in", values, "operatorName");
            return (Criteria) this;
        }

        public Criteria andOperatorNameBetween(String value1, String value2) {
            addCriterion("operator_name between", value1, value2, "operatorName");
            return (Criteria) this;
        }

        public Criteria andOperatorNameNotBetween(String value1, String value2) {
            addCriterion("operator_name not between", value1, value2, "operatorName");
            return (Criteria) this;
        }

        public Criteria andDeptIdIsNull() {
            addCriterion("dept_id is null");
            return (Criteria) this;
        }

        public Criteria andDeptIdIsNotNull() {
            addCriterion("dept_id is not null");
            return (Criteria) this;
        }

        public Criteria andDeptIdEqualTo(String value) {
            addCriterion("dept_id =", value, "deptId");
            return (Criteria) this;
        }

        public Criteria andDeptIdNotEqualTo(String value) {
            addCriterion("dept_id <>", value, "deptId");
            return (Criteria) this;
        }

        public Criteria andDeptIdGreaterThan(String value) {
            addCriterion("dept_id >", value, "deptId");
            return (Criteria) this;
        }

        public Criteria andDeptIdGreaterThanOrEqualTo(String value) {
            addCriterion("dept_id >=", value, "deptId");
            return (Criteria) this;
        }

        public Criteria andDeptIdLessThan(String value) {
            addCriterion("dept_id <", value, "deptId");
            return (Criteria) this;
        }

        public Criteria andDeptIdLessThanOrEqualTo(String value) {
            addCriterion("dept_id <=", value, "deptId");
            return (Criteria) this;
        }

        public Criteria andDeptIdLike(String value) {
            addCriterion("dept_id like", value, "deptId");
            return (Criteria) this;
        }

        public Criteria andDeptIdNotLike(String value) {
            addCriterion("dept_id not like", value, "deptId");
            return (Criteria) this;
        }

        public Criteria andDeptIdIn(List<String> values) {
            addCriterion("dept_id in", values, "deptId");
            return (Criteria) this;
        }

        public Criteria andDeptIdNotIn(List<String> values) {
            addCriterion("dept_id not in", values, "deptId");
            return (Criteria) this;
        }

        public Criteria andDeptIdBetween(String value1, String value2) {
            addCriterion("dept_id between", value1, value2, "deptId");
            return (Criteria) this;
        }

        public Criteria andDeptIdNotBetween(String value1, String value2) {
            addCriterion("dept_id not between", value1, value2, "deptId");
            return (Criteria) this;
        }

        public Criteria andDeptNameIsNull() {
            addCriterion("dept_name is null");
            return (Criteria) this;
        }

        public Criteria andDeptNameIsNotNull() {
            addCriterion("dept_name is not null");
            return (Criteria) this;
        }

        public Criteria andDeptNameEqualTo(String value) {
            addCriterion("dept_name =", value, "deptName");
            return (Criteria) this;
        }

        public Criteria andDeptNameNotEqualTo(String value) {
            addCriterion("dept_name <>", value, "deptName");
            return (Criteria) this;
        }

        public Criteria andDeptNameGreaterThan(String value) {
            addCriterion("dept_name >", value, "deptName");
            return (Criteria) this;
        }

        public Criteria andDeptNameGreaterThanOrEqualTo(String value) {
            addCriterion("dept_name >=", value, "deptName");
            return (Criteria) this;
        }

        public Criteria andDeptNameLessThan(String value) {
            addCriterion("dept_name <", value, "deptName");
            return (Criteria) this;
        }

        public Criteria andDeptNameLessThanOrEqualTo(String value) {
            addCriterion("dept_name <=", value, "deptName");
            return (Criteria) this;
        }

        public Criteria andDeptNameLike(String value) {
            addCriterion("dept_name like", value, "deptName");
            return (Criteria) this;
        }

        public Criteria andDeptNameNotLike(String value) {
            addCriterion("dept_name not like", value, "deptName");
            return (Criteria) this;
        }

        public Criteria andDeptNameIn(List<String> values) {
            addCriterion("dept_name in", values, "deptName");
            return (Criteria) this;
        }

        public Criteria andDeptNameNotIn(List<String> values) {
            addCriterion("dept_name not in", values, "deptName");
            return (Criteria) this;
        }

        public Criteria andDeptNameBetween(String value1, String value2) {
            addCriterion("dept_name between", value1, value2, "deptName");
            return (Criteria) this;
        }

        public Criteria andDeptNameNotBetween(String value1, String value2) {
            addCriterion("dept_name not between", value1, value2, "deptName");
            return (Criteria) this;
        }

        public Criteria andNextMonthRepaymentIsNull() {
            addCriterion("next_month_repayment is null");
            return (Criteria) this;
        }

        public Criteria andNextMonthRepaymentIsNotNull() {
            addCriterion("next_month_repayment is not null");
            return (Criteria) this;
        }

        public Criteria andNextMonthRepaymentEqualTo(Long value) {
            addCriterion("next_month_repayment =", value, "nextMonthRepayment");
            return (Criteria) this;
        }

        public Criteria andNextMonthRepaymentNotEqualTo(Long value) {
            addCriterion("next_month_repayment <>", value, "nextMonthRepayment");
            return (Criteria) this;
        }

        public Criteria andNextMonthRepaymentGreaterThan(Long value) {
            addCriterion("next_month_repayment >", value, "nextMonthRepayment");
            return (Criteria) this;
        }

        public Criteria andNextMonthRepaymentGreaterThanOrEqualTo(Long value) {
            addCriterion("next_month_repayment >=", value, "nextMonthRepayment");
            return (Criteria) this;
        }

        public Criteria andNextMonthRepaymentLessThan(Long value) {
            addCriterion("next_month_repayment <", value, "nextMonthRepayment");
            return (Criteria) this;
        }

        public Criteria andNextMonthRepaymentLessThanOrEqualTo(Long value) {
            addCriterion("next_month_repayment <=", value, "nextMonthRepayment");
            return (Criteria) this;
        }

        public Criteria andNextMonthRepaymentIn(List<Long> values) {
            addCriterion("next_month_repayment in", values, "nextMonthRepayment");
            return (Criteria) this;
        }

        public Criteria andNextMonthRepaymentNotIn(List<Long> values) {
            addCriterion("next_month_repayment not in", values, "nextMonthRepayment");
            return (Criteria) this;
        }

        public Criteria andNextMonthRepaymentBetween(Long value1, Long value2) {
            addCriterion("next_month_repayment between", value1, value2, "nextMonthRepayment");
            return (Criteria) this;
        }

        public Criteria andNextMonthRepaymentNotBetween(Long value1, Long value2) {
            addCriterion("next_month_repayment not between", value1, value2, "nextMonthRepayment");
            return (Criteria) this;
        }

        public Criteria andExpectedRepaymentDateIsNull() {
            addCriterion("expected_repayment_date is null");
            return (Criteria) this;
        }

        public Criteria andExpectedRepaymentDateIsNotNull() {
            addCriterion("expected_repayment_date is not null");
            return (Criteria) this;
        }

        public Criteria andExpectedRepaymentDateEqualTo(Date value) {
            addCriterionForJDBCDate("expected_repayment_date =", value, "expectedRepaymentDate");
            return (Criteria) this;
        }

        public Criteria andExpectedRepaymentDateNotEqualTo(Date value) {
            addCriterionForJDBCDate("expected_repayment_date <>", value, "expectedRepaymentDate");
            return (Criteria) this;
        }

        public Criteria andExpectedRepaymentDateGreaterThan(Date value) {
            addCriterionForJDBCDate("expected_repayment_date >", value, "expectedRepaymentDate");
            return (Criteria) this;
        }

        public Criteria andExpectedRepaymentDateGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("expected_repayment_date >=", value, "expectedRepaymentDate");
            return (Criteria) this;
        }

        public Criteria andExpectedRepaymentDateLessThan(Date value) {
            addCriterionForJDBCDate("expected_repayment_date <", value, "expectedRepaymentDate");
            return (Criteria) this;
        }

        public Criteria andExpectedRepaymentDateLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("expected_repayment_date <=", value, "expectedRepaymentDate");
            return (Criteria) this;
        }

        public Criteria andExpectedRepaymentDateIn(List<Date> values) {
            addCriterionForJDBCDate("expected_repayment_date in", values, "expectedRepaymentDate");
            return (Criteria) this;
        }

        public Criteria andExpectedRepaymentDateNotIn(List<Date> values) {
            addCriterionForJDBCDate("expected_repayment_date not in", values, "expectedRepaymentDate");
            return (Criteria) this;
        }

        public Criteria andExpectedRepaymentDateBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("expected_repayment_date between", value1, value2, "expectedRepaymentDate");
            return (Criteria) this;
        }

        public Criteria andExpectedRepaymentDateNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("expected_repayment_date not between", value1, value2, "expectedRepaymentDate");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table task_repayment_predictions
     *
     * @mbg.generated do_not_delete_during_merge Tue Dec 17 01:34:51 CST 2024
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table task_repayment_predictions
     *
     * @mbg.generated Tue Dec 17 01:34:51 CST 2024
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}