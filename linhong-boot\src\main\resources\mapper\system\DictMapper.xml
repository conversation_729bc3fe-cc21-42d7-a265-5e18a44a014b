<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.linhong.boot.system.mapper.DictMapper">
    <!-- 字典分页列表 -->
    <select id="getDictPage" resultType="com.linhong.boot.system.model.vo.DictPageVO">
        SELECT
            t1.id,
            t1.name,
            t1.dict_code,
            t1.status
        FROM
            sys_dict t1
        <where>
            t1.is_deleted = 0
            <if test='queryParams.keywords!=null and queryParams.keywords.trim() neq ""'>
                AND (
                    t1.name LIKE CONCAT('%',#{queryParams.keywords},'%')
                    OR
                    t1.dict_code LIKE CONCAT('%',#{queryParams.keywords},'%')
                )
            </if>
        </where>
        ORDER BY
            t1.create_time DESC
    </select>

    <resultMap id="DictWithDataMap" type="com.linhong.boot.system.model.vo.DictVO">
        <result column="name" property="name"/>
        <result column="dict_code" property="dictCode"/>
        <collection property="dictDataList"
                    column="{dictCode=dict_code}"
                    select="com.linhong.boot.system.mapper.DictDataMapper.getDictDataList">
        </collection>
    </resultMap>

    <!-- 获取字典列表（包含字典数据） -->
    <select id="getAllDictWithData" resultMap="DictWithDataMap">
        SELECT
            t1.name,
            t1.dict_code
        FROM
            sys_dict t1
        WHERE
            t1.is_deleted = 0 AND t1.status = 1
        ORDER BY
            t1.create_time DESC
    </select>
</mapper>
