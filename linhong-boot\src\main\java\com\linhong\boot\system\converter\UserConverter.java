package com.linhong.boot.system.converter;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linhong.boot.system.model.bo.UserBO;
import com.linhong.boot.system.model.dto.UserImportDTO;
import com.linhong.boot.system.model.entity.User;
import com.linhong.boot.system.model.form.UserForm;
import com.linhong.boot.system.model.form.UserProfileForm;
import com.linhong.boot.system.model.vo.UserInfoVO;
import com.linhong.boot.system.model.vo.UserPageVO;
import com.linhong.boot.system.model.vo.UserProfileVO;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

/**
 * 用户对象转换器
 *
 */
@Mapper(componentModel = "spring")
public interface UserConverter {

    @Mappings({
            @Mapping(target = "fiveGAccount", source = "fiveGAccount")
    })
    UserPageVO toPageVo(UserBO bo);

    Page<UserPageVO> toPageVo(Page<UserBO> bo);
    
    @Mappings({
            @Mapping(target = "fiveGAccount", source = "fiveGAccount")
    })
    UserForm toForm(User entity);

    @Mappings({
            @Mapping(target = "fiveGAccount", source = "fiveGAccount"),
            @Mapping(target = "createBy", ignore = true),
            @Mapping(target = "updateBy", ignore = true),
            @Mapping(target = "isDeleted", ignore = true),
            @Mapping(target = "createTime", ignore = true),
            @Mapping(target = "updateTime", ignore = true)
    })
    User toEntity(UserForm entity);

    @Mappings({
            @Mapping(target = "userId", source = "id")
    })
    UserInfoVO toUserInfoVo(User entity);

    @Mappings({
            @Mapping(target = "fiveGAccount", ignore = true), // 导入时暂时忽略5G账号
            @Mapping(target = "createBy", ignore = true),
            @Mapping(target = "updateBy", ignore = true),
            @Mapping(target = "isDeleted", ignore = true),
            @Mapping(target = "createTime", ignore = true),
            @Mapping(target = "updateTime", ignore = true),
            @Mapping(target = "password", ignore = true)
    })
    User toEntity(UserImportDTO vo);


    @Mappings({
            @Mapping(target = "fiveGAccount", source = "fiveGAccount")
    })
    UserProfileVO toProfileVO(UserBO bo);

    @Mappings({
            @Mapping(target = "fiveGAccount", source = "fiveGAccount"),
            @Mapping(target = "createBy", ignore = true),
            @Mapping(target = "updateBy", ignore = true),
            @Mapping(target = "isDeleted", ignore = true),
            @Mapping(target = "createTime", ignore = true),
            @Mapping(target = "updateTime", ignore = true),
            @Mapping(target = "password", ignore = true),
            @Mapping(target = "deptId", ignore = true)
    })
    User toEntity(UserProfileForm formData);
}
