<template>
  <!-- 新增联系人 -->
  <el-drawer
    v-model="props.show"
    :title="id ? '编辑联系人' : '新增联系人'"
    append-to-body
    @close="handleCloseDialog"
  >
    <eleForm
      ref="eleFormRef"
      :formItemList="contactPersonList"
      :formBox="formBox"
      :operateOpen="false"
      @handleSubmit="formHandleSubmit"
    />
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" :loading="loading" @click="handleSubmit">确 定</el-button>
        <el-button :loading="loading" @click="handleCloseDialog">取 消</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script lang="ts" setup>
import eleForm from "@/components/EleComponents/ele-form.vue";
import { ElMessage, ElMessageBox, ElButton, ElLoading } from "element-plus";

import { contactPersonList } from "../fieldsSys";
import dataSystemAPI from "@/api/dataSystem/pool";
const props = defineProps({
  show: {
    type: Boolean,
    required: true,
  },
  ids: {
    type: Number,
    required: "",
  },
  id: {
    type: Number,
    required: "",
  },
});

watch(
  () => props.id,
  (val) => {
    if (val) emergencyDetail();
  }
);
const emit = defineEmits(["update:show", "refresh"]);
const loading = ref(false);
const formBox = ref({
  spanItem: 24, // 每项占比
  formContent: 24, // 表单父级占比
  operate: 4, // 操作父容器占比
  operateItem: 12,
  operateGrid: " start",
});
const eleFormRef = ref(null);

// 获取联系人详情
const emergencyDetail = () => {
  dataSystemAPI
    .emergencyDetail({
      id: props.id,
    })
    .then((e: any) => {
      eleFormRef.value.setFormVlaue(e);
    });
};

//提交
const handleSubmit = () => {
  eleFormRef.value.handleSubmit();
};
const formHandleSubmit = (data: any) => {
  console.log(data);
  loading.value = true;
  if (props.id) {
    //修改
    dataSystemAPI
      .emergencyModify({
        ...data,
        userId: props.ids,
        id: props.id,
      })
      .then((e: any) => {
        ElMessage({
          message: "修改成功",
          type: "success",
        });
        loading.value = false;
        emit("refresh");
        handleCloseDialog();
      });
  } else {
    dataSystemAPI
      .emergencySave({
        ...data,
        userId: props.ids,
      })
      .then((e: any) => {
        ElMessage({
          message: "新增成功",
          type: "success",
        });
        loading.value = false;
        emit("refresh");
        handleCloseDialog();
      });
  }
};
//关闭弹窗
const handleCloseDialog = () => {
  console.log("关闭");
  eleFormRef.value.resetFields();
  emit("update:show", false);
};
</script>

<style lang="scss" scoped></style>
