import { queryMode } from "@/enums/optionsConfig/pool";
import { useUserStore, useArchitectureStoreHook } from "@/store";
import { changeRecordsOption } from "@/enums/optionsConfig/pool";
const userStore = useUserStore();
const architectureStore = useArchitectureStoreHook();
architectureStore.getArchitectureInfo();
/**-------------------------动态获取筛选内容---------------------------**/

//催收人/组织
const userOrDept = reactive({
  label: "催收人/组织",
  type: "cascader",
  options: JSON.parse(JSON.stringify(architectureStore.architectureInfoTissue)),
  field: "userOrDept",
  placeholder: "请选择",
  disabled: userStore.userInfo.roles[0] == "YG" ? true : false,
  props: {
    value: "value",
    label: "label",
    checkStrictly: true,
  },
  rules: [{ required: true, message: "请选择催收人/组织", trigger: "blur" }],
});

/**-----------------------页面呈现列表相关内容-----------------------------**/

// 表头 - 按照换单查询.html的列顺序排列
const jobListFields = [
  { label: "客户姓名", prop: "customerName" },
  { label: "客户索引号", prop: "customerIndexNumber", width: "180px" },
  { label: "换单日期", prop: "huanTime" },
  { label: "换单类型", prop: "huanType" },
  { label: "作业员/工号", prop: "userOrDept" },
  { label: "组织", prop: "deptName" },
  { label: "批次号", prop: "batchNumber" },
];

//筛选字段
const screenList: any = [
  {
    label: "客户索引号",
    type: "input",
    options: queryMode,
    fieldKey: "key13-key",
    field: "customerIndexNumber",
    placeholder: "请输入",
    rules: "",
  },
  {
    label: "客户姓名",
    type: "input",
    options: queryMode,
    fieldKey: "key4-key",
    field: "customerName",
    placeholder: "请输入",
    rules: "",
  },
  {
    label: "持卡人代码",
    type: "input",
    options: queryMode,
    fieldKey: "key6-key",
    field: "cardholderCode",
    placeholder: "请输入",
    rules: "",
  },

  {
    label: "外包序号",
    type: "input",
    field: "outsourceSerialNumber",
    placeholder: "请输入",
    rules: "",
  },
  {
    label: "换单日期",
    type: "date-picker",
    options: [],
    field: "huanTime",
    placeholder: "请选择",
    rules: "",
    formType: "daterange",
    size: "default",
  },
  {
    label: "换单类型",
    type: "select",
    options: changeRecordsOption,
    field: "huanType",
    placeholder: "请选择",
    rules: "",
  },
];

export { userOrDept, jobListFields, screenList };
