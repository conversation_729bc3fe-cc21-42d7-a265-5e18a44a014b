package com.linhong.boot.management.service.impl;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.linhong.boot.management.mapper.KeyValueStoreMapper;
import com.linhong.boot.management.model.entity.KeyValueStore;
import com.linhong.boot.management.service.KeyValueStoreService;

import lombok.RequiredArgsConstructor;

/**
 * 跟进状态表
 */
@Service
@RequiredArgsConstructor
public class KeyValueStoreServiceImpl extends ServiceImpl<KeyValueStoreMapper, KeyValueStore> implements KeyValueStoreService {
	
	
	


}
