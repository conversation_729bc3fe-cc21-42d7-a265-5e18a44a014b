package com.linhong.boot.shared.file.service;

import org.springframework.web.multipart.MultipartFile;

import com.linhong.boot.shared.file.model.FileInfo;

/**
 * 对象存储服务接口层
 *
 * <AUTHOR>
 * @since 2022/11/19
 */
public interface FileService {

    /**
     * 上传文件
     * @param file 表单文件对象
     * @return 文件信息
     */
    FileInfo uploadFile(MultipartFile file);

    /**
     * 删除文件
     *
     * @param filePath 文件完整URL
     * @return 删除结果
     */
    boolean deleteFile(String filePath);


}
