<template>
  <el-dialog
    v-model="visible"
    :show-close="false"
    :fullscreen="isFullscreen"
    width="50%"
    append-to-body
    @close="handleClose"
  >
    <template #header>
      <div class="flex-x-between">
        <span>通知公告详情</span>
        <div class="dialog-toolbar">
          <!-- 全屏/退出全屏按钮 -->
          <el-button circle @click="toggleFullscreen">
            <SvgIcon v-if="isFullscreen" icon-class="fullscreen-exit" />
            <SvgIcon v-else icon-class="fullscreen" />
          </el-button>
          <!-- 关闭按钮 -->
          <el-button circle @click="handleClose">
            <template #icon>
              <Close />
            </template>
          </el-button>
        </div>
      </div>
    </template>

    <el-descriptions :column="1">
      <el-descriptions-item label="标题：">
        {{ notice.title }}
      </el-descriptions-item>
      <el-descriptions-item label="发布状态：">
        <el-tag v-if="notice.publishStatus == 0" type="info">未发布</el-tag>
        <el-tag v-else-if="notice.publishStatus == 1" type="success">
          已发布
        </el-tag>
        <el-tag v-else-if="notice.publishStatus == -1" type="warning">
          已撤回
        </el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="发布人：">
        {{ notice.publisherName }}
      </el-descriptions-item>
      <el-descriptions-item label="发布时间：">
        {{ notice.publishTime }}
      </el-descriptions-item>
      <el-descriptions-item label="公告内容：">
        <div v-html="notice.content" />
      </el-descriptions-item>
    </el-descriptions>

    <!-- 重置密码操作区域 -->
    <div v-if="isPasswordResetRequest" class="mt-4">
      <el-divider />
      <div class="flex justify-center">
        <el-button type="primary" @click="handleResetPassword">
          重置密码
        </el-button>
      </div>
    </div>


  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import NoticeAPI, { NoticeDetailVO } from "@/api/system/notice";
import UserAPI from "@/api/system/user";

const visible = ref(false);
const notice = ref<NoticeDetailVO>({});
const isFullscreen = ref(false); // 控制全屏状态

// 判断是否为重置密码请求
const isPasswordResetRequest = computed(() => {
  return notice.value.title?.includes('申请重置密码') || false;
});

// 提取用户数据
const extractedUserData = computed(() => {
  if (!isPasswordResetRequest.value) return null;

  const title = notice.value.title || '';
  const match = title.match(/用户\s+(\S+)\s+申请重置密码/);
  if (match) {
    return {
      id: 0, // 这里需要通过用户名查询用户ID
      username: match[1],
      nickname: match[1]
    };
  }
  return null;
});

// 切换全屏
const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value;
};

// 关闭弹窗
const handleClose = () => {
  visible.value = false;
};

// 处理重置密码
const handleResetPassword = async () => {
  if (!extractedUserData.value) {
    ElMessage.error("无法获取用户信息");
    return;
  }

  try {
    // 根据用户名查询用户信息
    const userList = await UserAPI.getPage({
      keywords: extractedUserData.value.username,
      pageNum: 1,
      pageSize: 10
    });

    const user = userList.list.find((u: any) => u.username === extractedUserData.value?.username);
    if (!user) {
      ElMessage.error("用户不存在");
      return;
    }

    // 使用现有的重置密码逻辑（类似用户管理页面）
    ElMessageBox.prompt("请输入用户【" + user.username + "】的新密码", "重置密码", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
    }).then(
      ({ value }) => {
        if (!value || value.length < 6) {
          ElMessage.warning("密码至少需要6位字符，请重新输入");
          return false;
        }
        UserAPI.resetPassword(user.id, value).then(() => {
          ElMessage.success("密码重置成功，新密码是：" + value);
          // 重置密码成功后，确保通知被标记为已读
          // 由于getDetail方法已经会自动标记为已读，这里我们可以触发一次获取详情来确保已读状态
          if (notice.value.id) {
            NoticeAPI.getDetail(notice.value.id.toString())
              .then(() => {
                console.log("通知已标记为已读");
              })
              .catch((error: any) => {
                console.warn("标记通知为已读失败:", error);
              });
          }
        });
      },
      () => {
        ElMessage.info("已取消重置密码");
      }
    );
  } catch (error) {
    ElMessage.error("查询用户信息失败");
  }
};

// 接收公告详情
const openNotice = async (id: string) => {
  visible.value = true;
  const noticeDetail = await NoticeAPI.getDetail(id);
  notice.value = noticeDetail;
};

defineExpose({
  openNotice,
});
</script>

<style scoped></style>
