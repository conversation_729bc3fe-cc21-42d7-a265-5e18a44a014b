package com.linhong.boot.common.enums;

import com.linhong.boot.common.base.IBaseEnum;

import lombok.Getter;

/**
 * 状态枚举
 */
@Getter
public enum TransferRecordEnum implements IBaseEnum<Integer> {
	//分派类型，0:案池分派 1:换单 2:锁定 3:主管分派
	/**
	 * 案池分派
	 */
	AN_CHI_FEN_PAI(0, "案池分派"),
	/**
	 * 换单
	 */
	HUAN_DAN(1, "换单"),
	/**
	 * 锁定
	 */
	SUO_DIN(2, "锁定"),
	/**
	 * 主管分派
	 */
	ZHU_GUAN_FEN_PAI(3, "主管分派"),
	/**
	 * 退回案池
	 */
	TUI_HUI_AN_CHI(4,"退回案池");

    private final Integer value;


    private final String label;

    TransferRecordEnum(Integer value, String label) {
        this.value = value;
        this.label = label;
    }
    
    /**
     * 根据value获取label
     * @param value
     * @return status.getLabel
     */
    public static String fromValue(int value) {
        for (TransferRecordEnum status : TransferRecordEnum.values()) {
            if (status.getValue() == value) {
                return status.getLabel();
            }
        }
        throw new IllegalArgumentException("CaseEnum未知参数: " + value);
    }

    /**
     * 根据label获取value
     * @param label
     * @return
     */
    public static Integer fromLabel(String label) {
        for (TransferRecordEnum status : TransferRecordEnum.values()) {
            if (status.getLabel().equals(label)) {
                return status.getValue();
            }
        }
        throw new IllegalArgumentException("CaseEnum未知参数: " + label);
    }

    
}
