<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.linhong.boot.management.mapper.TransferRecordsMapper">
  <resultMap id="BaseResultMap" type="com.linhong.boot.management.model.entity.TransferRecords">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Dec 02 17:42:49 CST 2024.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="source_organization" jdbcType="VARCHAR" property="sourceOrganization" />
    <result column="target_organization" jdbcType="VARCHAR" property="targetOrganization" />
    <result column="source_user" jdbcType="VARCHAR" property="sourceUser" />
    <result column="target_user" jdbcType="VARCHAR" property="targetUser" />
    <result column="assignment_type" jdbcType="VARCHAR" property="assignmentType" />
    <result column="operator" jdbcType="VARCHAR" property="operator" />
    <result column="operation_time" jdbcType="TIMESTAMP" property="operationTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Page_Where_Clause">
    <where>
     	o.assignment_type = 1
        <if test="query.customerName !=null and query.customerName.trim() neq '' ">
     		and o.customer_name LIKE CONCAT('%', #{query.customerName}, '%')
     	</if>
     	<if test="query.customerIndexNumber !=null and query.customerIndexNumber.trim() neq '' ">
     		and o.customer_index_number LIKE CONCAT('%', #{query.customerIndexNumber}, '%')
     	</if>
     	<if test="query.huanType !=null and query.huanType.trim() neq '' ">
     		and o.assType = #{query.huanType}
     	</if>
     	<if test="query.cardholderCode !=null and query.cardholderCode.trim() neq '' ">
     		and o.cardholder_code LIKE CONCAT('%', #{query.cardholderCode}, '%')
     	</if>
     	<if test="query.huanTime !=null and query.huanTime.length == 2 ">
     		<if test="query.huanTime[0] != null and query.huanTime[1] != null">
            	<bind name="sT" value="query.huanTime[0]"/>
            	<bind name="eT" value="query.huanTime[1]"/>
                    and o.operation_time BETWEEN #{sT} and #{eT}
            </if>
     	</if>
     	<if test="query.userOrDept !=null and query.userOrDept.trim() neq '' ">
     		<if test="query.uIds != null and query.uIds.size() > 0">
            	and o.operator IN
            	<foreach item="uId" index="index" collection="query.uIds" open="(" separator="," close=")">
               		#{uId}
            	</foreach>
        	</if>
     	</if>
     	
     
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Dec 02 17:42:49 CST 2024.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Dec 02 17:42:49 CST 2024.
    -->
    id, source_organization, target_organization, source_user, target_user, assignment_type, 
    operator, operation_time
  </sql>
  
  <select id="selectUserOrDept" parameterType="list" resultType="com.linhong.boot.management.model.entity.TransferRecords">
   	 	SELECT tr.*
		FROM transfer_records tr
		INNER JOIN (
		    SELECT id, MAX(operation_time) AS max_opt_time
		    FROM transfer_records
		    GROUP BY id
		) latest_records ON tr.id = latest_records.id AND tr.operation_time = latest_records.max_opt_time 
		where 1=1 
		<if test="list != null and list.size() > 0">
           	and tr.id in 
           	<foreach item="cid" index="index" collection="list" open="(" separator="," close=")">
              	#{cid}
           	</foreach>
        </if>
  </select>
  
  <select id="selectTransPage" parameterType="com.linhong.boot.management.model.query.CcUserQuery" resultType="com.linhong.boot.management.model.vo.TransferRecordsVO">
    select * from (
		SELECT
		c.id,c.customer_name, c.customer_index_number , c.case_agency as outsourceSerialNumber , r.operation_time , r.assignment_type, r.operator,
		c.cardholder_code,
		CASE WHEN r.source_user = '案池' THEN 1 ELSE 2 END AS assType
		FROM
		transfer_records r left join cc_user c on r.id = c.id 
	) o 
	<include refid="Page_Where_Clause" />
	
  </select>
  <select id="selectByExample" parameterType="com.linhong.boot.management.model.entity.TransferRecordsExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Dec 02 17:42:49 CST 2024.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from transfer_records
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Dec 02 17:42:49 CST 2024.
    -->
    select 
    <include refid="Base_Column_List" />
    from transfer_records
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Dec 02 17:42:49 CST 2024.
    -->
    delete from transfer_records
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.linhong.boot.management.model.entity.TransferRecordsExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Dec 02 17:42:49 CST 2024.
    -->
    delete from transfer_records
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
<!--   <insert id="insert" parameterType="com.linhong.boot.management.model.entity.TransferRecords">
    
    insert into transfer_records (id, source_organization, target_organization, 
      source_user, target_user, assignment_type, 
      operator, operation_time)
    values (#{id,jdbcType=INTEGER}, #{sourceOrganization,jdbcType=VARCHAR}, #{targetOrganization,jdbcType=VARCHAR}, 
      #{sourceUser,jdbcType=VARCHAR}, #{targetUser,jdbcType=VARCHAR}, #{assignmentType,jdbcType=VARCHAR}, 
      #{operator,jdbcType=VARCHAR}, #{operationTime,jdbcType=TIMESTAMP})
  </insert> -->
  <insert id="insertSelective" parameterType="com.linhong.boot.management.model.entity.TransferRecords">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Dec 02 17:42:49 CST 2024.
    -->
    insert into transfer_records
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="sourceOrganization != null">
        source_organization,
      </if>
      <if test="targetOrganization != null">
        target_organization,
      </if>
      <if test="sourceUser != null">
        source_user,
      </if>
      <if test="targetUser != null">
        target_user,
      </if>
      <if test="assignmentType != null">
        assignment_type,
      </if>
      <if test="operator != null">
        operator,
      </if>
      <if test="operationTime != null">
        operation_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="sourceOrganization != null">
        #{sourceOrganization,jdbcType=VARCHAR},
      </if>
      <if test="targetOrganization != null">
        #{targetOrganization,jdbcType=VARCHAR},
      </if>
      <if test="sourceUser != null">
        #{sourceUser,jdbcType=VARCHAR},
      </if>
      <if test="targetUser != null">
        #{targetUser,jdbcType=VARCHAR},
      </if>
      <if test="assignmentType != null">
        #{assignmentType,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operationTime != null">
        #{operationTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.linhong.boot.management.model.entity.TransferRecordsExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Dec 02 17:42:49 CST 2024.
    -->
    select count(*) from transfer_records
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Dec 02 17:42:49 CST 2024.
    -->
    update transfer_records
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=INTEGER},
      </if>
      <if test="row.sourceOrganization != null">
        source_organization = #{row.sourceOrganization,jdbcType=VARCHAR},
      </if>
      <if test="row.targetOrganization != null">
        target_organization = #{row.targetOrganization,jdbcType=VARCHAR},
      </if>
      <if test="row.sourceUser != null">
        source_user = #{row.sourceUser,jdbcType=VARCHAR},
      </if>
      <if test="row.targetUser != null">
        target_user = #{row.targetUser,jdbcType=VARCHAR},
      </if>
      <if test="row.assignmentType != null">
        assignment_type = #{row.assignmentType,jdbcType=VARCHAR},
      </if>
      <if test="row.operator != null">
        operator = #{row.operator,jdbcType=VARCHAR},
      </if>
      <if test="row.operationTime != null">
        operation_time = #{row.operationTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Dec 02 17:42:49 CST 2024.
    -->
    update transfer_records
    set id = #{row.id,jdbcType=INTEGER},
      source_organization = #{row.sourceOrganization,jdbcType=VARCHAR},
      target_organization = #{row.targetOrganization,jdbcType=VARCHAR},
      source_user = #{row.sourceUser,jdbcType=VARCHAR},
      target_user = #{row.targetUser,jdbcType=VARCHAR},
      assignment_type = #{row.assignmentType,jdbcType=VARCHAR},
      operator = #{row.operator,jdbcType=VARCHAR},
      operation_time = #{row.operationTime,jdbcType=TIMESTAMP}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.linhong.boot.management.model.entity.TransferRecords">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Dec 02 17:42:49 CST 2024.
    -->
    update transfer_records
    <set>
      <if test="sourceOrganization != null">
        source_organization = #{sourceOrganization,jdbcType=VARCHAR},
      </if>
      <if test="targetOrganization != null">
        target_organization = #{targetOrganization,jdbcType=VARCHAR},
      </if>
      <if test="sourceUser != null">
        source_user = #{sourceUser,jdbcType=VARCHAR},
      </if>
      <if test="targetUser != null">
        target_user = #{targetUser,jdbcType=VARCHAR},
      </if>
      <if test="assignmentType != null">
        assignment_type = #{assignmentType,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        operator = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operationTime != null">
        operation_time = #{operationTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.linhong.boot.management.model.entity.TransferRecords">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Dec 02 17:42:49 CST 2024.
    -->
    update transfer_records
    set source_organization = #{sourceOrganization,jdbcType=VARCHAR},
      target_organization = #{targetOrganization,jdbcType=VARCHAR},
      source_user = #{sourceUser,jdbcType=VARCHAR},
      target_user = #{targetUser,jdbcType=VARCHAR},
      assignment_type = #{assignmentType,jdbcType=VARCHAR},
      operator = #{operator,jdbcType=VARCHAR},
      operation_time = #{operationTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>