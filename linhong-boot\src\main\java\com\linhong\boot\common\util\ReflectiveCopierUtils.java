package com.linhong.boot.common.util;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

import com.linhong.boot.common.annotation.ExcelComment;
import com.linhong.boot.management.model.vo.ExcelTitleVo;

/**
 * 数据转移
 */
public class ReflectiveCopierUtils {
	
	/**
	 * 
	 * @param source 被转移对象
	 * @param target 受转移对象
	 * @throws IllegalAccessException
	 */
	public static void copy(Object source, Object target) throws IllegalAccessException {
        Field[] sourceFields = source.getClass().getDeclaredFields();
        Field[] targetFields = target.getClass().getDeclaredFields();
        
        for (Field sourceField : sourceFields) {
            sourceField.setAccessible(true);
            for (Field targetField : targetFields) {
                targetField.setAccessible(true);
                if (sourceField.getName().equals(targetField.getName()) 
                    && sourceField.getType().equals(targetField.getType())) {
                    targetField.set(target, sourceField.get(source));
                }
            }
        }
    }
	
	/**
	 * 检查哪些属性有值
	 * @param o
	 * @return List<ExcelTitleVo>
	 */
	public static List<ExcelTitleVo> getNonEmptyFieldNames(Object obj) {
        Class<?> clazz = obj.getClass();
        Field[] fields = clazz.getDeclaredFields();
        List<ExcelTitleVo> titleList = new ArrayList<>();
        for (Field field : fields) {
        	field.setAccessible(true);
        	ExcelComment c = field.getAnnotation(ExcelComment.class);
        	ExcelTitleVo title = new ExcelTitleVo();
            try {
                Object value = field.get(obj);
                if (value != null && (value instanceof String ? !((String) value).isEmpty() : true)) {
                	title.setTitle(c !=null?c.value():"UnKnown");
                	title.setKey(field.getName());
                	if(!field.getName().equals("userOrDept") &&!field.getName().equals("isTuiAn") && !field.getName().equals("fileName"))
                		titleList.add(title);
                }
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            }
        }
        return titleList;
    }
	
}
