package com.linhong.boot.management.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.linhong.boot.management.model.entity.TaskRepaymentPredictions;
import com.linhong.boot.management.model.entity.TaskRepaymentPredictionsExample;

@Mapper
public interface TaskRepaymentPredictionsMapper extends BaseMapper<TaskRepaymentPredictions>{
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table task_repayment_predictions
     *
     * @mbg.generated Tue Dec 17 01:34:51 CST 2024
     */
    long countByExample(TaskRepaymentPredictionsExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table task_repayment_predictions
     *
     * @mbg.generated Tue Dec 17 01:34:51 CST 2024
     */
    int deleteByExample(TaskRepaymentPredictionsExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table task_repayment_predictions
     *
     * @mbg.generated Tue Dec 17 01:34:51 CST 2024
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table task_repayment_predictions
     *
     * @mbg.generated Tue Dec 17 01:34:51 CST 2024
     */
    int insert(TaskRepaymentPredictions row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table task_repayment_predictions
     *
     * @mbg.generated Tue Dec 17 01:34:51 CST 2024
     */
    int insertSelective(TaskRepaymentPredictions row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table task_repayment_predictions
     *
     * @mbg.generated Tue Dec 17 01:34:51 CST 2024
     */
    List<TaskRepaymentPredictions> selectByExampleWithBLOBs(TaskRepaymentPredictionsExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table task_repayment_predictions
     *
     * @mbg.generated Tue Dec 17 01:34:51 CST 2024
     */
    List<TaskRepaymentPredictions> selectByExample(TaskRepaymentPredictionsExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table task_repayment_predictions
     *
     * @mbg.generated Tue Dec 17 01:34:51 CST 2024
     */
    TaskRepaymentPredictions selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table task_repayment_predictions
     *
     * @mbg.generated Tue Dec 17 01:34:51 CST 2024
     */
    int updateByExampleSelective(@Param("row") TaskRepaymentPredictions row, @Param("example") TaskRepaymentPredictionsExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table task_repayment_predictions
     *
     * @mbg.generated Tue Dec 17 01:34:51 CST 2024
     */
    int updateByExampleWithBLOBs(@Param("row") TaskRepaymentPredictions row, @Param("example") TaskRepaymentPredictionsExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table task_repayment_predictions
     *
     * @mbg.generated Tue Dec 17 01:34:51 CST 2024
     */
    int updateByExample(@Param("row") TaskRepaymentPredictions row, @Param("example") TaskRepaymentPredictionsExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table task_repayment_predictions
     *
     * @mbg.generated Tue Dec 17 01:34:51 CST 2024
     */
    int updateByPrimaryKeySelective(TaskRepaymentPredictions row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table task_repayment_predictions
     *
     * @mbg.generated Tue Dec 17 01:34:51 CST 2024
     */
    int updateByPrimaryKeyWithBLOBs(TaskRepaymentPredictions row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table task_repayment_predictions
     *
     * @mbg.generated Tue Dec 17 01:34:51 CST 2024
     */
    int updateByPrimaryKey(TaskRepaymentPredictions row);
}