<template>
  <!-- 联系人 -->
  <el-dialog v-model="props.show" title="联系人" width="1000" align-center @close="handleCloseDialog">
    <el-row :gutter="24">
      <el-col :span="24">
        <eleForm ref="eleFormRef" :formItemList="contactPersonList" :formBox="formBox"
          :defineTxt="contactPersonId ? '修改' : '新建'" defineIcon="Check" @handleSubmit="formHandleSubmit"
          @handleCancel="resetForem" />
      </el-col>
      <el-col :span="24">
        <el-table ref="dataTableRef" v-loading="loading" :data="list" height="300" highlight-current-row border
          style="width: 100%">
          <el-table-column v-for="item in connectionMune" :key="item.prop" :label="item.label" :prop="item.prop"
            :width="item.width || ''" min-width="100" show-overflow-tooltip>
            <template v-if="item.prop == 'phone'" #default="scope">
              <el-button link type="primary" @click="copyToClipboard(scope.row.phone)">
                {{ scope.row.phone }}
              </el-button>
            </template>
          </el-table-column>
          <el-table-column v-hasPerm="['sys:poolDetails:contactPersonManage']" label="操作">
            <template #default="scope">
              <el-button type="primary" size="small" link icon="edit" @click="modifyFun(scope.row.id)">
                编辑
              </el-button>
              <el-button type="primary" size="small" link icon="delete" @click="deleteConnection(scope.row.id)">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-col>
    </el-row>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCloseDialog">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ElMessage, ElMessageBox, ElButton, ElLoading } from "element-plus";
import dataSystemAPI from "@/api/dataSystem/pool";
import eleForm from "@/components/EleComponents/ele-form.vue";
import { copyToClipboard } from "@/utils/commit";
import { connectionMune, contactPersonList } from "../fieldsSys";
const props = defineProps({
  show: {
    type: Boolean,
    required: true,
  },
  // ids
  ids: {
    type: Number,
    required: 0,
  },
});
watch(
  () => props.show,
  (val) => {
    if (val) handleQuery();
  }
);
const emit = defineEmits(["update:show", "refresh"]);

const loading = ref(false);
const list = ref([]);
const contactPersonId = ref<number | null>(null); //编辑联系人id
const eleFormRef = ref(null);
const formBox = ref({
  spanItem: 8, // 每项占比
  formContent: 24, // 表单父级占比
  operate: 24, // 操作父容器占比
  operateItem: 24,
  operateGrid: " end",
});
/**
 * 获取列表数据
 */
const handleQuery = () => {
  loading.value = true;
  dataSystemAPI
    .emergencyFindByPoolId({
      id: props.ids,
    })
    .then((data: any) => {
      list.value = data;
      loading.value = false;
    });
};

//编辑联系人
const modifyFun = (id: number) => {
  console.log(id);
  contactPersonId.value = id;
  dataSystemAPI
    .emergencyDetail({
      id: contactPersonId.value,
    })
    .then((e: any) => {
      eleFormRef.value.setFormVlaue(e);
    });
};
// 删除联系人
const deleteConnection = (id: number) => {
  ElMessageBox.confirm("确认删除该联系人?", "提示", {
    confirmButtonText: "确认",
    cancelButtonText: "取消",
    type: "warning",
    draggable: true,
  }).then(() => {
    loading.value = true;
    dataSystemAPI
      .emergencyDelete({
        id: id,
      })
      .then((e) => {
        ElMessage({
          message: "删除成功",
          type: "success",
        });
        loading.value = false;
        handleQuery();
      });
  });
};
const formHandleSubmit = (data: any) => {
  loading.value = true;
  if (contactPersonId.value) {
    //修改
    dataSystemAPI
      .emergencyModify({
        ...data,
        userId: props.ids,
        id: contactPersonId.value,
      })
      .then((e: any) => {
        ElMessage({
          message: "修改成功",
          type: "success",
        });
        resetForem();
      });
  } else {
    dataSystemAPI
      .emergencySave({
        ...data,
        userId: props.ids,
      })
      .then((e: any) => {
        ElMessage({
          message: "新增成功",
          type: "success",
        });
        resetForem();
      });
  }
};

//重置表单
const resetForem = () => {
  loading.value = false;
  handleQuery();
  contactPersonId.value = null;
  eleFormRef.value.setFormVlaue({});
};
//关闭弹窗
const handleCloseDialog = () => {
  console.log("关闭");
  emit("update:show", false);
};
</script>

<style lang="scss" scoped></style>
