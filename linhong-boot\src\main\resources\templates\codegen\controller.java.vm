package ${packageName}.${moduleName}.${subpackageName};

import ${packageName}.${moduleName}.service.${entityName}Service;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import ${packageName}.${moduleName}.model.form.${entityName}Form;
import ${packageName}.${moduleName}.model.query.${entityName}Query;
import ${packageName}.${moduleName}.model.vo.${entityName}VO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.linhong.boot.common.result.PageResult;
import com.linhong.boot.common.result.Result;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import jakarta.validation.Valid;

/**
 * $!{businessName}前端控制层
 *
 * <AUTHOR>
 * @since ${date}
 */
@Tag(name = "${businessName}接口")
@RestController
@RequestMapping("/api/v1/${lowerFirstEntityName}s")
@RequiredArgsConstructor
public class ${entityName}Controller  {

    private final ${entityName}Service ${lowerFirstEntityName}Service;

    @Operation(summary = "$!{businessName}分页列表")
    @GetMapping("/page")
    @PreAuthorize("@ss.hasPerm('${moduleName}:${lowerFirstEntityName}:query')")
    public PageResult<${entityName}VO> get${entityName}Page(${entityName}Query queryParams ) {
        IPage<${entityName}VO> result = ${lowerFirstEntityName}Service.get${entityName}Page(queryParams);
        return PageResult.success(result);
    }

    @Operation(summary = "新增${businessName}")
    @PostMapping
    @PreAuthorize("@ss.hasPerm('${moduleName}:${lowerFirstEntityName}:add')")
    public Result<Void> save${entityName}(@RequestBody @Valid ${entityName}Form formData ) {
        boolean result = ${lowerFirstEntityName}Service.save${entityName}(formData);
        return Result.judge(result);
    }

    @Operation(summary = "获取${businessName}表单数据")
    @GetMapping("/{id}/form")
    @PreAuthorize("@ss.hasPerm('${moduleName}:${lowerFirstEntityName}:edit')")
    public Result<${entityName}Form> get${entityName}Form(
        @Parameter(description = "$!{businessName}ID") @PathVariable Long id
    ) {
        ${entityName}Form formData = ${lowerFirstEntityName}Service.get${entityName}FormData(id);
        return Result.success(formData);
    }

    @Operation(summary = "修改${businessName}")
    @PutMapping(value = "/{id}")
    @PreAuthorize("@ss.hasPerm('${moduleName}:${lowerFirstEntityName}:edit')")
    public Result<Void> update${entityName}(
            @Parameter(description = "$!{businessName}ID") @PathVariable Long id,
            @RequestBody @Validated ${entityName}Form formData
    ) {
        boolean result = ${lowerFirstEntityName}Service.update${entityName}(id, formData);
        return Result.judge(result);
    }

    @Operation(summary = "删除${businessName}")
    @DeleteMapping("/{ids}")
    @PreAuthorize("@ss.hasPerm('${moduleName}:${lowerFirstEntityName}:delete')")
    public Result<Void> delete${entityName}s(
        @Parameter(description = "$!{businessName}ID，多个以英文逗号(,)分割") @PathVariable String ids
    ) {
        boolean result = ${lowerFirstEntityName}Service.delete${entityName}s(ids);
        return Result.judge(result);
    }
}
