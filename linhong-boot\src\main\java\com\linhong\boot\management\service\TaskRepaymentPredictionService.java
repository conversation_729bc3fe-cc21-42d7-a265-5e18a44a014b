package com.linhong.boot.management.service;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.linhong.boot.management.model.entity.TaskRepaymentPredictions;
import com.linhong.boot.management.model.query.TaskRepaymentPredictionQuery;

/**
 * 任务预测
 */
public interface TaskRepaymentPredictionService extends IService<TaskRepaymentPredictions>{

	/**
	 * 查询任务预测列表
	 * @param query
	 * @return
	 */
	public IPage<TaskRepaymentPredictions> getTaskRepaymentPage(TaskRepaymentPredictionQuery query);
	
	public IPage<TaskRepaymentPredictions> getTaskPage(TaskRepaymentPredictionQuery queryParams);
	/**
	 * 新增任务预测
	 */
	public boolean saveTaskRepayment(TaskRepaymentPredictions entity);
	/**
	 * 删除任务预测
	 * @param entity
	 * @return
	 */
	public boolean deletTaskRepayment(TaskRepaymentPredictions entity);
	/**
	 * 修改任务预测
	 * @param entity
	 * @return
	 */
	public boolean updateTaskRepayment(TaskRepaymentPredictions entity);
	/**
	 * 查看任务预测
	 * @param ccId
	 * @return
	 */
	public List<TaskRepaymentPredictions> getTaskRepaymentList(String ccId);
	/**
	 * 查询任务预测详情
	 * @param id
	 * @return
	 */
	public TaskRepaymentPredictions getTaskRepaymentOne(String id);
	
}
