package com.linhong.boot.management.listener;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.stream.Collectors;

import org.springframework.transaction.annotation.Transactional;

import com.alibaba.excel.context.AnalysisContext;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.esotericsoftware.minlog.Log;
import com.linhong.boot.common.base.BaseAnalysisEventListener;
import com.linhong.boot.common.enums.CaseEnum;
import com.linhong.boot.common.enums.QueryResultEnum;
import com.linhong.boot.common.util.ReflectiveCopierUtils;
import com.linhong.boot.management.model.dto.CcUserDTO;
import com.linhong.boot.management.model.entity.CcUser;
import com.linhong.boot.management.service.CasePoolService;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class CcUserImportListener extends BaseAnalysisEventListener<CcUserDTO> {


    // 有效条数
    private int validCount;

    // 无效条数
    private int invalidCount;
    
    /*外包序号*/
    private String outsourceSerialNumber;
    
    private String caseAgency;

    private List<CcUser> cList = new ArrayList<>();;
    
    private Set<String> existingCids = new HashSet<>();
    List<String> IndexList = new ArrayList<>();
    ConcurrentLinkedQueue<CcUser> dataQueue = new ConcurrentLinkedQueue<>();
    
    public String getCaseAgency() {
		return caseAgency;
	}

	public void setCaseAgency(String caseAgency) {
		this.caseAgency = caseAgency;
	}

	public String getOutsourceSerialNumber() {
		return outsourceSerialNumber;
	}

	public void setOutsourceSerialNumber(String outsourceSerialNumber) {
		this.outsourceSerialNumber = outsourceSerialNumber;
	}


	// 导入返回信息
    StringBuilder msg = new StringBuilder();

    private final CasePoolService casePoolService;

    public CcUserImportListener() {
		this.casePoolService=SpringUtil.getBean(CasePoolService.class);
    }


    @Override
    public void invoke(CcUserDTO ccUserDTO, AnalysisContext analysisContext){
        try {
        	CcUser ccUser = new CcUser(); 
			ReflectiveCopierUtils.copy(ccUserDTO, ccUser);
			//ccUser.setFollowUpStatus(CaseEnum.CASE_POOL.getValue().toString()); //初始化案件状态: 案池
//			ccUser.setOutsourceSerialNumber(outsourceSerialNumber);//外包序号
			ccUser.setLostContactQueryResult(QueryResultEnum.WEI_CHU.getValue().toString()); //初始化 失联查询状态未出
			ccUser.setIsMarkChange("-1");// 是否开启换单 默认:否
			ccUser.setEntrustTotalAmount(getSum(ccUser.getEntrustAmount()));//委托金额总额
			ccUser.setEntrustPrincipalTotal(getSum(ccUser.getEntrustPrincipal()));//委托本金总额
			ccUser.setIsLock("0"); //是否锁定(案池配置)默认否
			ccUser.setCaseAgency(caseAgency==null?"":caseAgency);//委案机构
			cList.add(ccUser);
			IndexList.add(ccUser.getCustomerIndexNumber());
			validCount++;
		} catch (IllegalAccessException e) {
			Log.error("导入异常:"+e.getMessage());
			e.printStackTrace();
		}
    }

    public BigDecimal getSum(String amount) {
    	if(amount==null) {
    		return BigDecimal.ZERO;
    	}
    	String[] amountString = amount.split("\\|");
    	BigDecimal sum = BigDecimal.ZERO;
    	for (String s : amountString) {
			sum = sum.add(new BigDecimal(s));
		}
    	return sum;
    }
    /**
     * 所有数据解析完成会来调用
     */
    @Override
    @Transactional
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
    	LambdaQueryWrapper<CcUser> cq = new LambdaQueryWrapper<>();
    	cq.in(CcUser::getCustomerIndexNumber, IndexList);
    	List<CcUser> oldUser = casePoolService.getBaseMapper().selectList(cq);
    	existingCids = oldUser.stream().map(CcUser::getCustomerIndexNumber).collect(Collectors.toSet());
    	Set<String> tuiAnCids = oldUser.stream().filter(c->c.getFollowUpStatus().equals(CaseEnum.TUI_AN.getValue().toString()))
    			.map(CcUser::getCustomerIndexNumber).collect(Collectors.toSet());
    	List<CcUser> updateList = new ArrayList<>();
    	for (CcUser c : cList) {
    		if (existingCids.contains(c.getCustomerIndexNumber())) {
    			CcUser updateCcUser = new CcUser();
    			try {
					ReflectiveCopierUtils.copy(c, updateCcUser);
				} catch (IllegalAccessException e) {
					e.printStackTrace();
				}
    			if (tuiAnCids.contains(c.getCustomerIndexNumber())) {
    				updateCcUser.setFollowUpStatus(CaseEnum.NEW_CASE.getValue().toString());
        		}
    			updateCcUser.setUserOrDept(c.getUserOrDept());
                updateList.add(updateCcUser);
            }
    		
		}
    	if (!updateList.isEmpty())
    		casePoolService.updateBatchByIdNumber(updateList);
    	
    	List<CcUser> newUser = cList.stream()
                 .filter(nu -> !existingCids.contains(nu.getCustomerIndexNumber()))
                 .collect(Collectors.toList());
    	for (CcUser ccUser : newUser) {
			ccUser.setFollowUpStatus(CaseEnum.CASE_POOL.getValue().toString());
			ccUser.setUserOrDept("");
		}
    	if(!newUser.isEmpty())
    		casePoolService.saveBatch(newUser);
//    	log.info("所有数据解析完成！"+dataQueue.size());
    }


    @Override
    public String getMsg() {
        // 总结信息
        return StrUtil.format("导入用户结束：成功{}条，失败{}条；{}", validCount, invalidCount, msg);
    }
}
