package com.linhong.boot.management.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.linhong.boot.management.mapper.TaskRepaymentPredictionsMapper;
import com.linhong.boot.management.model.entity.TaskRepaymentPredictions;
import com.linhong.boot.management.model.query.TaskRepaymentPredictionQuery;
import com.linhong.boot.management.service.TaskRepaymentPredictionService;
import com.linhong.boot.system.model.entity.Dept;
import com.linhong.boot.system.model.query.UserPageQuery;
import com.linhong.boot.system.model.vo.UserPageVO;
import com.linhong.boot.system.service.DeptService;
import com.linhong.boot.system.service.UserService;

import lombok.RequiredArgsConstructor;


@Service
@RequiredArgsConstructor
public class TaskRepaymentPredictionServiceImpl extends ServiceImpl<TaskRepaymentPredictionsMapper, TaskRepaymentPredictions> implements TaskRepaymentPredictionService {
	
	private final UserService userService;
	private final DeptService deptService;
	
	
	
	@Override
	public IPage<TaskRepaymentPredictions> getTaskRepaymentPage(TaskRepaymentPredictionQuery queryParams) {
		Page<TaskRepaymentPredictions> page = new Page<>(queryParams.getPageNum(), queryParams.getPageSize());
		QueryWrapper<TaskRepaymentPredictions> records = new QueryWrapper<>();
		records.eq("cc_id", queryParams.getCcId());
		IPage<TaskRepaymentPredictions> list = this.getBaseMapper().selectPage(page, records);
		return list;
	}
	
	

	@Override
	public IPage<TaskRepaymentPredictions> getTaskPage(TaskRepaymentPredictionQuery queryParams) {
		Page<TaskRepaymentPredictions> page = new Page<>(queryParams.getPageNum(), queryParams.getPageSize());
		if(queryParams.getUserOrDept()!=null) {
			/*判断是部门 还是 员工*/
			String uod = queryParams.getUserOrDept();
			Dept d = deptService.getBaseMapper().selectById(uod);
			List<Long> ids = new ArrayList<>();
			if(d!=null) {
				/*为部门查询 , 查询该部门下所有员工*/
				UserPageQuery q = new UserPageQuery();
				q.setPageSize(10000);
				q.setDeptId(uod);
				IPage<UserPageVO> u = userService.getUserPage(q);
				ids = u.getRecords().stream().map(UserPageVO::getId).collect(Collectors.toList());
			}else {
				/*指定的员工查询*/
				ids.add(Long.valueOf(uod));
				queryParams.setUIds(ids);
			}
			queryParams.setUIds(ids);
		}
		LambdaQueryWrapper<TaskRepaymentPredictions> records = new LambdaQueryWrapper<>();
		
		records.eq(queryParams.getCustomerName()!=null, TaskRepaymentPredictions::getCustomerName, queryParams.getCustomerName());
		records.eq(queryParams.getCustomerIndexNumber()!=null, TaskRepaymentPredictions::getCustomerIndexNumber, queryParams.getCustomerIndexNumber());
		if(queryParams.getHuanTime()!=null)
			records.between(TaskRepaymentPredictions::getExpectedRepaymentDate, queryParams.getHuanTime()[0], queryParams.getHuanTime()[1]);
		records.in(queryParams.getBatchNumber()!=null, TaskRepaymentPredictions::getBatchNumber, queryParams.getBatchNumber());
		records.in(queryParams.getUIds()!=null,TaskRepaymentPredictions::getOperatorId, queryParams.getUIds());
		IPage<TaskRepaymentPredictions> list = this.getBaseMapper().selectPage(page, records);
		return list;
	}

	@Override
	public boolean saveTaskRepayment(TaskRepaymentPredictions entity) {
		int res = this.getBaseMapper().insert(entity);
		return res>0?true:false;
	}

	@Override
	public boolean deletTaskRepayment(TaskRepaymentPredictions entity) {
		int res = this.getBaseMapper().deleteById(entity.getId());
		return res>0?true:false;
	}

	@Override
	public boolean updateTaskRepayment(TaskRepaymentPredictions entity) {
		int res = this.getBaseMapper().updateById(entity);
		return res>0?true:false;
	}

	@Override
	public List<TaskRepaymentPredictions> getTaskRepaymentList(String ccId) {
		LambdaQueryWrapper<TaskRepaymentPredictions> taskQuery = new LambdaQueryWrapper<>();
		taskQuery.eq(TaskRepaymentPredictions::getCcId, ccId);
		List<TaskRepaymentPredictions> taskList = this.getBaseMapper().selectList(taskQuery);
		return taskList;
	}

	@Override
	public TaskRepaymentPredictions getTaskRepaymentOne(String id) {
		TaskRepaymentPredictions task = this.getBaseMapper().selectById(id);
		return task;
	}

	

	
	
	


}
