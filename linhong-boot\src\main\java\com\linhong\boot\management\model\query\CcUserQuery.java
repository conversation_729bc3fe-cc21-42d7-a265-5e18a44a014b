package com.linhong.boot.management.model.query;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.linhong.boot.management.model.entity.CcUser;

import lombok.Data;


/**
 * 案件查询对象
 */
@Data
public class CcUserQuery {
	
	private Long id;
	
	private int pageNum = 1;

	private int pageSize = 10;
	
	/**
	 * 失联查询结果 0:已出,1:未出,2:未查,3:其他
	 */
	private String lostContactQueryResult;
	/**
	 * 催收人或者组织
	 * 如果选择的用户为组长,部门负责人,则他下面所有人的案件都要可见
	 */
    private String userOrDept;
    /**
     * 客户姓名
     */
    private String customerName;
    /**
     * 批次号
     */
    private List<String> batchNumber;
    /**
     * 客户索引号
     */
    private String customerIndexNumber;
    /**
     * 案件类型
     */
    private String caseType;
    /**
     * 委案机构
     */
    private String caseAgency;
    /**
     * 跟进状态
     */
    private String followUpStatus;
    /**
     * 委案开始日期
     */
    private String[] entrustStartDate;
    /**
     * 委案结束日期
     */
    private String[] entrustEndDate;
    /**
     * 
     * 委托金额段
     */
    private String[] entrustTotalAmount;
    
    /**
     * 身份证
     */
    private String cardholderIdNumber;
    
    /**
     * 持卡人代码
     */
    private String cardholderCode;
    /**
     * 委托时月龄分档
     */
    private String entrustAgeBracket;
    /**
     * 委托时佣金分档
     */
    private String entrustCommissionBracket;
    /**
     * 分配功能案件id集
     */
    private List<Long> ids;
    /**
     * 分配功能员工id集
     */
    @JsonProperty("uIds")
    private List<Long> uIds;
    /**
     * 分配功能 需要更改的案件数量
     */
    @JsonProperty("cIdNum")
    private int cIdNum;
    /* 外包序号 */
    private String outsourceSerialNumber;
    /*是否标记换单*/
    private String isMarkChange;
    
    private List<CcUser> userList;
    
    private String orderBy;
    
    private String ascOrDesc;
    
    private List<String> followUpStatusList;
    /**
     * 换单类型 1:换进  2:换出
     */
    private String huanType;
    private String[] huanTime;
    
    private String isLock;
    
}