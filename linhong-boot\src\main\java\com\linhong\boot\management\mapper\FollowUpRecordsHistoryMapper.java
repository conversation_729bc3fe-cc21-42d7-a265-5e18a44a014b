package com.linhong.boot.management.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.linhong.boot.management.model.entity.FollowUpRecordsHistory;
import com.linhong.boot.management.model.entity.FollowUpRecordsHistoryExample;
import com.linhong.boot.management.model.entity.FollowUpRecordsHistoryWithBLOBs;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface FollowUpRecordsHistoryMapper extends BaseMapper<FollowUpRecordsHistory> {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table follow_up_records_history
     *
     * @mbg.generated Thu Dec 26 15:31:06 CST 2024
     */
    long countByExample(FollowUpRecordsHistoryExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table follow_up_records_history
     *
     * @mbg.generated Thu Dec 26 15:31:06 CST 2024
     */
    int deleteByExample(FollowUpRecordsHistoryExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table follow_up_records_history
     *
     * @mbg.generated Thu Dec 26 15:31:06 CST 2024
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table follow_up_records_history
     *
     * @mbg.generated Thu Dec 26 15:31:06 CST 2024
     */
    int insert(FollowUpRecordsHistoryWithBLOBs row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table follow_up_records_history
     *
     * @mbg.generated Thu Dec 26 15:31:06 CST 2024
     */
    int insertSelective(FollowUpRecordsHistoryWithBLOBs row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table follow_up_records_history
     *
     * @mbg.generated Thu Dec 26 15:31:06 CST 2024
     */
    List<FollowUpRecordsHistoryWithBLOBs> selectByExampleWithBLOBs(FollowUpRecordsHistoryExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table follow_up_records_history
     *
     * @mbg.generated Thu Dec 26 15:31:06 CST 2024
     */
    List<FollowUpRecordsHistory> selectByExample(FollowUpRecordsHistoryExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table follow_up_records_history
     *
     * @mbg.generated Thu Dec 26 15:31:06 CST 2024
     */
    FollowUpRecordsHistoryWithBLOBs selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table follow_up_records_history
     *
     * @mbg.generated Thu Dec 26 15:31:06 CST 2024
     */
    int updateByExampleSelective(@Param("row") FollowUpRecordsHistoryWithBLOBs row, @Param("example") FollowUpRecordsHistoryExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table follow_up_records_history
     *
     * @mbg.generated Thu Dec 26 15:31:06 CST 2024
     */
    int updateByExampleWithBLOBs(@Param("row") FollowUpRecordsHistoryWithBLOBs row, @Param("example") FollowUpRecordsHistoryExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table follow_up_records_history
     *
     * @mbg.generated Thu Dec 26 15:31:06 CST 2024
     */
    int updateByExample(@Param("row") FollowUpRecordsHistory row, @Param("example") FollowUpRecordsHistoryExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table follow_up_records_history
     *
     * @mbg.generated Thu Dec 26 15:31:06 CST 2024
     */
    int updateByPrimaryKeySelective(FollowUpRecordsHistoryWithBLOBs row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table follow_up_records_history
     *
     * @mbg.generated Thu Dec 26 15:31:06 CST 2024
     */
    int updateByPrimaryKeyWithBLOBs(FollowUpRecordsHistoryWithBLOBs row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table follow_up_records_history
     *
     * @mbg.generated Thu Dec 26 15:31:06 CST 2024
     */
    int updateByPrimaryKey(FollowUpRecordsHistory row);
}