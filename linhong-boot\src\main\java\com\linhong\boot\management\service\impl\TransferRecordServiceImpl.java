package com.linhong.boot.management.service.impl;

import java.time.LocalDateTime;
import java.util.List;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.linhong.boot.management.mapper.TransferRecordsMapper;
import com.linhong.boot.management.model.entity.TransferRecords;
import com.linhong.boot.management.model.query.CcUserQuery;
import com.linhong.boot.management.model.vo.TransferRecordsVO;
import com.linhong.boot.management.service.TransferRecordsService;

import lombok.RequiredArgsConstructor;

/**
 * 分配历史记录
 */
@Service
@RequiredArgsConstructor
public class TransferRecordServiceImpl extends ServiceImpl<TransferRecordsMapper, TransferRecords> implements TransferRecordsService {
	
	
	
	@Override
	public TransferRecords saveTransFerRecord(Long ccId, String sourceUserName ,String sourceDept ,String targetUserName ,String targetDept, String operatorName,String assignmentType) {
		LocalDateTime now = LocalDateTime.now();
		TransferRecords records = new TransferRecords();
		records.setId(ccId);
		records.setSourceOrganization(sourceDept);// 源组织
		records.setSourceUser(sourceUserName);//源用户
		records.setTargetOrganization(targetDept);//目标组织
		records.setTargetUser(targetUserName);//目标用户
		records.setOperator(operatorName);//操作人
		records.setOperationTime(now);//操作时间
		records.setAssignmentType(assignmentType);//分派类型，0:案池分派 1:换单 2:锁定 3:主管分派
		return records;
		
	}

	@Override
	public List<TransferRecords> selectUserOrDept(List<String> list) {
		return this.baseMapper.selectUserOrDept(list);
	}

	@Override
	public Page<TransferRecordsVO> selectTransPage(CcUserQuery query) {
		Page<TransferRecordsVO> page = new Page<>(query.getPageNum(), query.getPageSize());
		Page<TransferRecordsVO> reList = this.baseMapper.selectTransPage(page, query);
		return reList;
	}
	
	


}
