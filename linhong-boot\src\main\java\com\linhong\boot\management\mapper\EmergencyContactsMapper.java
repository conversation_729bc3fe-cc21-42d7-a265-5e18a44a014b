package com.linhong.boot.management.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.linhong.boot.management.model.entity.EmergencyContacts;
import com.linhong.boot.management.model.entity.EmergencyContactsExample;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface EmergencyContactsMapper extends BaseMapper<EmergencyContacts>{
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table emergency_contacts
     *
     * @mbg.generated Tue Nov 26 07:16:48 HKT 2024
     */
    long countByExample(EmergencyContactsExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table emergency_contacts
     *
     * @mbg.generated Tue Nov 26 07:16:48 HKT 2024
     */
    int deleteByExample(EmergencyContactsExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table emergency_contacts
     *
     * @mbg.generated Tue Nov 26 07:16:48 HKT 2024
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table emergency_contacts
     *
     * @mbg.generated Tue Nov 26 07:16:48 HKT 2024
     */
    int insert(EmergencyContacts row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table emergency_contacts
     *
     * @mbg.generated Tue Nov 26 07:16:48 HKT 2024
     */
    int insertSelective(EmergencyContacts row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table emergency_contacts
     *
     * @mbg.generated Tue Nov 26 07:16:48 HKT 2024
     */
    List<EmergencyContacts> selectByExample(EmergencyContactsExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table emergency_contacts
     *
     * @mbg.generated Tue Nov 26 07:16:48 HKT 2024
     */
    EmergencyContacts selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table emergency_contacts
     *
     * @mbg.generated Tue Nov 26 07:16:48 HKT 2024
     */
    int updateByExampleSelective(@Param("row") EmergencyContacts row, @Param("example") EmergencyContactsExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table emergency_contacts
     *
     * @mbg.generated Tue Nov 26 07:16:48 HKT 2024
     */
    int updateByExample(@Param("row") EmergencyContacts row, @Param("example") EmergencyContactsExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table emergency_contacts
     *
     * @mbg.generated Tue Nov 26 07:16:48 HKT 2024
     */
    int updateByPrimaryKeySelective(EmergencyContacts row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table emergency_contacts
     *
     * @mbg.generated Tue Nov 26 07:16:48 HKT 2024
     */
    int updateByPrimaryKey(EmergencyContacts row);
}