package com.linhong.boot.management.model.entity;

import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;


public class CcUser {
	
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.id
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 
	 */
	private Long id;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.customer_name
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	private String customerName;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.cardholder_id_number
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	private String cardholderIdNumber;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.cardholder_code
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	private String cardholderCode;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.customer_index_number
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	private String customerIndexNumber;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.entrust_amount
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	private String entrustAmount;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.entrust_total_amount
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	private BigDecimal entrustTotalAmount;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.entrust_principal
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	private String entrustPrincipal;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.entrust_principal_total
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	private BigDecimal entrustPrincipalTotal;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.entrust_overdue_period
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	private String entrustOverduePeriod;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.target_period
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	private String targetPeriod;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.case_city
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	private String caseCity;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.last_follow_up_date
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	private Date lastFollowUpDate;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.entrust_start_date
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	@JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
	private Date entrustStartDate;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.entrust_end_date
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	@JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
	private Date entrustEndDate;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.case_type
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	private String caseType;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.new_old_case_flag
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	private String newOldCaseFlag;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.batch_number
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	private String batchNumber;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.balance_ops
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	private BigDecimal balanceOps;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.principal_ops
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	private BigDecimal principalOps;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.current_overdue_period
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	private String currentOverduePeriod;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.household_city
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	private String householdCity;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.customer_age
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	private Integer customerAge;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.occupation
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	private String occupation;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.card_opening_date
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	@JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
	private Date cardOpeningDate;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.credit_limit
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	private BigDecimal creditLimit;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.last_payment_date
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	@JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
	private Date lastPaymentDate;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.skill_group
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	private String skillGroup;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.account_number_last7
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	private String accountNumberLast7;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.entrust_age_bracket
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	private String entrustAgeBracket;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.entrust_amount_bracket
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	private String entrustAmountBracket;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.entrust_commission_bracket
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	private String entrustCommissionBracket;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.rating_bracket
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	private String ratingBracket;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.is_litigation
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	private String isLitigation;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.customer_current_month_repayment
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	private BigDecimal customerCurrentMonthRepayment;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.customer_previous_day_repayment
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	private BigDecimal customerPreviousDayRepayment;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.case_grabbing_user
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	private String caseGrabbingUser;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.special_project_type
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	private String specialProjectType;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.retention_count
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	private Integer retentionCount;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.personalized_installment_status
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	private String personalizedInstallmentStatus;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.personalized_installment_fulfillment_status
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	private String personalizedInstallmentFulfillmentStatus;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.complaint_label
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	private String complaintLabel;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.personalized_fulfillment_label
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	private String personalizedFulfillmentLabel;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.litigation_label
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	private String litigationLabel;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.smart_voice_label
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	private String smartVoiceLabel;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.special_project_label
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	private String specialProjectLabel;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.is_litigation_for_commission
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	private String isLitigationForCommission;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.case_agency
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	private String caseAgency;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column cc_user.follow_up_status
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	private String followUpStatus;
	/* 催收人/组织 */
	private String userOrDept;
	/*外包序号*/
	private String outsourceSerialNumber;
	/*是否标记换单*/
	private String isMarkChange;
	/*失联查询结果*/
	private String lostContactQueryResult;
	/*未跟进天数*/
	private int unfollowedDays;
	/*工作单位*/
	private String workUnit;
	/*银行卡号*/
	private String bankCardNumber;
	/*是否锁定 0:否 1:是  默认否*/
	private String isLock;
	/*原催收员*/
	@TableField(exist = false)
	private String oldUserOrDept;


	
	public String getOldUserOrDept() {
		return oldUserOrDept;
	}

	public void setOldUserOrDept(String oldUserOrDept) {
		this.oldUserOrDept = oldUserOrDept;
	}

	public String getIsLock() {
		return isLock;
	}

	public void setIsLock(String isLock) {
		this.isLock = isLock;
	}

	public String getWorkUnit() {
		return workUnit;
	}

	public void setWorkUnit(String workUnit) {
		this.workUnit = workUnit;
	}

	public String getBankCardNumber() {
		return bankCardNumber;
	}

	public void setBankCardNumber(String bankCardNumber) {
		this.bankCardNumber = bankCardNumber;
	}

	public String getUserOrDept() {
		return userOrDept;
	}

	public void setUserOrDept(String userOrDept) {
		this.userOrDept = userOrDept;
	}

	public String getOutsourceSerialNumber() {
		return outsourceSerialNumber;
	}

	public void setOutsourceSerialNumber(String outsourceSerialNumber) {
		this.outsourceSerialNumber = outsourceSerialNumber;
	}

	public String getIsMarkChange() {
		return isMarkChange;
	}

	public void setIsMarkChange(String isMarkChange) {
		this.isMarkChange = isMarkChange;
	}

	public String getLostContactQueryResult() {
		return lostContactQueryResult;
	}

	public void setLostContactQueryResult(String lostContactQueryResult) {
		this.lostContactQueryResult = lostContactQueryResult;
	}

	public int getUnfollowedDays() {
		return unfollowedDays;
	}

	public void setUnfollowedDays(int unfollowedDays) {
		this.unfollowedDays = unfollowedDays;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column cc_user.id
	 * @return  the value of cc_user.id
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public Long getId() {
		return id;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column cc_user.id
	 * @param id  the value for cc_user.id
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public void setId(Long id) {
		this.id = id;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column cc_user.customer_name
	 * @return  the value of cc_user.customer_name
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public String getCustomerName() {
		return customerName;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column cc_user.customer_name
	 * @param customerName  the value for cc_user.customer_name
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public void setCustomerName(String customerName) {
		this.customerName = customerName == null ? null : customerName.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column cc_user.cardholder_id_number
	 * @return  the value of cc_user.cardholder_id_number
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public String getCardholderIdNumber() {
		return cardholderIdNumber;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column cc_user.cardholder_id_number
	 * @param cardholderIdNumber  the value for cc_user.cardholder_id_number
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public void setCardholderIdNumber(String cardholderIdNumber) {
		this.cardholderIdNumber = cardholderIdNumber == null ? null : cardholderIdNumber.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column cc_user.cardholder_code
	 * @return  the value of cc_user.cardholder_code
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public String getCardholderCode() {
		return cardholderCode;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column cc_user.cardholder_code
	 * @param cardholderCode  the value for cc_user.cardholder_code
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public void setCardholderCode(String cardholderCode) {
		this.cardholderCode = cardholderCode == null ? null : cardholderCode.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column cc_user.customer_index_number
	 * @return  the value of cc_user.customer_index_number
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public String getCustomerIndexNumber() {
		return customerIndexNumber;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column cc_user.customer_index_number
	 * @param customerIndexNumber  the value for cc_user.customer_index_number
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public void setCustomerIndexNumber(String customerIndexNumber) {
		this.customerIndexNumber = customerIndexNumber == null ? null : customerIndexNumber.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column cc_user.entrust_amount
	 * @return  the value of cc_user.entrust_amount
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public String getEntrustAmount() {
		return entrustAmount;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column cc_user.entrust_amount
	 * @param entrustAmount  the value for cc_user.entrust_amount
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public void setEntrustAmount(String entrustAmount) {
		this.entrustAmount = entrustAmount;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column cc_user.entrust_total_amount
	 * @return  the value of cc_user.entrust_total_amount
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public BigDecimal getEntrustTotalAmount() {
		return entrustTotalAmount;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column cc_user.entrust_total_amount
	 * @param entrustTotalAmount  the value for cc_user.entrust_total_amount
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public void setEntrustTotalAmount(BigDecimal entrustTotalAmount) {
		this.entrustTotalAmount = entrustTotalAmount;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column cc_user.entrust_principal
	 * @return  the value of cc_user.entrust_principal
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public String getEntrustPrincipal() {
		return entrustPrincipal;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column cc_user.entrust_principal
	 * @param entrustPrincipal  the value for cc_user.entrust_principal
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public void setEntrustPrincipal(String entrustPrincipal) {
		this.entrustPrincipal = entrustPrincipal;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column cc_user.entrust_principal_total
	 * @return  the value of cc_user.entrust_principal_total
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public BigDecimal getEntrustPrincipalTotal() {
		return entrustPrincipalTotal;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column cc_user.entrust_principal_total
	 * @param entrustPrincipalTotal  the value for cc_user.entrust_principal_total
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public void setEntrustPrincipalTotal(BigDecimal entrustPrincipalTotal) {
		this.entrustPrincipalTotal = entrustPrincipalTotal;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column cc_user.entrust_overdue_period
	 * @return  the value of cc_user.entrust_overdue_period
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public String getEntrustOverduePeriod() {
		return entrustOverduePeriod;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column cc_user.entrust_overdue_period
	 * @param entrustOverduePeriod  the value for cc_user.entrust_overdue_period
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public void setEntrustOverduePeriod(String entrustOverduePeriod) {
		this.entrustOverduePeriod = entrustOverduePeriod == null ? null : entrustOverduePeriod.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column cc_user.target_period
	 * @return  the value of cc_user.target_period
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public String getTargetPeriod() {
		return targetPeriod;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column cc_user.target_period
	 * @param targetPeriod  the value for cc_user.target_period
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public void setTargetPeriod(String targetPeriod) {
		this.targetPeriod = targetPeriod == null ? null : targetPeriod.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column cc_user.case_city
	 * @return  the value of cc_user.case_city
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public String getCaseCity() {
		return caseCity;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column cc_user.case_city
	 * @param caseCity  the value for cc_user.case_city
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public void setCaseCity(String caseCity) {
		this.caseCity = caseCity == null ? null : caseCity.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column cc_user.last_follow_up_date
	 * @return  the value of cc_user.last_follow_up_date
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public Date getLastFollowUpDate() {
		return lastFollowUpDate;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column cc_user.last_follow_up_date
	 * @param lastFollowUpDate  the value for cc_user.last_follow_up_date
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public void setLastFollowUpDate(Date lastFollowUpDate) {
		this.lastFollowUpDate = lastFollowUpDate;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column cc_user.entrust_start_date
	 * @return  the value of cc_user.entrust_start_date
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public Date getEntrustStartDate() {
		return entrustStartDate;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column cc_user.entrust_start_date
	 * @param entrustStartDate  the value for cc_user.entrust_start_date
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public void setEntrustStartDate(Date entrustStartDate) {
		this.entrustStartDate = entrustStartDate;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column cc_user.entrust_end_date
	 * @return  the value of cc_user.entrust_end_date
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public Date getEntrustEndDate() {
		return entrustEndDate;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column cc_user.entrust_end_date
	 * @param entrustEndDate  the value for cc_user.entrust_end_date
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public void setEntrustEndDate(Date entrustEndDate) {
		this.entrustEndDate = entrustEndDate;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column cc_user.case_type
	 * @return  the value of cc_user.case_type
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public String getCaseType() {
		return caseType;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column cc_user.case_type
	 * @param caseType  the value for cc_user.case_type
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public void setCaseType(String caseType) {
		this.caseType = caseType == null ? null : caseType.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column cc_user.new_old_case_flag
	 * @return  the value of cc_user.new_old_case_flag
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public String getNewOldCaseFlag() {
		return newOldCaseFlag;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column cc_user.new_old_case_flag
	 * @param newOldCaseFlag  the value for cc_user.new_old_case_flag
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public void setNewOldCaseFlag(String newOldCaseFlag) {
		this.newOldCaseFlag = newOldCaseFlag == null ? null : newOldCaseFlag.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column cc_user.batch_number
	 * @return  the value of cc_user.batch_number
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public String getBatchNumber() {
		return batchNumber;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column cc_user.batch_number
	 * @param batchNumber  the value for cc_user.batch_number
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public void setBatchNumber(String batchNumber) {
		this.batchNumber = batchNumber == null ? null : batchNumber.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column cc_user.balance_ops
	 * @return  the value of cc_user.balance_ops
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public BigDecimal getBalanceOps() {
		return balanceOps;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column cc_user.balance_ops
	 * @param balanceOps  the value for cc_user.balance_ops
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public void setBalanceOps(BigDecimal balanceOps) {
		this.balanceOps = balanceOps;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column cc_user.principal_ops
	 * @return  the value of cc_user.principal_ops
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public BigDecimal getPrincipalOps() {
		return principalOps;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column cc_user.principal_ops
	 * @param principalOps  the value for cc_user.principal_ops
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public void setPrincipalOps(BigDecimal principalOps) {
		this.principalOps = principalOps;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column cc_user.current_overdue_period
	 * @return  the value of cc_user.current_overdue_period
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public String getCurrentOverduePeriod() {
		return currentOverduePeriod;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column cc_user.current_overdue_period
	 * @param currentOverduePeriod  the value for cc_user.current_overdue_period
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public void setCurrentOverduePeriod(String currentOverduePeriod) {
		this.currentOverduePeriod = currentOverduePeriod == null ? null : currentOverduePeriod.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column cc_user.household_city
	 * @return  the value of cc_user.household_city
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public String getHouseholdCity() {
		return householdCity;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column cc_user.household_city
	 * @param householdCity  the value for cc_user.household_city
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public void setHouseholdCity(String householdCity) {
		this.householdCity = householdCity == null ? null : householdCity.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column cc_user.customer_age
	 * @return  the value of cc_user.customer_age
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public Integer getCustomerAge() {
		return customerAge;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column cc_user.customer_age
	 * @param customerAge  the value for cc_user.customer_age
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public void setCustomerAge(Integer customerAge) {
		this.customerAge = customerAge;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column cc_user.occupation
	 * @return  the value of cc_user.occupation
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public String getOccupation() {
		return occupation;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column cc_user.occupation
	 * @param occupation  the value for cc_user.occupation
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public void setOccupation(String occupation) {
		this.occupation = occupation == null ? null : occupation.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column cc_user.card_opening_date
	 * @return  the value of cc_user.card_opening_date
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public Date getCardOpeningDate() {
		return cardOpeningDate;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column cc_user.card_opening_date
	 * @param cardOpeningDate  the value for cc_user.card_opening_date
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public void setCardOpeningDate(Date cardOpeningDate) {
		this.cardOpeningDate = cardOpeningDate;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column cc_user.credit_limit
	 * @return  the value of cc_user.credit_limit
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public BigDecimal getCreditLimit() {
		return creditLimit;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column cc_user.credit_limit
	 * @param creditLimit  the value for cc_user.credit_limit
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public void setCreditLimit(BigDecimal creditLimit) {
		this.creditLimit = creditLimit;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column cc_user.last_payment_date
	 * @return  the value of cc_user.last_payment_date
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public Date getLastPaymentDate() {
		return lastPaymentDate;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column cc_user.last_payment_date
	 * @param lastPaymentDate  the value for cc_user.last_payment_date
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public void setLastPaymentDate(Date lastPaymentDate) {
		this.lastPaymentDate = lastPaymentDate;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column cc_user.skill_group
	 * @return  the value of cc_user.skill_group
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public String getSkillGroup() {
		return skillGroup;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column cc_user.skill_group
	 * @param skillGroup  the value for cc_user.skill_group
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public void setSkillGroup(String skillGroup) {
		this.skillGroup = skillGroup == null ? null : skillGroup.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column cc_user.account_number_last7
	 * @return  the value of cc_user.account_number_last7
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public String getAccountNumberLast7() {
		return accountNumberLast7;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column cc_user.account_number_last7
	 * @param accountNumberLast7  the value for cc_user.account_number_last7
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public void setAccountNumberLast7(String accountNumberLast7) {
		this.accountNumberLast7 = accountNumberLast7 == null ? null : accountNumberLast7.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column cc_user.entrust_age_bracket
	 * @return  the value of cc_user.entrust_age_bracket
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public String getEntrustAgeBracket() {
		return entrustAgeBracket;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column cc_user.entrust_age_bracket
	 * @param entrustAgeBracket  the value for cc_user.entrust_age_bracket
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public void setEntrustAgeBracket(String entrustAgeBracket) {
		this.entrustAgeBracket = entrustAgeBracket == null ? null : entrustAgeBracket.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column cc_user.entrust_amount_bracket
	 * @return  the value of cc_user.entrust_amount_bracket
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public String getEntrustAmountBracket() {
		return entrustAmountBracket;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column cc_user.entrust_amount_bracket
	 * @param entrustAmountBracket  the value for cc_user.entrust_amount_bracket
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public void setEntrustAmountBracket(String entrustAmountBracket) {
		this.entrustAmountBracket = entrustAmountBracket == null ? null : entrustAmountBracket.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column cc_user.entrust_commission_bracket
	 * @return  the value of cc_user.entrust_commission_bracket
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public String getEntrustCommissionBracket() {
		return entrustCommissionBracket;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column cc_user.entrust_commission_bracket
	 * @param entrustCommissionBracket  the value for cc_user.entrust_commission_bracket
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public void setEntrustCommissionBracket(String entrustCommissionBracket) {
		this.entrustCommissionBracket = entrustCommissionBracket == null ? null : entrustCommissionBracket.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column cc_user.rating_bracket
	 * @return  the value of cc_user.rating_bracket
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public String getRatingBracket() {
		return ratingBracket;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column cc_user.rating_bracket
	 * @param ratingBracket  the value for cc_user.rating_bracket
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public void setRatingBracket(String ratingBracket) {
		this.ratingBracket = ratingBracket == null ? null : ratingBracket.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column cc_user.is_litigation
	 * @return  the value of cc_user.is_litigation
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public String getIsLitigation() {
		return isLitigation;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column cc_user.is_litigation
	 * @param isLitigation  the value for cc_user.is_litigation
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public void setIsLitigation(String isLitigation) {
		this.isLitigation = isLitigation == null ? null : isLitigation.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column cc_user.customer_current_month_repayment
	 * @return  the value of cc_user.customer_current_month_repayment
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public BigDecimal getCustomerCurrentMonthRepayment() {
		return customerCurrentMonthRepayment;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column cc_user.customer_current_month_repayment
	 * @param customerCurrentMonthRepayment  the value for cc_user.customer_current_month_repayment
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public void setCustomerCurrentMonthRepayment(BigDecimal customerCurrentMonthRepayment) {
		this.customerCurrentMonthRepayment = customerCurrentMonthRepayment;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column cc_user.customer_previous_day_repayment
	 * @return  the value of cc_user.customer_previous_day_repayment
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public BigDecimal getCustomerPreviousDayRepayment() {
		return customerPreviousDayRepayment;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column cc_user.customer_previous_day_repayment
	 * @param customerPreviousDayRepayment  the value for cc_user.customer_previous_day_repayment
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public void setCustomerPreviousDayRepayment(BigDecimal customerPreviousDayRepayment) {
		this.customerPreviousDayRepayment = customerPreviousDayRepayment;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column cc_user.case_grabbing_user
	 * @return  the value of cc_user.case_grabbing_user
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public String getCaseGrabbingUser() {
		return caseGrabbingUser;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column cc_user.case_grabbing_user
	 * @param caseGrabbingUser  the value for cc_user.case_grabbing_user
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public void setCaseGrabbingUser(String caseGrabbingUser) {
		this.caseGrabbingUser = caseGrabbingUser == null ? null : caseGrabbingUser.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column cc_user.special_project_type
	 * @return  the value of cc_user.special_project_type
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public String getSpecialProjectType() {
		return specialProjectType;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column cc_user.special_project_type
	 * @param specialProjectType  the value for cc_user.special_project_type
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public void setSpecialProjectType(String specialProjectType) {
		this.specialProjectType = specialProjectType == null ? null : specialProjectType.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column cc_user.retention_count
	 * @return  the value of cc_user.retention_count
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public Integer getRetentionCount() {
		return retentionCount;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column cc_user.retention_count
	 * @param retentionCount  the value for cc_user.retention_count
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public void setRetentionCount(Integer retentionCount) {
		this.retentionCount = retentionCount;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column cc_user.personalized_installment_status
	 * @return  the value of cc_user.personalized_installment_status
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public String getPersonalizedInstallmentStatus() {
		return personalizedInstallmentStatus;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column cc_user.personalized_installment_status
	 * @param personalizedInstallmentStatus  the value for cc_user.personalized_installment_status
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public void setPersonalizedInstallmentStatus(String personalizedInstallmentStatus) {
		this.personalizedInstallmentStatus = personalizedInstallmentStatus == null ? null
				: personalizedInstallmentStatus.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column cc_user.personalized_installment_fulfillment_status
	 * @return  the value of cc_user.personalized_installment_fulfillment_status
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public String getPersonalizedInstallmentFulfillmentStatus() {
		return personalizedInstallmentFulfillmentStatus;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column cc_user.personalized_installment_fulfillment_status
	 * @param personalizedInstallmentFulfillmentStatus  the value for cc_user.personalized_installment_fulfillment_status
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public void setPersonalizedInstallmentFulfillmentStatus(String personalizedInstallmentFulfillmentStatus) {
		this.personalizedInstallmentFulfillmentStatus = personalizedInstallmentFulfillmentStatus == null ? null
				: personalizedInstallmentFulfillmentStatus.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column cc_user.complaint_label
	 * @return  the value of cc_user.complaint_label
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public String getComplaintLabel() {
		return complaintLabel;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column cc_user.complaint_label
	 * @param complaintLabel  the value for cc_user.complaint_label
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public void setComplaintLabel(String complaintLabel) {
		this.complaintLabel = complaintLabel == null ? null : complaintLabel.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column cc_user.personalized_fulfillment_label
	 * @return  the value of cc_user.personalized_fulfillment_label
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public String getPersonalizedFulfillmentLabel() {
		return personalizedFulfillmentLabel;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column cc_user.personalized_fulfillment_label
	 * @param personalizedFulfillmentLabel  the value for cc_user.personalized_fulfillment_label
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public void setPersonalizedFulfillmentLabel(String personalizedFulfillmentLabel) {
		this.personalizedFulfillmentLabel = personalizedFulfillmentLabel == null ? null
				: personalizedFulfillmentLabel.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column cc_user.litigation_label
	 * @return  the value of cc_user.litigation_label
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public String getLitigationLabel() {
		return litigationLabel;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column cc_user.litigation_label
	 * @param litigationLabel  the value for cc_user.litigation_label
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public void setLitigationLabel(String litigationLabel) {
		this.litigationLabel = litigationLabel == null ? null : litigationLabel.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column cc_user.smart_voice_label
	 * @return  the value of cc_user.smart_voice_label
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public String getSmartVoiceLabel() {
		return smartVoiceLabel;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column cc_user.smart_voice_label
	 * @param smartVoiceLabel  the value for cc_user.smart_voice_label
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public void setSmartVoiceLabel(String smartVoiceLabel) {
		this.smartVoiceLabel = smartVoiceLabel == null ? null : smartVoiceLabel.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column cc_user.special_project_label
	 * @return  the value of cc_user.special_project_label
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public String getSpecialProjectLabel() {
		return specialProjectLabel;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column cc_user.special_project_label
	 * @param specialProjectLabel  the value for cc_user.special_project_label
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public void setSpecialProjectLabel(String specialProjectLabel) {
		this.specialProjectLabel = specialProjectLabel == null ? null : specialProjectLabel.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column cc_user.is_litigation_for_commission
	 * @return  the value of cc_user.is_litigation_for_commission
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public String getIsLitigationForCommission() {
		return isLitigationForCommission;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column cc_user.is_litigation_for_commission
	 * @param isLitigationForCommission  the value for cc_user.is_litigation_for_commission
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public void setIsLitigationForCommission(String isLitigationForCommission) {
		this.isLitigationForCommission = isLitigationForCommission == null ? null : isLitigationForCommission.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column cc_user.case_agencyâ€Œ
	 * @return  the value of cc_user.case_agencyâ€Œ
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public String getCaseAgency() {
		return caseAgency;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column cc_user.case_agencyâ€Œ
	 * @param caseAgencyâ€œ  the value for cc_user.case_agencyâ€Œ
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public void setCaseAgency(String caseAgency) {
		this.caseAgency = caseAgency == null ? null : caseAgency.trim();
	}

	/**
	 * 0:新案
	 * 1:继续跟进
	 * 2:重点跟进
	 * 3:谈方案
	 * 4:线下分期
	 * 5:个性化分期
	 * 6:待减免
	 * 7:要求退案
	 * 8:保留案件
	 * 9:投诉倾向
	 * 10:案池
	 */
	public String getFollowUpStatus() {
		return followUpStatus;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column cc_user.follow_up_status
	 * @param followUpStatus  the value for cc_user.follow_up_status
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public void setFollowUpStatus(String followUpStatus) {
		this.followUpStatus = followUpStatus == null ? null : followUpStatus.trim();
	}
}