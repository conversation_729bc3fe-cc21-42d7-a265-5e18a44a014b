<template>
  <div class="dashboard-container">
    <!-- 顶部统计卡片 -->
    <StatisticCards
      :cards="statisticCards"
      :loading="statisticLoading"
      @refresh="handleRefreshStatistics"
    />

    <!-- 中间内容区域 -->
    <el-row :gutter="20" class="content-row">
      <el-col :xs="24" :lg="12">
        <EmployeeRanking
          :ranking-list="employeeRanking"
          :loading="rankingLoading"
          @period-change="handleRankingPeriodChange"
        />
      </el-col>
      <el-col :xs="24" :lg="12">
        <TeamLevelChart
          :level-data="teamLevelData"
          :loading="teamLevelLoading"
        />
      </el-col>
    </el-row>

    <!-- 底部图表区域 -->
    <el-row :gutter="20" class="chart-row">
      <el-col :xs="24" :lg="12">
        <RepaymentTrend
          :chart-data="repaymentTrendData"
          :loading="trendLoading"
          @period-change="handleTrendPeriodChange"
          @days-change="handleDateRangeChange"
        />
      </el-col>
      <el-col :xs="24" :lg="12">
        <CaseDistribution
          :distribution-data="caseDistributionData"
          :loading="distributionLoading"
          @type-change="handleDistributionTypeChange"
        />
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: "Dashboard",
  inheritAttrs: false,
});

// 导入组件
import StatisticCards from "./components/StatisticCards.vue";
import EmployeeRanking from "./components/EmployeeRanking.vue";
import TeamLevelChart from "./components/TeamLevelChart.vue";
import RepaymentTrend from "./components/RepaymentTrend.vue";
import CaseDistribution from "./components/CaseDistribution.vue";

// 导入Vue相关
import { ref, onMounted } from "vue";
import { ElMessage } from "element-plus";

// 导入API和类型
import dashboardAPI, {
  type StatisticCard,
  type RankingItem,
  type TeamLevelItem,
  type ChartData,
  type CaseDistributionItem,
} from "@/api/dataSystem/dashboard";
import statementAPI from "@/api/dataSystem/statement";

// 导入用户store
import { useUserStore } from "@/store/modules/user";
import { formatNumber } from "@/utils/format";

// 响应式数据
const statisticCards = ref<StatisticCard[]>([]);
const employeeRanking = ref<RankingItem[]>([]);
const teamLevelData = ref<TeamLevelItem[]>([]);
const repaymentTrendData = ref<ChartData>({ xAxis: [], series: [] });
const caseDistributionData = ref<CaseDistributionItem[]>([]);

// 用户store
const userStore = useUserStore();

// 加载状态
const statisticLoading = ref(true);
const rankingLoading = ref(true);
const teamLevelLoading = ref(true);
const trendLoading = ref(true);
const distributionLoading = ref(true);

// 数据加载函数
const loadStatisticCards = async () => {
  try {
    statisticLoading.value = true;

    // 获取当前用户ID
    const currentUserId = userStore.userInfo?.userId?.toString() || "58";

    // 并行获取总案件数、总委托金额和总还款金额统计
    const [caseCountResult, entrustAmountResult, repaymentAmountResult] = await Promise.all([
      dashboardAPI.getTotalCaseCount(currentUserId),
      dashboardAPI.getTotalEntrustAmount(currentUserId),
      dashboardAPI.getTotalRepaymentAmount(currentUserId)
    ]);

    // 格式化数字显示
    const formatNumber = (num: number, isAmount = false) => {
      if (isAmount) {
        // 金额格式化：显示原始数值，保留两位小数，添加千分位分隔符
        return num.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
      } else {
        // 案件数格式化
        if (num >= 10000) {
          return (num / 10000).toFixed(1) + "W";
        } else if (num >= 1000) {
          return (num / 1000).toFixed(1) + "K";
        }
        return num.toLocaleString();
      }
    };

    // 使用实际数据或默认数据
    const totalCasesDisplay = caseCountResult.success
      ? formatNumber(caseCountResult.totalCount)
      : "2,587";

    const totalAmountDisplay = entrustAmountResult.success
      ? formatNumber(entrustAmountResult.totalAmount, true)
      : "8.74";

    // 计算任务回收 = 总在案金额 * 0.02
    const taskRecoveryAmount = entrustAmountResult.success
      ? entrustAmountResult.totalAmount * 0.02
      : 356.8;

    const taskRecoveryDisplay = entrustAmountResult.success
      ? formatNumber(taskRecoveryAmount, true)
      : "356.8";

    // 计算回收率 = 总还款金额 / 任务回收 * 100%
    const recoveryRate = (entrustAmountResult.success && repaymentAmountResult.success && taskRecoveryAmount > 0)
        ? (repaymentAmountResult.totalRepaymentAmount / taskRecoveryAmount) * 100
        : 74.6;

    const recoveryRateDisplay = (entrustAmountResult.success && repaymentAmountResult.success)
        ? recoveryRate.toFixed(2)
        : "74.6";

    statisticCards.value = [
      {
        title: "总案件数",
        value: totalCasesDisplay,
        icon: "users",
        color: "#4c5b7a",
        trend: { value: 12.5, type: "up" },
      },
      {
        title: "总金额",
        value: entrustAmountResult.success ? `¥${totalAmountDisplay}` : "¥8.74",
        unit: entrustAmountResult.success ? "" : "M",
        icon: "folder",
        color: "#2563eb",
        trend: { value: 8.2, type: "up" },
      },
      {
        title: "任务回收",
        value: entrustAmountResult.success ? `¥${taskRecoveryDisplay}` : "¥356.8",
        unit: entrustAmountResult.success ? "" : "K",
        icon: "trending-up",
        color: "#0ea5e9",
        trend: { value: 13.2, type: "down" },
      },
      {
        title: "平均回收率",
        value: recoveryRateDisplay,
        unit: "%",
        icon: "chart",
        color: "#7c3aed",
        trend: { value: 2.3, type: "up" },
      },
    ];

    // 输出统计信息
    if (caseCountResult.success && entrustAmountResult.success) {
      console.log(`
        用户信息:
        - 当前用户ID: ${currentUserId}
        - 用户名: ${userStore.userInfo?.username || '未知'}

        案件统计:
        - 案池户数: ${caseCountResult.casepoolCount}
        - 作业清单户数: ${caseCountResult.workPageCount}
        - 总案件数: ${caseCountResult.totalCount}

        金额统计:
        - 案池委托金额: ¥${entrustAmountResult.casepoolAmount.toLocaleString()} (类型: ${typeof entrustAmountResult.casepoolAmount})
        - 作业清单委托金额: ¥${entrustAmountResult.workPageAmount.toLocaleString()} (类型: ${typeof entrustAmountResult.workPageAmount})
        - 总委托金额: ¥${entrustAmountResult.totalAmount.toLocaleString()} (类型: ${typeof entrustAmountResult.totalAmount})
        - 任务回收金额: ¥${taskRecoveryAmount.toLocaleString()} (总金额 * 0.02)

        回收率统计:
        - 总还款金额: ¥${repaymentAmountResult.success ? repaymentAmountResult.totalRepaymentAmount.toLocaleString() : '获取失败'}
        - 回收率: ${recoveryRateDisplay}% (还款金额 / 任务回收 * 100)
        - 计算公式: ${repaymentAmountResult.success ? repaymentAmountResult.totalRepaymentAmount : 0} / ${taskRecoveryAmount} * 100 = ${recoveryRateDisplay}%

        - 计算验证: ${entrustAmountResult.casepoolAmount} + ${entrustAmountResult.workPageAmount} = ${entrustAmountResult.casepoolAmount + entrustAmountResult.workPageAmount}
      `);
    } else {
      if (!caseCountResult.success) {
        console.warn("获取案件数统计失败，使用默认数据");
      }
      if (!entrustAmountResult.success) {
        console.warn("获取委托金额统计失败，使用默认数据");
      }
      if (!repaymentAmountResult.success) {
        console.warn("获取还款金额统计失败，使用默认数据");
      }
    }
  } catch (error) {
    console.error("加载统计卡片数据失败:", error);
    // 如果出现异常，使用默认数据
    statisticCards.value = [
      {
        title: "总案件数",
        value: "2,587",
        icon: "users",
        color: "#4c5b7a",
        trend: { value: 12.5, type: "up" },
      },
      {
        title: "总金额",
        value: "¥8.74",
        unit: "M",
        icon: "folder",
        color: "#2563eb",
        trend: { value: 8.2, type: "up" },
      },
      {
        title: "今日回款",
        value: "¥356.8",
        unit: "K",
        icon: "trending-up",
        color: "#0ea5e9",
        trend: { value: 13.2, type: "down" },
      },
      {
        title: "平均回收率",
        value: "74.6",
        unit: "%",
        icon: "chart",
        color: "#7c3aed",
        trend: { value: 2.3, type: "up" },
      },
    ];
  } finally {
    statisticLoading.value = false;
  }
};

const loadEmployeeRanking = async (period: "personal" | "team" = "personal") => {
  try {
    rankingLoading.value = true;

    // 根据个人/团队选择调用不同的API
    let data;
    if (period === "personal") {
      // 调用本月个人排行榜API（按回收率排序）
      data = await statementAPI.reportNowMonthRank({ type: "personal" });
    } else {
      // 调用本月团队排行榜API（按回收率排序）
      data = await statementAPI.reportNowMonthRank({ type: "team" });
    }

    // 处理返回的数据
    // 由于响应拦截器已经处理了 {"code":"00000","data":[...],"msg":"一切ok"} 格式
    // 这里的data实际上就是原始响应中的data数组
    let responseData = null;
    if (Array.isArray(data)) {
      // 响应拦截器处理后，data直接就是数组
      responseData = data;
    } else if (data && Array.isArray(data.data)) {
      // 备用：如果响应拦截器没有处理
      responseData = data.data;
    }

    if (responseData && Array.isArray(responseData)) {
      const mappedData = responseData.map((item: any, index: number) => ({
        rank: item.rankSort || (index + 1), // 使用后端返回的排名或索引+1
        userName: item.userName || '未知',
        deptName: item.deptName || '未知部门',
        amount: parseInt(item.userPoints) || 0,
        recoveryRate: parseFloat(item.recoveryRate) || 0,
        entrustTotalAmount: parseInt(item.entrustTotalAmount) || 0,
      }));

      employeeRanking.value = mappedData;
    } else {
      employeeRanking.value = [];
    }
  } catch (error) {
    console.error('加载员工排行榜失败:', error);
    // API调用失败时显示空数据
    employeeRanking.value = [];
    // 可以显示错误提示
    ElMessage.error(`加载${period === 'personal' ? '个人' : '团队'}排行榜失败，请稍后重试`);
  } finally {
    rankingLoading.value = false;
  }
};

const loadTeamLevelData = async () => {
  try {
    teamLevelLoading.value = true;
    // 模拟数据 - 根据设计图的团队级别分布
    teamLevelData.value = [
      { level: "催收一组", count: 856, percentage: 73, color: "#4080ff" },
      { level: "催收二组", count: 732, percentage: 68, color: "#23c343" },
      { level: "催收三组", count: 654, percentage: 62, color: "#ff9a2e" },
      { level: "新客户组", count: 520, percentage: 45, color: "#9c88ff" },
    ];
    // const data = await dashboardAPI.getTeamLevelDistribution();
    // teamLevelData.value = data;
  } catch (error) {
    console.error("加载团队级别数据失败:", error);
  } finally {
    teamLevelLoading.value = false;
  }
};

const loadRepaymentTrend = async (
  period: "amount" | "count" | "recent" = "amount",
  days: number = 7
) => {
  try {
    trendLoading.value = true;

    // 获取当前用户ID
    const currentUserId = userStore.userInfo?.userId?.toString() || "58";

    // 计算日期范围
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(endDate.getDate() - (days - 1));

    const formatDate = (date: Date) => {
      return date.toISOString().split("T")[0];
    };

    try {
      const data = await dashboardAPI.getRepaymentTrend({
        startDate: formatDate(startDate),
        endDate: formatDate(endDate),
        period: period,
        operatorId: currentUserId,
      });

      // 检查数据是否有效，如果无效则使用模拟数据
      const xAxisData = data.xAxis || data.xaxis || [];
      if (!data || xAxisData.length === 0 || !data.series || data.series.length === 0) {
        console.log("使用模拟数据");
        repaymentTrendData.value = generateMockTrendData(period, days);
      } else {
        // 确保数据格式正确
        repaymentTrendData.value = {
          ...data,
          xAxis: xAxisData  // 统一使用 xAxis 字段名
        };
      }
    } catch (apiError) {
      console.log("API调用失败，使用模拟数据:", apiError);
      repaymentTrendData.value = generateMockTrendData(period, days);
    }
  } catch (error) {
    console.error("加载回款趋势数据失败:", error);
    ElMessage.error("加载回款趋势数据失败");
    // 即使出错也提供模拟数据
    repaymentTrendData.value = generateMockTrendData(period, days);
  } finally {
    trendLoading.value = false;
  }
};

const loadCaseDistribution = async (type: 'department' | 'personal' = 'department') => {
  try {
    distributionLoading.value = true;
    const data = await dashboardAPI.getCaseDistribution(type);

    // 处理返回的数据，转换为组件需要的格式
    caseDistributionData.value = data.map((item: any) => ({
      category: item.category,
      value: item.caseCount,
      amount: item.totalAmount,
      progress: item.percentage || 0, // 保留小数点
      color: item.color
    }));
  } catch (error) {
    console.error("加载案件分布数据失败:", error);
    ElMessage.error("加载案件分布数据失败");
    // 出错时显示空数据
    caseDistributionData.value = [];
  } finally {
    distributionLoading.value = false;
  }
};

// 事件处理函数
const handleRankingPeriodChange = (period: "personal" | "team") => {
  loadEmployeeRanking(period);
};



// 当前选择的分析类型和日期范围
const currentPeriod = ref<"amount" | "count" | "recent">("amount");
const currentDateRange = ref<number>(7); // 默认7天

// 生成模拟趋势数据
const generateMockTrendData = (period: "amount" | "count" | "recent", days: number): ChartData => {
  const endDate = new Date();
  const startDate = new Date();
  startDate.setDate(endDate.getDate() - (days - 1));

  const xAxis: string[] = [];
  const dates: string[] = [];

  // 生成日期数组
  for (let i = 0; i < days; i++) {
    const currentDate = new Date(startDate);
    currentDate.setDate(startDate.getDate() + i);

    const month = currentDate.getMonth() + 1;
    const day = currentDate.getDate();
    xAxis.push(`${month}/${day}`);
    dates.push(currentDate.toISOString().split('T')[0]);
  }

  const series: any[] = [];

  if (period === "amount") {
    // 金额数据 (万元)
    const amountData = Array.from({ length: days }, () =>
      Math.round((Math.random() * 50 + 10) * 100) / 100
    );
    series.push({
      name: "回款金额(万元)",
      data: amountData,
      type: "line",
      color: "#fbbf24"
    });
  } else if (period === "count") {
    // 户数数据
    const countData = Array.from({ length: days }, () =>
      Math.floor(Math.random() * 20 + 5)
    );
    series.push({
      name: "回款户数",
      data: countData,
      type: "line",
      color: "#4080ff"
    });
  } else if (period === "recent") {
    // 同时显示金额和户数
    const amountData = Array.from({ length: days }, () =>
      Math.round((Math.random() * 50 + 10) * 100) / 100
    );
    const countData = Array.from({ length: days }, () =>
      Math.floor(Math.random() * 20 + 5)
    );

    series.push({
      name: "回款金额(万元)",
      data: amountData,
      type: "line",
      color: "#fbbf24"
    });
    series.push({
      name: "回款户数",
      data: countData,
      type: "line",
      color: "#4080ff"
    });
  }

  return {
    xAxis,
    dates,
    series
  };
};

const handleTrendPeriodChange = (period: "amount" | "count" | "recent") => {
  currentPeriod.value = period;
  loadRepaymentTrend(period, currentDateRange.value);
};

const handleDateRangeChange = (days: number) => {
  currentDateRange.value = days;
  loadRepaymentTrend(currentPeriod.value, days);
};

const handleDistributionTypeChange = (type: "department" | "personal") => {
  loadCaseDistribution(type);
};

// 刷新所有数据
const handleRefreshStatistics = () => {
  initDashboard();
};

// 初始化数据
const initDashboard = async () => {
  await Promise.all([
    loadStatisticCards(),
    loadEmployeeRanking(),
    loadTeamLevelData(),
    loadRepaymentTrend(),
    loadCaseDistribution(),
  ]);
};

onMounted(() => {
  initDashboard();
});
</script>

<style lang="scss" scoped>
.dashboard-container {
  position: relative;
  padding: 24px;
  background: linear-gradient(135deg, #1e3a8a 0%, #2563eb 50%, #3b82f6 100%);
  min-height: calc(100vh - 50px);

  .content-row {
    margin-bottom: 24px;
  }

  .chart-row {
    margin-bottom: 24px;
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .dashboard-container {
    padding: 16px;
  }
}

@media (max-width: 768px) {
  .dashboard-container {
    padding: 12px;

    .content-row,
    .chart-row {
      margin-bottom: 16px;
    }
  }
}
</style>
