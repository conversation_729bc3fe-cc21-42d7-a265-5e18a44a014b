package com.linhong.boot.management.model.entity;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;

public class TaskRepaymentPredictions {
	
	private String ccId;
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column task_repayment_predictions.id
     *
     * @mbg.generated Tue Dec 17 01:34:51 CST 2024
     */
    private Integer id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column task_repayment_predictions.customer_name
     *
     * @mbg.generated Tue Dec 17 01:34:51 CST 2024
     */
    private String customerName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column task_repayment_predictions.customer_index
     *
     * @mbg.generated Tue Dec 17 01:34:51 CST 2024
     */
    private String customerIndexNumber;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column task_repayment_predictions.operator_id
     *
     * @mbg.generated Tue Dec 17 01:34:51 CST 2024
     */
    private String operatorId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column task_repayment_predictions.operator_name
     *
     * @mbg.generated Tue Dec 17 01:34:51 CST 2024
     */
    private String userOrDept;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column task_repayment_predictions.dept_id
     *
     * @mbg.generated Tue Dec 17 01:34:51 CST 2024
     */
    private String deptId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column task_repayment_predictions.dept_name
     *
     * @mbg.generated Tue Dec 17 01:34:51 CST 2024
     */
    private String deptName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column task_repayment_predictions.next_month_repayment
     *
     * @mbg.generated Tue Dec 17 01:34:51 CST 2024
     */
    private Long nextMonthRepayment;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column task_repayment_predictions.expected_repayment_date
     *
     * @mbg.generated Tue Dec 17 01:34:51 CST 2024
     */
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date expectedRepaymentDate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column task_repayment_predictions.remarks
     *
     * @mbg.generated Tue Dec 17 01:34:51 CST 2024
     */
    private String remarks;
    
    private String batchNumber;

    
    
    public String getBatchNumber() {
		return batchNumber;
	}

	public void setBatchNumber(String batchNumber) {
		this.batchNumber = batchNumber;
	}

	public String getCcId() {
		return ccId;
	}

	public void setCcId(String ccId) {
		this.ccId = ccId;
	}

	/**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column task_repayment_predictions.id
     *
     * @return the value of task_repayment_predictions.id
     *
     * @mbg.generated Tue Dec 17 01:34:51 CST 2024
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column task_repayment_predictions.id
     *
     * @param id the value for task_repayment_predictions.id
     *
     * @mbg.generated Tue Dec 17 01:34:51 CST 2024
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column task_repayment_predictions.customer_name
     *
     * @return the value of task_repayment_predictions.customer_name
     *
     * @mbg.generated Tue Dec 17 01:34:51 CST 2024
     */
    public String getCustomerName() {
        return customerName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column task_repayment_predictions.customer_name
     *
     * @param customerName the value for task_repayment_predictions.customer_name
     *
     * @mbg.generated Tue Dec 17 01:34:51 CST 2024
     */
    public void setCustomerName(String customerName) {
        this.customerName = customerName == null ? null : customerName.trim();
    }

    

    public String getCustomerIndexNumber() {
		return customerIndexNumber;
	}

	public void setCustomerIndexNumber(String customerIndexNumber) {
		this.customerIndexNumber = customerIndexNumber;
	}

	/**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column task_repayment_predictions.operator_id
     *
     * @return the value of task_repayment_predictions.operator_id
     *
     * @mbg.generated Tue Dec 17 01:34:51 CST 2024
     */
    public String getOperatorId() {
        return operatorId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column task_repayment_predictions.operator_id
     *
     * @param operatorId the value for task_repayment_predictions.operator_id
     *
     * @mbg.generated Tue Dec 17 01:34:51 CST 2024
     */
    public void setOperatorId(String operatorId) {
        this.operatorId = operatorId == null ? null : operatorId.trim();
    }

  

    public String getUserOrDept() {
		return userOrDept;
	}

	public void setUserOrDept(String userOrDept) {
		this.userOrDept = userOrDept;
	}

	/**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column task_repayment_predictions.dept_id
     *
     * @return the value of task_repayment_predictions.dept_id
     *
     * @mbg.generated Tue Dec 17 01:34:51 CST 2024
     */
    public String getDeptId() {
        return deptId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column task_repayment_predictions.dept_id
     *
     * @param deptId the value for task_repayment_predictions.dept_id
     *
     * @mbg.generated Tue Dec 17 01:34:51 CST 2024
     */
    public void setDeptId(String deptId) {
        this.deptId = deptId == null ? null : deptId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column task_repayment_predictions.dept_name
     *
     * @return the value of task_repayment_predictions.dept_name
     *
     * @mbg.generated Tue Dec 17 01:34:51 CST 2024
     */
    public String getDeptName() {
        return deptName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column task_repayment_predictions.dept_name
     *
     * @param deptName the value for task_repayment_predictions.dept_name
     *
     * @mbg.generated Tue Dec 17 01:34:51 CST 2024
     */
    public void setDeptName(String deptName) {
        this.deptName = deptName == null ? null : deptName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column task_repayment_predictions.next_month_repayment
     *
     * @return the value of task_repayment_predictions.next_month_repayment
     *
     * @mbg.generated Tue Dec 17 01:34:51 CST 2024
     */
    public Long getNextMonthRepayment() {
        return nextMonthRepayment;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column task_repayment_predictions.next_month_repayment
     *
     * @param nextMonthRepayment the value for task_repayment_predictions.next_month_repayment
     *
     * @mbg.generated Tue Dec 17 01:34:51 CST 2024
     */
    public void setNextMonthRepayment(Long nextMonthRepayment) {
        this.nextMonthRepayment = nextMonthRepayment;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column task_repayment_predictions.expected_repayment_date
     *
     * @return the value of task_repayment_predictions.expected_repayment_date
     *
     * @mbg.generated Tue Dec 17 01:34:51 CST 2024
     */
    public Date getExpectedRepaymentDate() {
        return expectedRepaymentDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column task_repayment_predictions.expected_repayment_date
     *
     * @param expectedRepaymentDate the value for task_repayment_predictions.expected_repayment_date
     *
     * @mbg.generated Tue Dec 17 01:34:51 CST 2024
     */
    public void setExpectedRepaymentDate(Date expectedRepaymentDate) {
        this.expectedRepaymentDate = expectedRepaymentDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column task_repayment_predictions.remarks
     *
     * @return the value of task_repayment_predictions.remarks
     *
     * @mbg.generated Tue Dec 17 01:34:51 CST 2024
     */
    public String getRemarks() {
        return remarks;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column task_repayment_predictions.remarks
     *
     * @param remarks the value for task_repayment_predictions.remarks
     *
     * @mbg.generated Tue Dec 17 01:34:51 CST 2024
     */
    public void setRemarks(String remarks) {
        this.remarks = remarks == null ? null : remarks.trim();
    }
}