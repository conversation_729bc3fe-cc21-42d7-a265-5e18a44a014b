package com.linhong.boot.management.service;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.linhong.boot.management.model.entity.CcUser;
import com.linhong.boot.management.model.entity.RepaymentRecords;
import com.linhong.boot.management.model.entity.TransferRecords;
import com.linhong.boot.management.model.query.CcUserExcQuery;
import com.linhong.boot.management.model.query.CcUserQuery;
import com.linhong.boot.management.model.query.ChangeCaseQuery;
import com.linhong.boot.management.model.query.RepaymentQuery;
import com.linhong.boot.management.model.vo.CcUserVo;
import com.linhong.boot.management.model.vo.TransferRecordsVO;

/**
 * 案池接口
 */
public interface CasePoolService extends IService<CcUser>{
	
	/**
	 * 批量更新
	 * @param ccUsers
	 * @return
	 */
	int updateBatchByIdNumber(List<CcUser> ccUsers);

	/**
	 * 案池分页列表
	 * @param ccUserQuery
	 * @return
	 */
	IPage<CcUserVo> getPoolPage(CcUserQuery queryParams);
	
	/**
	 * 作业清单
	 * @param ccUserQuery
	 * @return
	 */
	IPage<CcUserVo> getWorkPage(CcUserQuery queryParams);
	/**
	 * 换单记录列表
	 * @param queryParams
	 * @return
	 */
	IPage<TransferRecordsVO> getHuanPage(CcUserQuery queryParams);
	
	/**
	 * 还款记录分页列表
	 * @param ccUserQuery
	 * @return
	 */
	IPage<RepaymentRecords> getRepaymentPage(RepaymentQuery queryParams);
	
	/**
	 * 分配记录列表
	 * @param ccUserQuery
	 * @return
	 */
	List<TransferRecords> getAallocatPage(CcUserQuery queryParams);
	
	/**
	 * 案池导出
	 * @param ccUserQuery
	 * @return
	 */
	List<CcUser> getPoolExt(CcUserExcQuery queryParams);
	/**
	 * 作业清单导出
	 * @param queryParams
	 * @return
	 */
	List<CcUser> getWorkPoolExt(CcUserExcQuery queryParams);
	/**
	 * 案池配置影响范围列表
	 * @param queryParams
	 * @return
	 */
	List<CcUser> getCaseList(CcUserQuery queryParams);
	/**
	 * 获取案件详情
	 * @param id
	 * @return
	 */
	CcUserVo getPoolDetail(Long id);
	
	/**
	 * 跟进ccId 分配案件
	 * @param ccId
	 * @return
	 */
	boolean allocate(CcUserQuery query);
	
	/**
	 * 修改案件状态
	 * @return
	 */
	boolean updateStatusForCcUser(Long id,String status);
	/**
	 * 根据案件id修改案件负责人
	 * @param id
	 * @param status
	 * @return
	 */
	boolean updateUserOrDeptById(Long id,String status);
	/**
	 * 根据案件id修改案件
	 * @param userName
	 * @param ccId
	 * @return
	 */
	boolean updateUserOrDept(CcUser u, Long ccId);
	
	/**
	 * 换单
	 * @param query
	 * @return 101:换单失败,案件不足
	 */
	Integer changeCase(ChangeCaseQuery query);
	/**
	 * 作业清单分配
	 * @param query
	 * @return
	 */
	boolean workAllocate(CcUserQuery query);
	
	
	
	
}
