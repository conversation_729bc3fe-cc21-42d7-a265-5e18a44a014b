package com.linhong.boot.management.model.entity;

import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;

public class TransferRecords {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column transfer_records.id
     *
     * @mbg.generated Mon Dec 02 17:42:49 CST 2024
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column transfer_records.source_organization
     *
     * @mbg.generated Mon Dec 02 17:42:49 CST 2024
     */
    private String sourceOrganization;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column transfer_records.target_organization
     *
     * @mbg.generated Mon Dec 02 17:42:49 CST 2024
     */
    private String targetOrganization;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column transfer_records.source_user
     *
     * @mbg.generated Mon Dec 02 17:42:49 CST 2024
     */
    private String sourceUser;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column transfer_records.target_user
     *
     * @mbg.generated Mon Dec 02 17:42:49 CST 2024
     */
    private String targetUser;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column transfer_records.assignment_type
     *
     * @mbg.generated Mon Dec 02 17:42:49 CST 2024
     */
    private String assignmentType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column transfer_records.operator
     *
     * @mbg.generated Mon Dec 02 17:42:49 CST 2024
     */
    private String operator;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column transfer_records.operation_time
     *
     * @mbg.generated Mon Dec 02 17:42:49 CST 2024
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime operationTime;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column transfer_records.id
     *
     * @return the value of transfer_records.id
     *
     * @mbg.generated Mon Dec 02 17:42:49 CST 2024
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column transfer_records.id
     *
     * @param id the value for transfer_records.id
     *
     * @mbg.generated Mon Dec 02 17:42:49 CST 2024
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column transfer_records.source_organization
     *
     * @return the value of transfer_records.source_organization
     *
     * @mbg.generated Mon Dec 02 17:42:49 CST 2024
     */
    public String getSourceOrganization() {
        return sourceOrganization;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column transfer_records.source_organization
     *
     * @param sourceOrganization the value for transfer_records.source_organization
     *
     * @mbg.generated Mon Dec 02 17:42:49 CST 2024
     */
    public void setSourceOrganization(String sourceOrganization) {
        this.sourceOrganization = sourceOrganization == null ? null : sourceOrganization.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column transfer_records.target_organization
     *
     * @return the value of transfer_records.target_organization
     *
     * @mbg.generated Mon Dec 02 17:42:49 CST 2024
     */
    public String getTargetOrganization() {
        return targetOrganization;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column transfer_records.target_organization
     *
     * @param targetOrganization the value for transfer_records.target_organization
     *
     * @mbg.generated Mon Dec 02 17:42:49 CST 2024
     */
    public void setTargetOrganization(String targetOrganization) {
        this.targetOrganization = targetOrganization == null ? null : targetOrganization.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column transfer_records.source_user
     *
     * @return the value of transfer_records.source_user
     *
     * @mbg.generated Mon Dec 02 17:42:49 CST 2024
     */
    public String getSourceUser() {
        return sourceUser;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column transfer_records.source_user
     *
     * @param sourceUser the value for transfer_records.source_user
     *
     * @mbg.generated Mon Dec 02 17:42:49 CST 2024
     */
    public void setSourceUser(String sourceUser) {
        this.sourceUser = sourceUser == null ? null : sourceUser.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column transfer_records.target_user
     *
     * @return the value of transfer_records.target_user
     *
     * @mbg.generated Mon Dec 02 17:42:49 CST 2024
     */
    public String getTargetUser() {
        return targetUser;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column transfer_records.target_user
     *
     * @param targetUser the value for transfer_records.target_user
     *
     * @mbg.generated Mon Dec 02 17:42:49 CST 2024
     */
    public void setTargetUser(String targetUser) {
        this.targetUser = targetUser == null ? null : targetUser.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column transfer_records.assignment_type
     *
     * @return the value of transfer_records.assignment_type
     *
     * @mbg.generated Mon Dec 02 17:42:49 CST 2024
     */
    public String getAssignmentType() {
        return assignmentType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column transfer_records.assignment_type
     *
     * @param assignmentType the value for transfer_records.assignment_type
     *
     * @mbg.generated Mon Dec 02 17:42:49 CST 2024
     */
    public void setAssignmentType(String assignmentType) {
        this.assignmentType = assignmentType == null ? null : assignmentType.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column transfer_records.operator
     *
     * @return the value of transfer_records.operator
     *
     * @mbg.generated Mon Dec 02 17:42:49 CST 2024
     */
    public String getOperator() {
        return operator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column transfer_records.operator
     *
     * @param operator the value for transfer_records.operator
     *
     * @mbg.generated Mon Dec 02 17:42:49 CST 2024
     */
    public void setOperator(String operator) {
        this.operator = operator == null ? null : operator.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column transfer_records.operation_time
     *
     * @return the value of transfer_records.operation_time
     *
     * @mbg.generated Mon Dec 02 17:42:49 CST 2024
     */
    public LocalDateTime getOperationTime() {
        return operationTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column transfer_records.operation_time
     *
     * @param operationTime the value for transfer_records.operation_time
     *
     * @mbg.generated Mon Dec 02 17:42:49 CST 2024
     */
    public void setOperationTime(LocalDateTime operationTime) {
        this.operationTime = operationTime;
    }
}