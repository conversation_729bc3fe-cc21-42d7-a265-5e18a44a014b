<script setup lang="ts">
defineOptions({
  name: "ele-form",
});
// 引入组件
import NumberRange from "./NumberRange.vue";

import { trimObjectValuesInPlace } from "@/utils/commit";
const loading = ref(false);
const props = defineProps({
  // 表单选项
  formItemList: {
    type: Array,
    default: [],
  },
  //初始化表单内容
  formValue: {
    type: Object,
    default: {},
  },
  // 自定义的表单操作按钮
  operate: {
    type: Array,
    default: [],
  },
  // 开启默认表单查询操作按钮
  operateOpen: {
    type: Boolean,
    default: true,
  },
  // 栅格布局
  formBox: {
    type: Object,
    default: () => ({
      spanItem: 6, // 每项占比
      formContent: 22, // 表单父级占比
      operate: 24, // 操作父容器占比
      operateItem: 5,
      operateGrid: "end",
    }),
  },
  defineTxt: {
    type: String,
    default: "搜索",
  },
  defineIcon: {
    type: String,
    default: "search",
  },
  cancelTxt: {
    type: String,
    default: "重置",
  },
});

const formRef = ref();
const emit = defineEmits(["handleSubmit", "handleCancel", "fromChange"]);
const form: any = reactive({});
const formItemListData: any = ref([]);
props.formItemList.forEach((item) => {
  let objs = JSON.parse(JSON.stringify(item));
  if (objs.type === "upload") {
    form[objs.field] = objs.value !== undefined ? objs.value : [];
  } else if (objs.type === "tag") {
    form[objs.field] = objs.option;
  } else {
    form[objs.field] = objs.value !== undefined ? objs.value : null;
  }
});
formItemListData.value = props.formItemList;

// 提交按钮
const handleSubmit = async () => {
  loading.value = true;
  formRef.value.validate((valid: any) => {
    console.log(valid);
    if (valid) {
      emit("handleSubmit", Object.assign({}, form));
    } else {
      console.warn("错误,请检查代码或内容");
    }
    setTimeout(() => {
      loading.value = false;
    });
  });
};
// 重置/取消、关闭
const resetFields = () => {
  loading.value = true;
  props.formItemList.forEach((item: any) => {
    if (item.type === "upload") {
      form[item.field] = [];
    } else if (item.type === "tag") {
      form[item.field] = [];
      item.option = [];
    } else if (item.type === "number-range") {
      form[item.field] = "";
      form["minValue"] = "";
      form["maxValue"] = "";
    } else {
      form[item.field] = item.value !== undefined ? item.value : null;
    }
  });
  formRef.value.resetFields();
  setTimeout(() => {
    loading.value = false;
    // 重新初始化
    setFormVlaue(props.formValue);
    emit("handleCancel");
  }, 0);
};

// 更新表单值
const setFormVlaue = (objs: any) => {
  console.log("setFormVlaue", objs);
  if (Object.keys(objs).length) {
    for (let key in objs) {
      if (key in form) form[key] = objs[key];
    }
  } else {
    for (let key in form) {
      delete form[key];
    }
  }
};
//获得form单值
const getFormVlaue = () => {
  let formVlaue = Object.assign({}, form);
  return trimObjectValuesInPlace(formVlaue);
};
//监听form表单值被更改
watch(
  form,
  (val) => {
    emit("fromChange", val);
  },
  { deep: true, immediate: false }
);
//初始化内容更新表单
watch(
  props.formValue,
  (val) => {
    if (val) {
      setFormVlaue(val);
    }
  },
  { deep: true, immediate: true }
);
defineExpose({
  // 设置值
  setFormVlaue,
  //获取form单值
  getFormVlaue,
  //提交
  handleSubmit,
  //取消重置
  resetFields,
});
</script>

<template>
  <el-form
    ref="formRef"
    :model="form"
    label-width="100px"
    label-position="top"
    @submit="handleSubmit"
  >
    <el-row :gutter="24">
      <el-col :span="formBox.formContent || 24">
        <el-row :gutter="24">
          <el-col
            v-for="(item, index) in formItemListData"
            v-show="!item.hasAuth"
            :key="index"
            :span="item.spanItem || formBox.spanItem || 8"
          >
            <!-- 提交按钮 -->
            <div v-if="item.type === 'button—submit'" class="button—submit">
              <el-button
                :type="item.formType"
                :loading="loading"
                :icon="item.icon"
                :disabled="item.disabled"
                @click="handleSubmit"
              >
                {{ item.label }}
              </el-button>
            </div>
            <!-- form表单 -->
            <el-form-item
              v-else
              :label="item.label"
              :prop="item.field"
              :label-width="item.labelWidth || '100px'"
              :rules="item.rules ? item.rules : undefined"
            >
              <template #label>
                {{ item.label }}
                <el-tooltip
                  v-if="item.tooltipMsg"
                  class="box-item"
                  effect="dark"
                  :content="item.tooltipMsg"
                  placement="top"
                >
                  <el-icon style="vertical-align: sub; cursor: pointer">
                    <Warning color="#F56C6C" />
                  </el-icon>
                </el-tooltip>
              </template>
              <template v-if="item.type === 'input'">
                <el-input
                  v-model.trim="form[item.field]"
                  :maxlength="item.maxlength || ''"
                  :placeholder="item.placeholder"
                  :type="item.formType || 'string'"
                  :disabled="item.disabled"
                  :size="item.size || 'default'"
                  clearable
                />
              </template>
              <template v-else-if="item.type === 'switch'">
                <el-switch v-model="form[item.field]" :disabled="item.disabled" />
              </template>
              <template v-else-if="item.type === 'cascader'">
                <el-cascader
                  v-model="form[item.field]"
                  :options="item.options"
                  :props="item.props"
                  :placeholder="item.placeholder"
                  collapse-tags
                  collapse-tags-tooltip
                  clearable
                  :disabled="item.disabled"
                  :show-all-levels="false"
                />
              </template>
              <template v-else-if="item.type === 'select'">
                <el-select
                  v-model="form[item.field]"
                  :size="item.size || 'default'"
                  :placeholder="item.placeholder"
                  :multiple="item.multiple || false"
                  :collapse-tags="item.collapseTags || false"
                  :collapse-tags-tooltip="item.collapseTagsTooltip || false"
                  clearable
                  :class="{ 'select-with-checkbox': item.multiple && item.field === 'batchNumber' }"
                >
                  <el-option
                    v-for="itemT in item.options"
                    :key="itemT.value"
                    :label="itemT.label"
                    :value="itemT.value"
                  >
                    <!-- 为批次号多选添加复选框 -->
                    <template v-if="item.multiple && item.field === 'batchNumber'">
                      <span class="checkbox-wrapper">
                        <span
                          class="custom-checkbox"
                          :class="{
                            checked: form[item.field] && form[item.field].includes(itemT.value),
                          }"
                        />
                        <span class="option-label">{{ itemT.label }}</span>
                      </span>
                    </template>
                  </el-option>
                </el-select>
              </template>
              <template v-else-if="item.type === 'select-input'">
                <el-input
                  v-model="form[item.field]"
                  :placeholder="item.placeholder"
                  class="input-with-select"
                  clearable
                  :size="item.size || 'default'"
                >
                  <template #prepend>
                    <el-select
                      v-model="form[item.fieldKey]"
                      :placeholder="item.placeholder"
                      style="width: 110px"
                    >
                      <el-option
                        v-for="itemT in item.options"
                        :key="itemT.value"
                        :label="itemT.label"
                        :value="itemT.value"
                      />
                    </el-select>
                  </template>
                </el-input>
              </template>
              <template v-else-if="item.type === 'date-picker'">
                <el-date-picker
                  v-model="form[item.field]"
                  format="YYYY/MM/DD"
                  value-format="YYYY-MM-DD"
                  :type="item.formType"
                  range-separator="To"
                  :start-placeholder="item.placeholder"
                  :end-placeholder="item.placeholder"
                  :disabled-date="item.disabledDate"
                  :size="item.size || 'default'"
                  clearable
                />
              </template>
              <template v-else-if="item.type === 'number-range'">
                <number-range
                  v-model.trim="form[item.field]"
                  v-model:min-value="form[item.minValue]"
                  v-model:max-value="form[item.maxValue]"
                  :valueRange="item.valueRange || [0, 100]"
                  clearable
                >
                  <!-- <template #append>
                  <span>元</span>
                </template> -->
                </number-range>
              </template>
            </el-form-item>
          </el-col>
        </el-row>
      </el-col>
      <el-col v-if="operateOpen" :span="formBox.operate || 4">
        <el-row :gutter="24" :justify="formBox.operateGrid || 'end'">
          <el-col :span="formBox.operateItem || 12" :pull="1" style="margin-bottom: 20px">
            <div class="formBox_button">
              <el-button
                type="primary"
                :loading="loading"
                :icon="props.defineIcon"
                @click="handleSubmit"
              >
                {{ props.defineTxt }}
              </el-button>
              <el-button icon="refresh" :loading="loading" @click="resetFields">
                {{ props.cancelTxt }}
              </el-button>
            </div>
          </el-col>
        </el-row>
      </el-col>
    </el-row>
  </el-form>
</template>

<style lang="scss" scoped>
.button—submit {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.formBox_button {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

// 表单项垂直布局样式
:deep(.el-form-item) {
  margin-bottom: 18px;

  .el-form-item__label {
    margin-bottom: 8px;
    padding: 0;
    line-height: 1.4;
    font-weight: 500;
  }

  .el-form-item__content {
    margin-left: 0 !important;
  }
}

// 输入框样式优化
:deep(.el-input),
:deep(.el-select),
:deep(.el-cascader),
:deep(.el-date-picker) {
  width: 100%;
}

// 输入框圆角
:deep(.el-input__wrapper) {
  border-radius: 6px;
}

:deep(.el-select .el-input__wrapper) {
  border-radius: 6px;
}

/* 批次号多选复选框样式 */
:deep(.select-with-checkbox) {
  /* 清空原来的多选框样式 */
  .el-select-dropdown.is-multiple .el-select-dropdown__item:after {
    content: "";
  }
  .el-select-dropdown.is-multiple .el-select-dropdown__item.selected:after {
    content: "";
  }
}

/* 复选框包装器样式 */
:deep(.checkbox-wrapper) {
  display: flex;
  align-items: center;
  width: 100%;
}

/* 自定义复选框样式 */
:deep(.custom-checkbox) {
  display: inline-block;
  position: relative;
  border: 1px solid #dcdfe6;
  border-radius: 2px;
  box-sizing: border-box;
  width: 14px;
  height: 14px;
  background-color: #fff;
  margin-right: 8px;
  flex-shrink: 0;
  transition:
    border-color 0.25s cubic-bezier(0.71, -0.46, 0.29, 1.46),
    background-color 0.25s cubic-bezier(0.71, -0.46, 0.29, 1.46);
}

/* 选中状态的复选框样式 */
:deep(.custom-checkbox.checked) {
  background-color: #409eff;
  border-color: #409eff;
}

/* 复选框中的对号样式 - 不显示对钩 */
:deep(.custom-checkbox.checked::after) {
  content: "";
}

/* 选项标签样式 */
:deep(.option-label) {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
