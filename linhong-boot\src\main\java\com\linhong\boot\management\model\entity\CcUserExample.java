package com.linhong.boot.management.model.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

public class CcUserExample {
	
	private int pageNum;
	
	private int pageSize;
	
	

	public int getPageNum() {
		return pageNum;
	}

	public void setPageNum(int pageNum) {
		this.pageNum = pageNum;
	}

	public int getPageSize() {
		return pageSize;
	}

	public void setPageSize(int pageSize) {
		this.pageSize = pageSize;
	}

	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table cc_user
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	protected String orderByClause;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table cc_user
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	protected boolean distinct;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table cc_user
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	protected List<Criteria> oredCriteria;

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table cc_user
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public CcUserExample() {
		oredCriteria = new ArrayList<>();
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table cc_user
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public void setOrderByClause(String orderByClause) {
		this.orderByClause = orderByClause;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table cc_user
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public String getOrderByClause() {
		return orderByClause;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table cc_user
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public void setDistinct(boolean distinct) {
		this.distinct = distinct;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table cc_user
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public boolean isDistinct() {
		return distinct;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table cc_user
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public List<Criteria> getOredCriteria() {
		return oredCriteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table cc_user
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public void or(Criteria criteria) {
		oredCriteria.add(criteria);
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table cc_user
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public Criteria or() {
		Criteria criteria = createCriteriaInternal();
		oredCriteria.add(criteria);
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table cc_user
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public Criteria createCriteria() {
		Criteria criteria = createCriteriaInternal();
		if (oredCriteria.size() == 0) {
			oredCriteria.add(criteria);
		}
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table cc_user
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	protected Criteria createCriteriaInternal() {
		Criteria criteria = new Criteria();
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table cc_user
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public void clear() {
		oredCriteria.clear();
		orderByClause = null;
		distinct = false;
	}

	/**
	 * This class was generated by MyBatis Generator. This class corresponds to the database table cc_user
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	protected abstract static class GeneratedCriteria {
		protected List<Criterion> criteria;

		protected GeneratedCriteria() {
			super();
			criteria = new ArrayList<>();
		}

		public boolean isValid() {
			return criteria.size() > 0;
		}

		public List<Criterion> getAllCriteria() {
			return criteria;
		}

		public List<Criterion> getCriteria() {
			return criteria;
		}

		protected void addCriterion(String condition) {
			if (condition == null) {
				throw new RuntimeException("Value for condition cannot be null");
			}
			criteria.add(new Criterion(condition));
		}

		protected void addCriterion(String condition, Object value, String property) {
			if (value == null) {
				throw new RuntimeException("Value for " + property + " cannot be null");
			}
			criteria.add(new Criterion(condition, value));
		}

		protected void addCriterion(String condition, Object value1, Object value2, String property) {
			if (value1 == null || value2 == null) {
				throw new RuntimeException("Between values for " + property + " cannot be null");
			}
			criteria.add(new Criterion(condition, value1, value2));
		}

		protected void addCriterionForJDBCDate(String condition, Date value, String property) {
			if (value == null) {
				throw new RuntimeException("Value for " + property + " cannot be null");
			}
			addCriterion(condition, new java.sql.Date(value.getTime()), property);
		}

		protected void addCriterionForJDBCDate(String condition, List<Date> values, String property) {
			if (values == null || values.size() == 0) {
				throw new RuntimeException("Value list for " + property + " cannot be null or empty");
			}
			List<java.sql.Date> dateList = new ArrayList<>();
			Iterator<Date> iter = values.iterator();
			while (iter.hasNext()) {
				dateList.add(new java.sql.Date(iter.next().getTime()));
			}
			addCriterion(condition, dateList, property);
		}

		protected void addCriterionForJDBCDate(String condition, Date value1, Date value2, String property) {
			if (value1 == null || value2 == null) {
				throw new RuntimeException("Between values for " + property + " cannot be null");
			}
			addCriterion(condition, new java.sql.Date(value1.getTime()), new java.sql.Date(value2.getTime()), property);
		}

		public Criteria andIdIsNull() {
			addCriterion("id is null");
			return (Criteria) this;
		}

		public Criteria andIdIsNotNull() {
			addCriterion("id is not null");
			return (Criteria) this;
		}

		public Criteria andIdEqualTo(Long value) {
			addCriterion("id =", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotEqualTo(Long value) {
			addCriterion("id <>", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdGreaterThan(Long value) {
			addCriterion("id >", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdGreaterThanOrEqualTo(Long value) {
			addCriterion("id >=", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdLessThan(Long value) {
			addCriterion("id <", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdLessThanOrEqualTo(Long value) {
			addCriterion("id <=", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdIn(List<Long> values) {
			addCriterion("id in", values, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotIn(List<Long> values) {
			addCriterion("id not in", values, "id");
			return (Criteria) this;
		}

		public Criteria andIdBetween(Long value1, Long value2) {
			addCriterion("id between", value1, value2, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotBetween(Long value1, Long value2) {
			addCriterion("id not between", value1, value2, "id");
			return (Criteria) this;
		}

		public Criteria andCustomerNameIsNull() {
			addCriterion("customer_name is null");
			return (Criteria) this;
		}

		public Criteria andCustomerNameIsNotNull() {
			addCriterion("customer_name is not null");
			return (Criteria) this;
		}

		public Criteria andCustomerNameEqualTo(String value) {
			addCriterion("customer_name =", value, "customerName");
			return (Criteria) this;
		}

		public Criteria andCustomerNameNotEqualTo(String value) {
			addCriterion("customer_name <>", value, "customerName");
			return (Criteria) this;
		}

		public Criteria andCustomerNameGreaterThan(String value) {
			addCriterion("customer_name >", value, "customerName");
			return (Criteria) this;
		}

		public Criteria andCustomerNameGreaterThanOrEqualTo(String value) {
			addCriterion("customer_name >=", value, "customerName");
			return (Criteria) this;
		}

		public Criteria andCustomerNameLessThan(String value) {
			addCriterion("customer_name <", value, "customerName");
			return (Criteria) this;
		}

		public Criteria andCustomerNameLessThanOrEqualTo(String value) {
			addCriterion("customer_name <=", value, "customerName");
			return (Criteria) this;
		}

		public Criteria andCustomerNameLike(String value) {
			addCriterion("customer_name like", value, "customerName");
			return (Criteria) this;
		}

		public Criteria andCustomerNameNotLike(String value) {
			addCriterion("customer_name not like", value, "customerName");
			return (Criteria) this;
		}

		public Criteria andCustomerNameIn(List<String> values) {
			addCriterion("customer_name in", values, "customerName");
			return (Criteria) this;
		}

		public Criteria andCustomerNameNotIn(List<String> values) {
			addCriterion("customer_name not in", values, "customerName");
			return (Criteria) this;
		}

		public Criteria andCustomerNameBetween(String value1, String value2) {
			addCriterion("customer_name between", value1, value2, "customerName");
			return (Criteria) this;
		}

		public Criteria andCustomerNameNotBetween(String value1, String value2) {
			addCriterion("customer_name not between", value1, value2, "customerName");
			return (Criteria) this;
		}

		public Criteria andCardholderIdNumberIsNull() {
			addCriterion("cardholder_id_number is null");
			return (Criteria) this;
		}

		public Criteria andCardholderIdNumberIsNotNull() {
			addCriterion("cardholder_id_number is not null");
			return (Criteria) this;
		}

		public Criteria andCardholderIdNumberEqualTo(String value) {
			addCriterion("cardholder_id_number =", value, "cardholderIdNumber");
			return (Criteria) this;
		}

		public Criteria andCardholderIdNumberNotEqualTo(String value) {
			addCriterion("cardholder_id_number <>", value, "cardholderIdNumber");
			return (Criteria) this;
		}

		public Criteria andCardholderIdNumberGreaterThan(String value) {
			addCriterion("cardholder_id_number >", value, "cardholderIdNumber");
			return (Criteria) this;
		}

		public Criteria andCardholderIdNumberGreaterThanOrEqualTo(String value) {
			addCriterion("cardholder_id_number >=", value, "cardholderIdNumber");
			return (Criteria) this;
		}

		public Criteria andCardholderIdNumberLessThan(String value) {
			addCriterion("cardholder_id_number <", value, "cardholderIdNumber");
			return (Criteria) this;
		}

		public Criteria andCardholderIdNumberLessThanOrEqualTo(String value) {
			addCriterion("cardholder_id_number <=", value, "cardholderIdNumber");
			return (Criteria) this;
		}

		public Criteria andCardholderIdNumberLike(String value) {
			addCriterion("cardholder_id_number like", value, "cardholderIdNumber");
			return (Criteria) this;
		}

		public Criteria andCardholderIdNumberNotLike(String value) {
			addCriterion("cardholder_id_number not like", value, "cardholderIdNumber");
			return (Criteria) this;
		}

		public Criteria andCardholderIdNumberIn(List<String> values) {
			addCriterion("cardholder_id_number in", values, "cardholderIdNumber");
			return (Criteria) this;
		}

		public Criteria andCardholderIdNumberNotIn(List<String> values) {
			addCriterion("cardholder_id_number not in", values, "cardholderIdNumber");
			return (Criteria) this;
		}

		public Criteria andCardholderIdNumberBetween(String value1, String value2) {
			addCriterion("cardholder_id_number between", value1, value2, "cardholderIdNumber");
			return (Criteria) this;
		}

		public Criteria andCardholderIdNumberNotBetween(String value1, String value2) {
			addCriterion("cardholder_id_number not between", value1, value2, "cardholderIdNumber");
			return (Criteria) this;
		}

		public Criteria andCardholderCodeIsNull() {
			addCriterion("cardholder_code is null");
			return (Criteria) this;
		}

		public Criteria andCardholderCodeIsNotNull() {
			addCriterion("cardholder_code is not null");
			return (Criteria) this;
		}

		public Criteria andCardholderCodeEqualTo(String value) {
			addCriterion("cardholder_code =", value, "cardholderCode");
			return (Criteria) this;
		}

		public Criteria andCardholderCodeNotEqualTo(String value) {
			addCriterion("cardholder_code <>", value, "cardholderCode");
			return (Criteria) this;
		}

		public Criteria andCardholderCodeGreaterThan(String value) {
			addCriterion("cardholder_code >", value, "cardholderCode");
			return (Criteria) this;
		}

		public Criteria andCardholderCodeGreaterThanOrEqualTo(String value) {
			addCriterion("cardholder_code >=", value, "cardholderCode");
			return (Criteria) this;
		}

		public Criteria andCardholderCodeLessThan(String value) {
			addCriterion("cardholder_code <", value, "cardholderCode");
			return (Criteria) this;
		}

		public Criteria andCardholderCodeLessThanOrEqualTo(String value) {
			addCriterion("cardholder_code <=", value, "cardholderCode");
			return (Criteria) this;
		}

		public Criteria andCardholderCodeLike(String value) {
			addCriterion("cardholder_code like", value, "cardholderCode");
			return (Criteria) this;
		}

		public Criteria andCardholderCodeNotLike(String value) {
			addCriterion("cardholder_code not like", value, "cardholderCode");
			return (Criteria) this;
		}

		public Criteria andCardholderCodeIn(List<String> values) {
			addCriterion("cardholder_code in", values, "cardholderCode");
			return (Criteria) this;
		}

		public Criteria andCardholderCodeNotIn(List<String> values) {
			addCriterion("cardholder_code not in", values, "cardholderCode");
			return (Criteria) this;
		}

		public Criteria andCardholderCodeBetween(String value1, String value2) {
			addCriterion("cardholder_code between", value1, value2, "cardholderCode");
			return (Criteria) this;
		}

		public Criteria andCardholderCodeNotBetween(String value1, String value2) {
			addCriterion("cardholder_code not between", value1, value2, "cardholderCode");
			return (Criteria) this;
		}

		public Criteria andCustomerIndexNumberIsNull() {
			addCriterion("customer_index_number is null");
			return (Criteria) this;
		}

		public Criteria andCustomerIndexNumberIsNotNull() {
			addCriterion("customer_index_number is not null");
			return (Criteria) this;
		}

		public Criteria andCustomerIndexNumberEqualTo(String value) {
			addCriterion("customer_index_number =", value, "customerIndexNumber");
			return (Criteria) this;
		}

		public Criteria andCustomerIndexNumberNotEqualTo(String value) {
			addCriterion("customer_index_number <>", value, "customerIndexNumber");
			return (Criteria) this;
		}

		public Criteria andCustomerIndexNumberGreaterThan(String value) {
			addCriterion("customer_index_number >", value, "customerIndexNumber");
			return (Criteria) this;
		}

		public Criteria andCustomerIndexNumberGreaterThanOrEqualTo(String value) {
			addCriterion("customer_index_number >=", value, "customerIndexNumber");
			return (Criteria) this;
		}

		public Criteria andCustomerIndexNumberLessThan(String value) {
			addCriterion("customer_index_number <", value, "customerIndexNumber");
			return (Criteria) this;
		}

		public Criteria andCustomerIndexNumberLessThanOrEqualTo(String value) {
			addCriterion("customer_index_number <=", value, "customerIndexNumber");
			return (Criteria) this;
		}

		public Criteria andCustomerIndexNumberLike(String value) {
			addCriterion("customer_index_number like", value, "customerIndexNumber");
			return (Criteria) this;
		}

		public Criteria andCustomerIndexNumberNotLike(String value) {
			addCriterion("customer_index_number not like", value, "customerIndexNumber");
			return (Criteria) this;
		}

		public Criteria andCustomerIndexNumberIn(List<String> values) {
			addCriterion("customer_index_number in", values, "customerIndexNumber");
			return (Criteria) this;
		}

		public Criteria andCustomerIndexNumberNotIn(List<String> values) {
			addCriterion("customer_index_number not in", values, "customerIndexNumber");
			return (Criteria) this;
		}

		public Criteria andCustomerIndexNumberBetween(String value1, String value2) {
			addCriterion("customer_index_number between", value1, value2, "customerIndexNumber");
			return (Criteria) this;
		}

		public Criteria andCustomerIndexNumberNotBetween(String value1, String value2) {
			addCriterion("customer_index_number not between", value1, value2, "customerIndexNumber");
			return (Criteria) this;
		}

		public Criteria andEntrustAmountIsNull() {
			addCriterion("entrust_amount is null");
			return (Criteria) this;
		}

		public Criteria andEntrustAmountIsNotNull() {
			addCriterion("entrust_amount is not null");
			return (Criteria) this;
		}

		public Criteria andEntrustAmountEqualTo(BigDecimal value) {
			addCriterion("entrust_amount =", value, "entrustAmount");
			return (Criteria) this;
		}

		public Criteria andEntrustAmountNotEqualTo(BigDecimal value) {
			addCriterion("entrust_amount <>", value, "entrustAmount");
			return (Criteria) this;
		}

		public Criteria andEntrustAmountGreaterThan(BigDecimal value) {
			addCriterion("entrust_amount >", value, "entrustAmount");
			return (Criteria) this;
		}

		public Criteria andEntrustAmountGreaterThanOrEqualTo(BigDecimal value) {
			addCriterion("entrust_amount >=", value, "entrustAmount");
			return (Criteria) this;
		}

		public Criteria andEntrustAmountLessThan(BigDecimal value) {
			addCriterion("entrust_amount <", value, "entrustAmount");
			return (Criteria) this;
		}

		public Criteria andEntrustAmountLessThanOrEqualTo(BigDecimal value) {
			addCriterion("entrust_amount <=", value, "entrustAmount");
			return (Criteria) this;
		}

		public Criteria andEntrustAmountIn(List<BigDecimal> values) {
			addCriterion("entrust_amount in", values, "entrustAmount");
			return (Criteria) this;
		}

		public Criteria andEntrustAmountNotIn(List<BigDecimal> values) {
			addCriterion("entrust_amount not in", values, "entrustAmount");
			return (Criteria) this;
		}

		public Criteria andEntrustAmountBetween(BigDecimal value1, BigDecimal value2) {
			addCriterion("entrust_amount between", value1, value2, "entrustAmount");
			return (Criteria) this;
		}

		public Criteria andEntrustAmountNotBetween(BigDecimal value1, BigDecimal value2) {
			addCriterion("entrust_amount not between", value1, value2, "entrustAmount");
			return (Criteria) this;
		}

		public Criteria andEntrustTotalAmountIsNull() {
			addCriterion("entrust_total_amount is null");
			return (Criteria) this;
		}

		public Criteria andEntrustTotalAmountIsNotNull() {
			addCriterion("entrust_total_amount is not null");
			return (Criteria) this;
		}

		public Criteria andEntrustTotalAmountEqualTo(BigDecimal value) {
			addCriterion("entrust_total_amount =", value, "entrustTotalAmount");
			return (Criteria) this;
		}

		public Criteria andEntrustTotalAmountNotEqualTo(BigDecimal value) {
			addCriterion("entrust_total_amount <>", value, "entrustTotalAmount");
			return (Criteria) this;
		}

		public Criteria andEntrustTotalAmountGreaterThan(BigDecimal value) {
			addCriterion("entrust_total_amount >", value, "entrustTotalAmount");
			return (Criteria) this;
		}

		public Criteria andEntrustTotalAmountGreaterThanOrEqualTo(BigDecimal value) {
			addCriterion("entrust_total_amount >=", value, "entrustTotalAmount");
			return (Criteria) this;
		}

		public Criteria andEntrustTotalAmountLessThan(BigDecimal value) {
			addCriterion("entrust_total_amount <", value, "entrustTotalAmount");
			return (Criteria) this;
		}

		public Criteria andEntrustTotalAmountLessThanOrEqualTo(BigDecimal value) {
			addCriterion("entrust_total_amount <=", value, "entrustTotalAmount");
			return (Criteria) this;
		}

		public Criteria andEntrustTotalAmountIn(List<BigDecimal> values) {
			addCriterion("entrust_total_amount in", values, "entrustTotalAmount");
			return (Criteria) this;
		}

		public Criteria andEntrustTotalAmountNotIn(List<BigDecimal> values) {
			addCriterion("entrust_total_amount not in", values, "entrustTotalAmount");
			return (Criteria) this;
		}

		public Criteria andEntrustTotalAmountBetween(BigDecimal value1, BigDecimal value2) {
			addCriterion("entrust_total_amount between", value1, value2, "entrustTotalAmount");
			return (Criteria) this;
		}

		public Criteria andEntrustTotalAmountNotBetween(BigDecimal value1, BigDecimal value2) {
			addCriterion("entrust_total_amount not between", value1, value2, "entrustTotalAmount");
			return (Criteria) this;
		}

		public Criteria andEntrustPrincipalIsNull() {
			addCriterion("entrust_principal is null");
			return (Criteria) this;
		}

		public Criteria andEntrustPrincipalIsNotNull() {
			addCriterion("entrust_principal is not null");
			return (Criteria) this;
		}

		public Criteria andEntrustPrincipalEqualTo(BigDecimal value) {
			addCriterion("entrust_principal =", value, "entrustPrincipal");
			return (Criteria) this;
		}

		public Criteria andEntrustPrincipalNotEqualTo(BigDecimal value) {
			addCriterion("entrust_principal <>", value, "entrustPrincipal");
			return (Criteria) this;
		}

		public Criteria andEntrustPrincipalGreaterThan(BigDecimal value) {
			addCriterion("entrust_principal >", value, "entrustPrincipal");
			return (Criteria) this;
		}

		public Criteria andEntrustPrincipalGreaterThanOrEqualTo(BigDecimal value) {
			addCriterion("entrust_principal >=", value, "entrustPrincipal");
			return (Criteria) this;
		}

		public Criteria andEntrustPrincipalLessThan(BigDecimal value) {
			addCriterion("entrust_principal <", value, "entrustPrincipal");
			return (Criteria) this;
		}

		public Criteria andEntrustPrincipalLessThanOrEqualTo(BigDecimal value) {
			addCriterion("entrust_principal <=", value, "entrustPrincipal");
			return (Criteria) this;
		}

		public Criteria andEntrustPrincipalIn(List<BigDecimal> values) {
			addCriterion("entrust_principal in", values, "entrustPrincipal");
			return (Criteria) this;
		}

		public Criteria andEntrustPrincipalNotIn(List<BigDecimal> values) {
			addCriterion("entrust_principal not in", values, "entrustPrincipal");
			return (Criteria) this;
		}

		public Criteria andEntrustPrincipalBetween(BigDecimal value1, BigDecimal value2) {
			addCriterion("entrust_principal between", value1, value2, "entrustPrincipal");
			return (Criteria) this;
		}

		public Criteria andEntrustPrincipalNotBetween(BigDecimal value1, BigDecimal value2) {
			addCriterion("entrust_principal not between", value1, value2, "entrustPrincipal");
			return (Criteria) this;
		}

		public Criteria andEntrustPrincipalTotalIsNull() {
			addCriterion("entrust_principal_total is null");
			return (Criteria) this;
		}

		public Criteria andEntrustPrincipalTotalIsNotNull() {
			addCriterion("entrust_principal_total is not null");
			return (Criteria) this;
		}

		public Criteria andEntrustPrincipalTotalEqualTo(BigDecimal value) {
			addCriterion("entrust_principal_total =", value, "entrustPrincipalTotal");
			return (Criteria) this;
		}

		public Criteria andEntrustPrincipalTotalNotEqualTo(BigDecimal value) {
			addCriterion("entrust_principal_total <>", value, "entrustPrincipalTotal");
			return (Criteria) this;
		}

		public Criteria andEntrustPrincipalTotalGreaterThan(BigDecimal value) {
			addCriterion("entrust_principal_total >", value, "entrustPrincipalTotal");
			return (Criteria) this;
		}

		public Criteria andEntrustPrincipalTotalGreaterThanOrEqualTo(BigDecimal value) {
			addCriterion("entrust_principal_total >=", value, "entrustPrincipalTotal");
			return (Criteria) this;
		}

		public Criteria andEntrustPrincipalTotalLessThan(BigDecimal value) {
			addCriterion("entrust_principal_total <", value, "entrustPrincipalTotal");
			return (Criteria) this;
		}

		public Criteria andEntrustPrincipalTotalLessThanOrEqualTo(BigDecimal value) {
			addCriterion("entrust_principal_total <=", value, "entrustPrincipalTotal");
			return (Criteria) this;
		}

		public Criteria andEntrustPrincipalTotalIn(List<BigDecimal> values) {
			addCriterion("entrust_principal_total in", values, "entrustPrincipalTotal");
			return (Criteria) this;
		}

		public Criteria andEntrustPrincipalTotalNotIn(List<BigDecimal> values) {
			addCriterion("entrust_principal_total not in", values, "entrustPrincipalTotal");
			return (Criteria) this;
		}

		public Criteria andEntrustPrincipalTotalBetween(BigDecimal value1, BigDecimal value2) {
			addCriterion("entrust_principal_total between", value1, value2, "entrustPrincipalTotal");
			return (Criteria) this;
		}

		public Criteria andEntrustPrincipalTotalNotBetween(BigDecimal value1, BigDecimal value2) {
			addCriterion("entrust_principal_total not between", value1, value2, "entrustPrincipalTotal");
			return (Criteria) this;
		}

		public Criteria andEntrustOverduePeriodIsNull() {
			addCriterion("entrust_overdue_period is null");
			return (Criteria) this;
		}

		public Criteria andEntrustOverduePeriodIsNotNull() {
			addCriterion("entrust_overdue_period is not null");
			return (Criteria) this;
		}

		public Criteria andEntrustOverduePeriodEqualTo(String value) {
			addCriterion("entrust_overdue_period =", value, "entrustOverduePeriod");
			return (Criteria) this;
		}

		public Criteria andEntrustOverduePeriodNotEqualTo(String value) {
			addCriterion("entrust_overdue_period <>", value, "entrustOverduePeriod");
			return (Criteria) this;
		}

		public Criteria andEntrustOverduePeriodGreaterThan(String value) {
			addCriterion("entrust_overdue_period >", value, "entrustOverduePeriod");
			return (Criteria) this;
		}

		public Criteria andEntrustOverduePeriodGreaterThanOrEqualTo(String value) {
			addCriterion("entrust_overdue_period >=", value, "entrustOverduePeriod");
			return (Criteria) this;
		}

		public Criteria andEntrustOverduePeriodLessThan(String value) {
			addCriterion("entrust_overdue_period <", value, "entrustOverduePeriod");
			return (Criteria) this;
		}

		public Criteria andEntrustOverduePeriodLessThanOrEqualTo(String value) {
			addCriterion("entrust_overdue_period <=", value, "entrustOverduePeriod");
			return (Criteria) this;
		}

		public Criteria andEntrustOverduePeriodLike(String value) {
			addCriterion("entrust_overdue_period like", value, "entrustOverduePeriod");
			return (Criteria) this;
		}

		public Criteria andEntrustOverduePeriodNotLike(String value) {
			addCriterion("entrust_overdue_period not like", value, "entrustOverduePeriod");
			return (Criteria) this;
		}

		public Criteria andEntrustOverduePeriodIn(List<String> values) {
			addCriterion("entrust_overdue_period in", values, "entrustOverduePeriod");
			return (Criteria) this;
		}

		public Criteria andEntrustOverduePeriodNotIn(List<String> values) {
			addCriterion("entrust_overdue_period not in", values, "entrustOverduePeriod");
			return (Criteria) this;
		}

		public Criteria andEntrustOverduePeriodBetween(String value1, String value2) {
			addCriterion("entrust_overdue_period between", value1, value2, "entrustOverduePeriod");
			return (Criteria) this;
		}

		public Criteria andEntrustOverduePeriodNotBetween(String value1, String value2) {
			addCriterion("entrust_overdue_period not between", value1, value2, "entrustOverduePeriod");
			return (Criteria) this;
		}

		public Criteria andTargetPeriodIsNull() {
			addCriterion("target_period is null");
			return (Criteria) this;
		}

		public Criteria andTargetPeriodIsNotNull() {
			addCriterion("target_period is not null");
			return (Criteria) this;
		}

		public Criteria andTargetPeriodEqualTo(String value) {
			addCriterion("target_period =", value, "targetPeriod");
			return (Criteria) this;
		}

		public Criteria andTargetPeriodNotEqualTo(String value) {
			addCriterion("target_period <>", value, "targetPeriod");
			return (Criteria) this;
		}

		public Criteria andTargetPeriodGreaterThan(String value) {
			addCriterion("target_period >", value, "targetPeriod");
			return (Criteria) this;
		}

		public Criteria andTargetPeriodGreaterThanOrEqualTo(String value) {
			addCriterion("target_period >=", value, "targetPeriod");
			return (Criteria) this;
		}

		public Criteria andTargetPeriodLessThan(String value) {
			addCriterion("target_period <", value, "targetPeriod");
			return (Criteria) this;
		}

		public Criteria andTargetPeriodLessThanOrEqualTo(String value) {
			addCriterion("target_period <=", value, "targetPeriod");
			return (Criteria) this;
		}

		public Criteria andTargetPeriodLike(String value) {
			addCriterion("target_period like", value, "targetPeriod");
			return (Criteria) this;
		}

		public Criteria andTargetPeriodNotLike(String value) {
			addCriterion("target_period not like", value, "targetPeriod");
			return (Criteria) this;
		}

		public Criteria andTargetPeriodIn(List<String> values) {
			addCriterion("target_period in", values, "targetPeriod");
			return (Criteria) this;
		}

		public Criteria andTargetPeriodNotIn(List<String> values) {
			addCriterion("target_period not in", values, "targetPeriod");
			return (Criteria) this;
		}

		public Criteria andTargetPeriodBetween(String value1, String value2) {
			addCriterion("target_period between", value1, value2, "targetPeriod");
			return (Criteria) this;
		}

		public Criteria andTargetPeriodNotBetween(String value1, String value2) {
			addCriterion("target_period not between", value1, value2, "targetPeriod");
			return (Criteria) this;
		}

		public Criteria andCaseCityIsNull() {
			addCriterion("case_city is null");
			return (Criteria) this;
		}

		public Criteria andCaseCityIsNotNull() {
			addCriterion("case_city is not null");
			return (Criteria) this;
		}

		public Criteria andCaseCityEqualTo(String value) {
			addCriterion("case_city =", value, "caseCity");
			return (Criteria) this;
		}

		public Criteria andCaseCityNotEqualTo(String value) {
			addCriterion("case_city <>", value, "caseCity");
			return (Criteria) this;
		}

		public Criteria andCaseCityGreaterThan(String value) {
			addCriterion("case_city >", value, "caseCity");
			return (Criteria) this;
		}

		public Criteria andCaseCityGreaterThanOrEqualTo(String value) {
			addCriterion("case_city >=", value, "caseCity");
			return (Criteria) this;
		}

		public Criteria andCaseCityLessThan(String value) {
			addCriterion("case_city <", value, "caseCity");
			return (Criteria) this;
		}

		public Criteria andCaseCityLessThanOrEqualTo(String value) {
			addCriterion("case_city <=", value, "caseCity");
			return (Criteria) this;
		}

		public Criteria andCaseCityLike(String value) {
			addCriterion("case_city like", value, "caseCity");
			return (Criteria) this;
		}

		public Criteria andCaseCityNotLike(String value) {
			addCriterion("case_city not like", value, "caseCity");
			return (Criteria) this;
		}

		public Criteria andCaseCityIn(List<String> values) {
			addCriterion("case_city in", values, "caseCity");
			return (Criteria) this;
		}

		public Criteria andCaseCityNotIn(List<String> values) {
			addCriterion("case_city not in", values, "caseCity");
			return (Criteria) this;
		}

		public Criteria andCaseCityBetween(String value1, String value2) {
			addCriterion("case_city between", value1, value2, "caseCity");
			return (Criteria) this;
		}

		public Criteria andCaseCityNotBetween(String value1, String value2) {
			addCriterion("case_city not between", value1, value2, "caseCity");
			return (Criteria) this;
		}

		public Criteria andLastFollowUpDateIsNull() {
			addCriterion("last_follow_up_date is null");
			return (Criteria) this;
		}

		public Criteria andLastFollowUpDateIsNotNull() {
			addCriterion("last_follow_up_date is not null");
			return (Criteria) this;
		}

		public Criteria andLastFollowUpDateEqualTo(Date value) {
			addCriterionForJDBCDate("last_follow_up_date =", value, "lastFollowUpDate");
			return (Criteria) this;
		}

		public Criteria andLastFollowUpDateNotEqualTo(Date value) {
			addCriterionForJDBCDate("last_follow_up_date <>", value, "lastFollowUpDate");
			return (Criteria) this;
		}

		public Criteria andLastFollowUpDateGreaterThan(Date value) {
			addCriterionForJDBCDate("last_follow_up_date >", value, "lastFollowUpDate");
			return (Criteria) this;
		}

		public Criteria andLastFollowUpDateGreaterThanOrEqualTo(Date value) {
			addCriterionForJDBCDate("last_follow_up_date >=", value, "lastFollowUpDate");
			return (Criteria) this;
		}

		public Criteria andLastFollowUpDateLessThan(Date value) {
			addCriterionForJDBCDate("last_follow_up_date <", value, "lastFollowUpDate");
			return (Criteria) this;
		}

		public Criteria andLastFollowUpDateLessThanOrEqualTo(Date value) {
			addCriterionForJDBCDate("last_follow_up_date <=", value, "lastFollowUpDate");
			return (Criteria) this;
		}

		public Criteria andLastFollowUpDateIn(List<Date> values) {
			addCriterionForJDBCDate("last_follow_up_date in", values, "lastFollowUpDate");
			return (Criteria) this;
		}

		public Criteria andLastFollowUpDateNotIn(List<Date> values) {
			addCriterionForJDBCDate("last_follow_up_date not in", values, "lastFollowUpDate");
			return (Criteria) this;
		}

		public Criteria andLastFollowUpDateBetween(Date value1, Date value2) {
			addCriterionForJDBCDate("last_follow_up_date between", value1, value2, "lastFollowUpDate");
			return (Criteria) this;
		}

		public Criteria andLastFollowUpDateNotBetween(Date value1, Date value2) {
			addCriterionForJDBCDate("last_follow_up_date not between", value1, value2, "lastFollowUpDate");
			return (Criteria) this;
		}

		public Criteria andEntrustStartDateIsNull() {
			addCriterion("entrust_start_date is null");
			return (Criteria) this;
		}

		public Criteria andEntrustStartDateIsNotNull() {
			addCriterion("entrust_start_date is not null");
			return (Criteria) this;
		}

		public Criteria andEntrustStartDateEqualTo(Date value) {
			addCriterionForJDBCDate("entrust_start_date =", value, "entrustStartDate");
			return (Criteria) this;
		}

		public Criteria andEntrustStartDateNotEqualTo(Date value) {
			addCriterionForJDBCDate("entrust_start_date <>", value, "entrustStartDate");
			return (Criteria) this;
		}

		public Criteria andEntrustStartDateGreaterThan(Date value) {
			addCriterionForJDBCDate("entrust_start_date >", value, "entrustStartDate");
			return (Criteria) this;
		}

		public Criteria andEntrustStartDateGreaterThanOrEqualTo(Date value) {
			addCriterionForJDBCDate("entrust_start_date >=", value, "entrustStartDate");
			return (Criteria) this;
		}

		public Criteria andEntrustStartDateLessThan(Date value) {
			addCriterionForJDBCDate("entrust_start_date <", value, "entrustStartDate");
			return (Criteria) this;
		}

		public Criteria andEntrustStartDateLessThanOrEqualTo(Date value) {
			addCriterionForJDBCDate("entrust_start_date <=", value, "entrustStartDate");
			return (Criteria) this;
		}

		public Criteria andEntrustStartDateIn(List<Date> values) {
			addCriterionForJDBCDate("entrust_start_date in", values, "entrustStartDate");
			return (Criteria) this;
		}

		public Criteria andEntrustStartDateNotIn(List<Date> values) {
			addCriterionForJDBCDate("entrust_start_date not in", values, "entrustStartDate");
			return (Criteria) this;
		}

		public Criteria andEntrustStartDateBetween(Date value1, Date value2) {
			addCriterionForJDBCDate("entrust_start_date between", value1, value2, "entrustStartDate");
			return (Criteria) this;
		}

		public Criteria andEntrustStartDateNotBetween(Date value1, Date value2) {
			addCriterionForJDBCDate("entrust_start_date not between", value1, value2, "entrustStartDate");
			return (Criteria) this;
		}

		public Criteria andEntrustEndDateIsNull() {
			addCriterion("entrust_end_date is null");
			return (Criteria) this;
		}

		public Criteria andEntrustEndDateIsNotNull() {
			addCriterion("entrust_end_date is not null");
			return (Criteria) this;
		}

		public Criteria andEntrustEndDateEqualTo(Date value) {
			addCriterionForJDBCDate("entrust_end_date =", value, "entrustEndDate");
			return (Criteria) this;
		}

		public Criteria andEntrustEndDateNotEqualTo(Date value) {
			addCriterionForJDBCDate("entrust_end_date <>", value, "entrustEndDate");
			return (Criteria) this;
		}

		public Criteria andEntrustEndDateGreaterThan(Date value) {
			addCriterionForJDBCDate("entrust_end_date >", value, "entrustEndDate");
			return (Criteria) this;
		}

		public Criteria andEntrustEndDateGreaterThanOrEqualTo(Date value) {
			addCriterionForJDBCDate("entrust_end_date >=", value, "entrustEndDate");
			return (Criteria) this;
		}

		public Criteria andEntrustEndDateLessThan(Date value) {
			addCriterionForJDBCDate("entrust_end_date <", value, "entrustEndDate");
			return (Criteria) this;
		}

		public Criteria andEntrustEndDateLessThanOrEqualTo(Date value) {
			addCriterionForJDBCDate("entrust_end_date <=", value, "entrustEndDate");
			return (Criteria) this;
		}

		public Criteria andEntrustEndDateIn(List<Date> values) {
			addCriterionForJDBCDate("entrust_end_date in", values, "entrustEndDate");
			return (Criteria) this;
		}

		public Criteria andEntrustEndDateNotIn(List<Date> values) {
			addCriterionForJDBCDate("entrust_end_date not in", values, "entrustEndDate");
			return (Criteria) this;
		}

		public Criteria andEntrustEndDateBetween(Date value1, Date value2) {
			addCriterionForJDBCDate("entrust_end_date between", value1, value2, "entrustEndDate");
			return (Criteria) this;
		}

		public Criteria andEntrustEndDateNotBetween(Date value1, Date value2) {
			addCriterionForJDBCDate("entrust_end_date not between", value1, value2, "entrustEndDate");
			return (Criteria) this;
		}

		public Criteria andCaseTypeIsNull() {
			addCriterion("case_type is null");
			return (Criteria) this;
		}

		public Criteria andCaseTypeIsNotNull() {
			addCriterion("case_type is not null");
			return (Criteria) this;
		}

		public Criteria andCaseTypeEqualTo(String value) {
			addCriterion("case_type =", value, "caseType");
			return (Criteria) this;
		}

		public Criteria andCaseTypeNotEqualTo(String value) {
			addCriterion("case_type <>", value, "caseType");
			return (Criteria) this;
		}

		public Criteria andCaseTypeGreaterThan(String value) {
			addCriterion("case_type >", value, "caseType");
			return (Criteria) this;
		}

		public Criteria andCaseTypeGreaterThanOrEqualTo(String value) {
			addCriterion("case_type >=", value, "caseType");
			return (Criteria) this;
		}

		public Criteria andCaseTypeLessThan(String value) {
			addCriterion("case_type <", value, "caseType");
			return (Criteria) this;
		}

		public Criteria andCaseTypeLessThanOrEqualTo(String value) {
			addCriterion("case_type <=", value, "caseType");
			return (Criteria) this;
		}

		public Criteria andCaseTypeLike(String value) {
			addCriterion("case_type like", value, "caseType");
			return (Criteria) this;
		}

		public Criteria andCaseTypeNotLike(String value) {
			addCriterion("case_type not like", value, "caseType");
			return (Criteria) this;
		}

		public Criteria andCaseTypeIn(List<String> values) {
			addCriterion("case_type in", values, "caseType");
			return (Criteria) this;
		}

		public Criteria andCaseTypeNotIn(List<String> values) {
			addCriterion("case_type not in", values, "caseType");
			return (Criteria) this;
		}

		public Criteria andCaseTypeBetween(String value1, String value2) {
			addCriterion("case_type between", value1, value2, "caseType");
			return (Criteria) this;
		}

		public Criteria andCaseTypeNotBetween(String value1, String value2) {
			addCriterion("case_type not between", value1, value2, "caseType");
			return (Criteria) this;
		}

		public Criteria andNewOldCaseFlagIsNull() {
			addCriterion("new_old_case_flag is null");
			return (Criteria) this;
		}

		public Criteria andNewOldCaseFlagIsNotNull() {
			addCriterion("new_old_case_flag is not null");
			return (Criteria) this;
		}

		public Criteria andNewOldCaseFlagEqualTo(String value) {
			addCriterion("new_old_case_flag =", value, "newOldCaseFlag");
			return (Criteria) this;
		}

		public Criteria andNewOldCaseFlagNotEqualTo(String value) {
			addCriterion("new_old_case_flag <>", value, "newOldCaseFlag");
			return (Criteria) this;
		}

		public Criteria andNewOldCaseFlagGreaterThan(String value) {
			addCriterion("new_old_case_flag >", value, "newOldCaseFlag");
			return (Criteria) this;
		}

		public Criteria andNewOldCaseFlagGreaterThanOrEqualTo(String value) {
			addCriterion("new_old_case_flag >=", value, "newOldCaseFlag");
			return (Criteria) this;
		}

		public Criteria andNewOldCaseFlagLessThan(String value) {
			addCriterion("new_old_case_flag <", value, "newOldCaseFlag");
			return (Criteria) this;
		}

		public Criteria andNewOldCaseFlagLessThanOrEqualTo(String value) {
			addCriterion("new_old_case_flag <=", value, "newOldCaseFlag");
			return (Criteria) this;
		}

		public Criteria andNewOldCaseFlagLike(String value) {
			addCriterion("new_old_case_flag like", value, "newOldCaseFlag");
			return (Criteria) this;
		}

		public Criteria andNewOldCaseFlagNotLike(String value) {
			addCriterion("new_old_case_flag not like", value, "newOldCaseFlag");
			return (Criteria) this;
		}

		public Criteria andNewOldCaseFlagIn(List<String> values) {
			addCriterion("new_old_case_flag in", values, "newOldCaseFlag");
			return (Criteria) this;
		}

		public Criteria andNewOldCaseFlagNotIn(List<String> values) {
			addCriterion("new_old_case_flag not in", values, "newOldCaseFlag");
			return (Criteria) this;
		}

		public Criteria andNewOldCaseFlagBetween(String value1, String value2) {
			addCriterion("new_old_case_flag between", value1, value2, "newOldCaseFlag");
			return (Criteria) this;
		}

		public Criteria andNewOldCaseFlagNotBetween(String value1, String value2) {
			addCriterion("new_old_case_flag not between", value1, value2, "newOldCaseFlag");
			return (Criteria) this;
		}

		public Criteria andBatchNumberIsNull() {
			addCriterion("batch_number is null");
			return (Criteria) this;
		}

		public Criteria andBatchNumberIsNotNull() {
			addCriterion("batch_number is not null");
			return (Criteria) this;
		}

		public Criteria andBatchNumberEqualTo(String value) {
			addCriterion("batch_number =", value, "batchNumber");
			return (Criteria) this;
		}

		public Criteria andBatchNumberNotEqualTo(String value) {
			addCriterion("batch_number <>", value, "batchNumber");
			return (Criteria) this;
		}

		public Criteria andBatchNumberGreaterThan(String value) {
			addCriterion("batch_number >", value, "batchNumber");
			return (Criteria) this;
		}

		public Criteria andBatchNumberGreaterThanOrEqualTo(String value) {
			addCriterion("batch_number >=", value, "batchNumber");
			return (Criteria) this;
		}

		public Criteria andBatchNumberLessThan(String value) {
			addCriterion("batch_number <", value, "batchNumber");
			return (Criteria) this;
		}

		public Criteria andBatchNumberLessThanOrEqualTo(String value) {
			addCriterion("batch_number <=", value, "batchNumber");
			return (Criteria) this;
		}

		public Criteria andBatchNumberLike(String value) {
			addCriterion("batch_number like", value, "batchNumber");
			return (Criteria) this;
		}

		public Criteria andBatchNumberNotLike(String value) {
			addCriterion("batch_number not like", value, "batchNumber");
			return (Criteria) this;
		}

		public Criteria andBatchNumberIn(List<String> values) {
			addCriterion("batch_number in", values, "batchNumber");
			return (Criteria) this;
		}

		public Criteria andBatchNumberNotIn(List<String> values) {
			addCriterion("batch_number not in", values, "batchNumber");
			return (Criteria) this;
		}

		public Criteria andBatchNumberBetween(String value1, String value2) {
			addCriterion("batch_number between", value1, value2, "batchNumber");
			return (Criteria) this;
		}

		public Criteria andBatchNumberNotBetween(String value1, String value2) {
			addCriterion("batch_number not between", value1, value2, "batchNumber");
			return (Criteria) this;
		}

		public Criteria andBalanceOpsIsNull() {
			addCriterion("balance_ops is null");
			return (Criteria) this;
		}

		public Criteria andBalanceOpsIsNotNull() {
			addCriterion("balance_ops is not null");
			return (Criteria) this;
		}

		public Criteria andBalanceOpsEqualTo(BigDecimal value) {
			addCriterion("balance_ops =", value, "balanceOps");
			return (Criteria) this;
		}

		public Criteria andBalanceOpsNotEqualTo(BigDecimal value) {
			addCriterion("balance_ops <>", value, "balanceOps");
			return (Criteria) this;
		}

		public Criteria andBalanceOpsGreaterThan(BigDecimal value) {
			addCriterion("balance_ops >", value, "balanceOps");
			return (Criteria) this;
		}

		public Criteria andBalanceOpsGreaterThanOrEqualTo(BigDecimal value) {
			addCriterion("balance_ops >=", value, "balanceOps");
			return (Criteria) this;
		}

		public Criteria andBalanceOpsLessThan(BigDecimal value) {
			addCriterion("balance_ops <", value, "balanceOps");
			return (Criteria) this;
		}

		public Criteria andBalanceOpsLessThanOrEqualTo(BigDecimal value) {
			addCriterion("balance_ops <=", value, "balanceOps");
			return (Criteria) this;
		}

		public Criteria andBalanceOpsIn(List<BigDecimal> values) {
			addCriterion("balance_ops in", values, "balanceOps");
			return (Criteria) this;
		}

		public Criteria andBalanceOpsNotIn(List<BigDecimal> values) {
			addCriterion("balance_ops not in", values, "balanceOps");
			return (Criteria) this;
		}

		public Criteria andBalanceOpsBetween(BigDecimal value1, BigDecimal value2) {
			addCriterion("balance_ops between", value1, value2, "balanceOps");
			return (Criteria) this;
		}

		public Criteria andBalanceOpsNotBetween(BigDecimal value1, BigDecimal value2) {
			addCriterion("balance_ops not between", value1, value2, "balanceOps");
			return (Criteria) this;
		}

		public Criteria andPrincipalOpsIsNull() {
			addCriterion("principal_ops is null");
			return (Criteria) this;
		}

		public Criteria andPrincipalOpsIsNotNull() {
			addCriterion("principal_ops is not null");
			return (Criteria) this;
		}

		public Criteria andPrincipalOpsEqualTo(BigDecimal value) {
			addCriterion("principal_ops =", value, "principalOps");
			return (Criteria) this;
		}

		public Criteria andPrincipalOpsNotEqualTo(BigDecimal value) {
			addCriterion("principal_ops <>", value, "principalOps");
			return (Criteria) this;
		}

		public Criteria andPrincipalOpsGreaterThan(BigDecimal value) {
			addCriterion("principal_ops >", value, "principalOps");
			return (Criteria) this;
		}

		public Criteria andPrincipalOpsGreaterThanOrEqualTo(BigDecimal value) {
			addCriterion("principal_ops >=", value, "principalOps");
			return (Criteria) this;
		}

		public Criteria andPrincipalOpsLessThan(BigDecimal value) {
			addCriterion("principal_ops <", value, "principalOps");
			return (Criteria) this;
		}

		public Criteria andPrincipalOpsLessThanOrEqualTo(BigDecimal value) {
			addCriterion("principal_ops <=", value, "principalOps");
			return (Criteria) this;
		}

		public Criteria andPrincipalOpsIn(List<BigDecimal> values) {
			addCriterion("principal_ops in", values, "principalOps");
			return (Criteria) this;
		}

		public Criteria andPrincipalOpsNotIn(List<BigDecimal> values) {
			addCriterion("principal_ops not in", values, "principalOps");
			return (Criteria) this;
		}

		public Criteria andPrincipalOpsBetween(BigDecimal value1, BigDecimal value2) {
			addCriterion("principal_ops between", value1, value2, "principalOps");
			return (Criteria) this;
		}

		public Criteria andPrincipalOpsNotBetween(BigDecimal value1, BigDecimal value2) {
			addCriterion("principal_ops not between", value1, value2, "principalOps");
			return (Criteria) this;
		}

		public Criteria andCurrentOverduePeriodIsNull() {
			addCriterion("current_overdue_period is null");
			return (Criteria) this;
		}

		public Criteria andCurrentOverduePeriodIsNotNull() {
			addCriterion("current_overdue_period is not null");
			return (Criteria) this;
		}

		public Criteria andCurrentOverduePeriodEqualTo(String value) {
			addCriterion("current_overdue_period =", value, "currentOverduePeriod");
			return (Criteria) this;
		}

		public Criteria andCurrentOverduePeriodNotEqualTo(String value) {
			addCriterion("current_overdue_period <>", value, "currentOverduePeriod");
			return (Criteria) this;
		}

		public Criteria andCurrentOverduePeriodGreaterThan(String value) {
			addCriterion("current_overdue_period >", value, "currentOverduePeriod");
			return (Criteria) this;
		}

		public Criteria andCurrentOverduePeriodGreaterThanOrEqualTo(String value) {
			addCriterion("current_overdue_period >=", value, "currentOverduePeriod");
			return (Criteria) this;
		}

		public Criteria andCurrentOverduePeriodLessThan(String value) {
			addCriterion("current_overdue_period <", value, "currentOverduePeriod");
			return (Criteria) this;
		}

		public Criteria andCurrentOverduePeriodLessThanOrEqualTo(String value) {
			addCriterion("current_overdue_period <=", value, "currentOverduePeriod");
			return (Criteria) this;
		}

		public Criteria andCurrentOverduePeriodLike(String value) {
			addCriterion("current_overdue_period like", value, "currentOverduePeriod");
			return (Criteria) this;
		}

		public Criteria andCurrentOverduePeriodNotLike(String value) {
			addCriterion("current_overdue_period not like", value, "currentOverduePeriod");
			return (Criteria) this;
		}

		public Criteria andCurrentOverduePeriodIn(List<String> values) {
			addCriterion("current_overdue_period in", values, "currentOverduePeriod");
			return (Criteria) this;
		}

		public Criteria andCurrentOverduePeriodNotIn(List<String> values) {
			addCriterion("current_overdue_period not in", values, "currentOverduePeriod");
			return (Criteria) this;
		}

		public Criteria andCurrentOverduePeriodBetween(String value1, String value2) {
			addCriterion("current_overdue_period between", value1, value2, "currentOverduePeriod");
			return (Criteria) this;
		}

		public Criteria andCurrentOverduePeriodNotBetween(String value1, String value2) {
			addCriterion("current_overdue_period not between", value1, value2, "currentOverduePeriod");
			return (Criteria) this;
		}

		public Criteria andHouseholdCityIsNull() {
			addCriterion("household_city is null");
			return (Criteria) this;
		}

		public Criteria andHouseholdCityIsNotNull() {
			addCriterion("household_city is not null");
			return (Criteria) this;
		}

		public Criteria andHouseholdCityEqualTo(String value) {
			addCriterion("household_city =", value, "householdCity");
			return (Criteria) this;
		}

		public Criteria andHouseholdCityNotEqualTo(String value) {
			addCriterion("household_city <>", value, "householdCity");
			return (Criteria) this;
		}

		public Criteria andHouseholdCityGreaterThan(String value) {
			addCriterion("household_city >", value, "householdCity");
			return (Criteria) this;
		}

		public Criteria andHouseholdCityGreaterThanOrEqualTo(String value) {
			addCriterion("household_city >=", value, "householdCity");
			return (Criteria) this;
		}

		public Criteria andHouseholdCityLessThan(String value) {
			addCriterion("household_city <", value, "householdCity");
			return (Criteria) this;
		}

		public Criteria andHouseholdCityLessThanOrEqualTo(String value) {
			addCriterion("household_city <=", value, "householdCity");
			return (Criteria) this;
		}

		public Criteria andHouseholdCityLike(String value) {
			addCriterion("household_city like", value, "householdCity");
			return (Criteria) this;
		}

		public Criteria andHouseholdCityNotLike(String value) {
			addCriterion("household_city not like", value, "householdCity");
			return (Criteria) this;
		}

		public Criteria andHouseholdCityIn(List<String> values) {
			addCriterion("household_city in", values, "householdCity");
			return (Criteria) this;
		}

		public Criteria andHouseholdCityNotIn(List<String> values) {
			addCriterion("household_city not in", values, "householdCity");
			return (Criteria) this;
		}

		public Criteria andHouseholdCityBetween(String value1, String value2) {
			addCriterion("household_city between", value1, value2, "householdCity");
			return (Criteria) this;
		}

		public Criteria andHouseholdCityNotBetween(String value1, String value2) {
			addCriterion("household_city not between", value1, value2, "householdCity");
			return (Criteria) this;
		}

		public Criteria andCustomerAgeIsNull() {
			addCriterion("customer_age is null");
			return (Criteria) this;
		}

		public Criteria andCustomerAgeIsNotNull() {
			addCriterion("customer_age is not null");
			return (Criteria) this;
		}

		public Criteria andCustomerAgeEqualTo(Integer value) {
			addCriterion("customer_age =", value, "customerAge");
			return (Criteria) this;
		}

		public Criteria andCustomerAgeNotEqualTo(Integer value) {
			addCriterion("customer_age <>", value, "customerAge");
			return (Criteria) this;
		}

		public Criteria andCustomerAgeGreaterThan(Integer value) {
			addCriterion("customer_age >", value, "customerAge");
			return (Criteria) this;
		}

		public Criteria andCustomerAgeGreaterThanOrEqualTo(Integer value) {
			addCriterion("customer_age >=", value, "customerAge");
			return (Criteria) this;
		}

		public Criteria andCustomerAgeLessThan(Integer value) {
			addCriterion("customer_age <", value, "customerAge");
			return (Criteria) this;
		}

		public Criteria andCustomerAgeLessThanOrEqualTo(Integer value) {
			addCriterion("customer_age <=", value, "customerAge");
			return (Criteria) this;
		}

		public Criteria andCustomerAgeIn(List<Integer> values) {
			addCriterion("customer_age in", values, "customerAge");
			return (Criteria) this;
		}

		public Criteria andCustomerAgeNotIn(List<Integer> values) {
			addCriterion("customer_age not in", values, "customerAge");
			return (Criteria) this;
		}

		public Criteria andCustomerAgeBetween(Integer value1, Integer value2) {
			addCriterion("customer_age between", value1, value2, "customerAge");
			return (Criteria) this;
		}

		public Criteria andCustomerAgeNotBetween(Integer value1, Integer value2) {
			addCriterion("customer_age not between", value1, value2, "customerAge");
			return (Criteria) this;
		}

		public Criteria andOccupationIsNull() {
			addCriterion("occupation is null");
			return (Criteria) this;
		}

		public Criteria andOccupationIsNotNull() {
			addCriterion("occupation is not null");
			return (Criteria) this;
		}

		public Criteria andOccupationEqualTo(String value) {
			addCriterion("occupation =", value, "occupation");
			return (Criteria) this;
		}

		public Criteria andOccupationNotEqualTo(String value) {
			addCriterion("occupation <>", value, "occupation");
			return (Criteria) this;
		}

		public Criteria andOccupationGreaterThan(String value) {
			addCriterion("occupation >", value, "occupation");
			return (Criteria) this;
		}

		public Criteria andOccupationGreaterThanOrEqualTo(String value) {
			addCriterion("occupation >=", value, "occupation");
			return (Criteria) this;
		}

		public Criteria andOccupationLessThan(String value) {
			addCriterion("occupation <", value, "occupation");
			return (Criteria) this;
		}

		public Criteria andOccupationLessThanOrEqualTo(String value) {
			addCriterion("occupation <=", value, "occupation");
			return (Criteria) this;
		}

		public Criteria andOccupationLike(String value) {
			addCriterion("occupation like", value, "occupation");
			return (Criteria) this;
		}

		public Criteria andOccupationNotLike(String value) {
			addCriterion("occupation not like", value, "occupation");
			return (Criteria) this;
		}

		public Criteria andOccupationIn(List<String> values) {
			addCriterion("occupation in", values, "occupation");
			return (Criteria) this;
		}

		public Criteria andOccupationNotIn(List<String> values) {
			addCriterion("occupation not in", values, "occupation");
			return (Criteria) this;
		}

		public Criteria andOccupationBetween(String value1, String value2) {
			addCriterion("occupation between", value1, value2, "occupation");
			return (Criteria) this;
		}

		public Criteria andOccupationNotBetween(String value1, String value2) {
			addCriterion("occupation not between", value1, value2, "occupation");
			return (Criteria) this;
		}

		public Criteria andCardOpeningDateIsNull() {
			addCriterion("card_opening_date is null");
			return (Criteria) this;
		}

		public Criteria andCardOpeningDateIsNotNull() {
			addCriterion("card_opening_date is not null");
			return (Criteria) this;
		}

		public Criteria andCardOpeningDateEqualTo(Date value) {
			addCriterionForJDBCDate("card_opening_date =", value, "cardOpeningDate");
			return (Criteria) this;
		}

		public Criteria andCardOpeningDateNotEqualTo(Date value) {
			addCriterionForJDBCDate("card_opening_date <>", value, "cardOpeningDate");
			return (Criteria) this;
		}

		public Criteria andCardOpeningDateGreaterThan(Date value) {
			addCriterionForJDBCDate("card_opening_date >", value, "cardOpeningDate");
			return (Criteria) this;
		}

		public Criteria andCardOpeningDateGreaterThanOrEqualTo(Date value) {
			addCriterionForJDBCDate("card_opening_date >=", value, "cardOpeningDate");
			return (Criteria) this;
		}

		public Criteria andCardOpeningDateLessThan(Date value) {
			addCriterionForJDBCDate("card_opening_date <", value, "cardOpeningDate");
			return (Criteria) this;
		}

		public Criteria andCardOpeningDateLessThanOrEqualTo(Date value) {
			addCriterionForJDBCDate("card_opening_date <=", value, "cardOpeningDate");
			return (Criteria) this;
		}

		public Criteria andCardOpeningDateIn(List<Date> values) {
			addCriterionForJDBCDate("card_opening_date in", values, "cardOpeningDate");
			return (Criteria) this;
		}

		public Criteria andCardOpeningDateNotIn(List<Date> values) {
			addCriterionForJDBCDate("card_opening_date not in", values, "cardOpeningDate");
			return (Criteria) this;
		}

		public Criteria andCardOpeningDateBetween(Date value1, Date value2) {
			addCriterionForJDBCDate("card_opening_date between", value1, value2, "cardOpeningDate");
			return (Criteria) this;
		}

		public Criteria andCardOpeningDateNotBetween(Date value1, Date value2) {
			addCriterionForJDBCDate("card_opening_date not between", value1, value2, "cardOpeningDate");
			return (Criteria) this;
		}

		public Criteria andCreditLimitIsNull() {
			addCriterion("credit_limit is null");
			return (Criteria) this;
		}

		public Criteria andCreditLimitIsNotNull() {
			addCriterion("credit_limit is not null");
			return (Criteria) this;
		}

		public Criteria andCreditLimitEqualTo(BigDecimal value) {
			addCriterion("credit_limit =", value, "creditLimit");
			return (Criteria) this;
		}

		public Criteria andCreditLimitNotEqualTo(BigDecimal value) {
			addCriterion("credit_limit <>", value, "creditLimit");
			return (Criteria) this;
		}

		public Criteria andCreditLimitGreaterThan(BigDecimal value) {
			addCriterion("credit_limit >", value, "creditLimit");
			return (Criteria) this;
		}

		public Criteria andCreditLimitGreaterThanOrEqualTo(BigDecimal value) {
			addCriterion("credit_limit >=", value, "creditLimit");
			return (Criteria) this;
		}

		public Criteria andCreditLimitLessThan(BigDecimal value) {
			addCriterion("credit_limit <", value, "creditLimit");
			return (Criteria) this;
		}

		public Criteria andCreditLimitLessThanOrEqualTo(BigDecimal value) {
			addCriterion("credit_limit <=", value, "creditLimit");
			return (Criteria) this;
		}

		public Criteria andCreditLimitIn(List<BigDecimal> values) {
			addCriterion("credit_limit in", values, "creditLimit");
			return (Criteria) this;
		}

		public Criteria andCreditLimitNotIn(List<BigDecimal> values) {
			addCriterion("credit_limit not in", values, "creditLimit");
			return (Criteria) this;
		}

		public Criteria andCreditLimitBetween(BigDecimal value1, BigDecimal value2) {
			addCriterion("credit_limit between", value1, value2, "creditLimit");
			return (Criteria) this;
		}

		public Criteria andCreditLimitNotBetween(BigDecimal value1, BigDecimal value2) {
			addCriterion("credit_limit not between", value1, value2, "creditLimit");
			return (Criteria) this;
		}

		public Criteria andLastPaymentDateIsNull() {
			addCriterion("last_payment_date is null");
			return (Criteria) this;
		}

		public Criteria andLastPaymentDateIsNotNull() {
			addCriterion("last_payment_date is not null");
			return (Criteria) this;
		}

		public Criteria andLastPaymentDateEqualTo(Date value) {
			addCriterionForJDBCDate("last_payment_date =", value, "lastPaymentDate");
			return (Criteria) this;
		}

		public Criteria andLastPaymentDateNotEqualTo(Date value) {
			addCriterionForJDBCDate("last_payment_date <>", value, "lastPaymentDate");
			return (Criteria) this;
		}

		public Criteria andLastPaymentDateGreaterThan(Date value) {
			addCriterionForJDBCDate("last_payment_date >", value, "lastPaymentDate");
			return (Criteria) this;
		}

		public Criteria andLastPaymentDateGreaterThanOrEqualTo(Date value) {
			addCriterionForJDBCDate("last_payment_date >=", value, "lastPaymentDate");
			return (Criteria) this;
		}

		public Criteria andLastPaymentDateLessThan(Date value) {
			addCriterionForJDBCDate("last_payment_date <", value, "lastPaymentDate");
			return (Criteria) this;
		}

		public Criteria andLastPaymentDateLessThanOrEqualTo(Date value) {
			addCriterionForJDBCDate("last_payment_date <=", value, "lastPaymentDate");
			return (Criteria) this;
		}

		public Criteria andLastPaymentDateIn(List<Date> values) {
			addCriterionForJDBCDate("last_payment_date in", values, "lastPaymentDate");
			return (Criteria) this;
		}

		public Criteria andLastPaymentDateNotIn(List<Date> values) {
			addCriterionForJDBCDate("last_payment_date not in", values, "lastPaymentDate");
			return (Criteria) this;
		}

		public Criteria andLastPaymentDateBetween(Date value1, Date value2) {
			addCriterionForJDBCDate("last_payment_date between", value1, value2, "lastPaymentDate");
			return (Criteria) this;
		}

		public Criteria andLastPaymentDateNotBetween(Date value1, Date value2) {
			addCriterionForJDBCDate("last_payment_date not between", value1, value2, "lastPaymentDate");
			return (Criteria) this;
		}

		public Criteria andSkillGroupIsNull() {
			addCriterion("skill_group is null");
			return (Criteria) this;
		}

		public Criteria andSkillGroupIsNotNull() {
			addCriterion("skill_group is not null");
			return (Criteria) this;
		}

		public Criteria andSkillGroupEqualTo(String value) {
			addCriterion("skill_group =", value, "skillGroup");
			return (Criteria) this;
		}

		public Criteria andSkillGroupNotEqualTo(String value) {
			addCriterion("skill_group <>", value, "skillGroup");
			return (Criteria) this;
		}

		public Criteria andSkillGroupGreaterThan(String value) {
			addCriterion("skill_group >", value, "skillGroup");
			return (Criteria) this;
		}

		public Criteria andSkillGroupGreaterThanOrEqualTo(String value) {
			addCriterion("skill_group >=", value, "skillGroup");
			return (Criteria) this;
		}

		public Criteria andSkillGroupLessThan(String value) {
			addCriterion("skill_group <", value, "skillGroup");
			return (Criteria) this;
		}

		public Criteria andSkillGroupLessThanOrEqualTo(String value) {
			addCriterion("skill_group <=", value, "skillGroup");
			return (Criteria) this;
		}

		public Criteria andSkillGroupLike(String value) {
			addCriterion("skill_group like", value, "skillGroup");
			return (Criteria) this;
		}

		public Criteria andSkillGroupNotLike(String value) {
			addCriterion("skill_group not like", value, "skillGroup");
			return (Criteria) this;
		}

		public Criteria andSkillGroupIn(List<String> values) {
			addCriterion("skill_group in", values, "skillGroup");
			return (Criteria) this;
		}

		public Criteria andSkillGroupNotIn(List<String> values) {
			addCriterion("skill_group not in", values, "skillGroup");
			return (Criteria) this;
		}

		public Criteria andSkillGroupBetween(String value1, String value2) {
			addCriterion("skill_group between", value1, value2, "skillGroup");
			return (Criteria) this;
		}

		public Criteria andSkillGroupNotBetween(String value1, String value2) {
			addCriterion("skill_group not between", value1, value2, "skillGroup");
			return (Criteria) this;
		}

		public Criteria andAccountNumberLast7IsNull() {
			addCriterion("account_number_last7 is null");
			return (Criteria) this;
		}

		public Criteria andAccountNumberLast7IsNotNull() {
			addCriterion("account_number_last7 is not null");
			return (Criteria) this;
		}

		public Criteria andAccountNumberLast7EqualTo(String value) {
			addCriterion("account_number_last7 =", value, "accountNumberLast7");
			return (Criteria) this;
		}

		public Criteria andAccountNumberLast7NotEqualTo(String value) {
			addCriterion("account_number_last7 <>", value, "accountNumberLast7");
			return (Criteria) this;
		}

		public Criteria andAccountNumberLast7GreaterThan(String value) {
			addCriterion("account_number_last7 >", value, "accountNumberLast7");
			return (Criteria) this;
		}

		public Criteria andAccountNumberLast7GreaterThanOrEqualTo(String value) {
			addCriterion("account_number_last7 >=", value, "accountNumberLast7");
			return (Criteria) this;
		}

		public Criteria andAccountNumberLast7LessThan(String value) {
			addCriterion("account_number_last7 <", value, "accountNumberLast7");
			return (Criteria) this;
		}

		public Criteria andAccountNumberLast7LessThanOrEqualTo(String value) {
			addCriterion("account_number_last7 <=", value, "accountNumberLast7");
			return (Criteria) this;
		}

		public Criteria andAccountNumberLast7Like(String value) {
			addCriterion("account_number_last7 like", value, "accountNumberLast7");
			return (Criteria) this;
		}

		public Criteria andAccountNumberLast7NotLike(String value) {
			addCriterion("account_number_last7 not like", value, "accountNumberLast7");
			return (Criteria) this;
		}

		public Criteria andAccountNumberLast7In(List<String> values) {
			addCriterion("account_number_last7 in", values, "accountNumberLast7");
			return (Criteria) this;
		}

		public Criteria andAccountNumberLast7NotIn(List<String> values) {
			addCriterion("account_number_last7 not in", values, "accountNumberLast7");
			return (Criteria) this;
		}

		public Criteria andAccountNumberLast7Between(String value1, String value2) {
			addCriterion("account_number_last7 between", value1, value2, "accountNumberLast7");
			return (Criteria) this;
		}

		public Criteria andAccountNumberLast7NotBetween(String value1, String value2) {
			addCriterion("account_number_last7 not between", value1, value2, "accountNumberLast7");
			return (Criteria) this;
		}

		public Criteria andEntrustAgeBracketIsNull() {
			addCriterion("entrust_age_bracket is null");
			return (Criteria) this;
		}

		public Criteria andEntrustAgeBracketIsNotNull() {
			addCriterion("entrust_age_bracket is not null");
			return (Criteria) this;
		}

		public Criteria andEntrustAgeBracketEqualTo(String value) {
			addCriterion("entrust_age_bracket =", value, "entrustAgeBracket");
			return (Criteria) this;
		}

		public Criteria andEntrustAgeBracketNotEqualTo(String value) {
			addCriterion("entrust_age_bracket <>", value, "entrustAgeBracket");
			return (Criteria) this;
		}

		public Criteria andEntrustAgeBracketGreaterThan(String value) {
			addCriterion("entrust_age_bracket >", value, "entrustAgeBracket");
			return (Criteria) this;
		}

		public Criteria andEntrustAgeBracketGreaterThanOrEqualTo(String value) {
			addCriterion("entrust_age_bracket >=", value, "entrustAgeBracket");
			return (Criteria) this;
		}

		public Criteria andEntrustAgeBracketLessThan(String value) {
			addCriterion("entrust_age_bracket <", value, "entrustAgeBracket");
			return (Criteria) this;
		}

		public Criteria andEntrustAgeBracketLessThanOrEqualTo(String value) {
			addCriterion("entrust_age_bracket <=", value, "entrustAgeBracket");
			return (Criteria) this;
		}

		public Criteria andEntrustAgeBracketLike(String value) {
			addCriterion("entrust_age_bracket like", value, "entrustAgeBracket");
			return (Criteria) this;
		}

		public Criteria andEntrustAgeBracketNotLike(String value) {
			addCriterion("entrust_age_bracket not like", value, "entrustAgeBracket");
			return (Criteria) this;
		}

		public Criteria andEntrustAgeBracketIn(List<String> values) {
			addCriterion("entrust_age_bracket in", values, "entrustAgeBracket");
			return (Criteria) this;
		}

		public Criteria andEntrustAgeBracketNotIn(List<String> values) {
			addCriterion("entrust_age_bracket not in", values, "entrustAgeBracket");
			return (Criteria) this;
		}

		public Criteria andEntrustAgeBracketBetween(String value1, String value2) {
			addCriterion("entrust_age_bracket between", value1, value2, "entrustAgeBracket");
			return (Criteria) this;
		}

		public Criteria andEntrustAgeBracketNotBetween(String value1, String value2) {
			addCriterion("entrust_age_bracket not between", value1, value2, "entrustAgeBracket");
			return (Criteria) this;
		}

		public Criteria andEntrustAmountBracketIsNull() {
			addCriterion("entrust_amount_bracket is null");
			return (Criteria) this;
		}

		public Criteria andEntrustAmountBracketIsNotNull() {
			addCriterion("entrust_amount_bracket is not null");
			return (Criteria) this;
		}

		public Criteria andEntrustAmountBracketEqualTo(String value) {
			addCriterion("entrust_amount_bracket =", value, "entrustAmountBracket");
			return (Criteria) this;
		}

		public Criteria andEntrustAmountBracketNotEqualTo(String value) {
			addCriterion("entrust_amount_bracket <>", value, "entrustAmountBracket");
			return (Criteria) this;
		}

		public Criteria andEntrustAmountBracketGreaterThan(String value) {
			addCriterion("entrust_amount_bracket >", value, "entrustAmountBracket");
			return (Criteria) this;
		}

		public Criteria andEntrustAmountBracketGreaterThanOrEqualTo(String value) {
			addCriterion("entrust_amount_bracket >=", value, "entrustAmountBracket");
			return (Criteria) this;
		}

		public Criteria andEntrustAmountBracketLessThan(String value) {
			addCriterion("entrust_amount_bracket <", value, "entrustAmountBracket");
			return (Criteria) this;
		}

		public Criteria andEntrustAmountBracketLessThanOrEqualTo(String value) {
			addCriterion("entrust_amount_bracket <=", value, "entrustAmountBracket");
			return (Criteria) this;
		}

		public Criteria andEntrustAmountBracketLike(String value) {
			addCriterion("entrust_amount_bracket like", value, "entrustAmountBracket");
			return (Criteria) this;
		}

		public Criteria andEntrustAmountBracketNotLike(String value) {
			addCriterion("entrust_amount_bracket not like", value, "entrustAmountBracket");
			return (Criteria) this;
		}

		public Criteria andEntrustAmountBracketIn(List<String> values) {
			addCriterion("entrust_amount_bracket in", values, "entrustAmountBracket");
			return (Criteria) this;
		}

		public Criteria andEntrustAmountBracketNotIn(List<String> values) {
			addCriterion("entrust_amount_bracket not in", values, "entrustAmountBracket");
			return (Criteria) this;
		}

		public Criteria andEntrustAmountBracketBetween(String value1, String value2) {
			addCriterion("entrust_amount_bracket between", value1, value2, "entrustAmountBracket");
			return (Criteria) this;
		}

		public Criteria andEntrustAmountBracketNotBetween(String value1, String value2) {
			addCriterion("entrust_amount_bracket not between", value1, value2, "entrustAmountBracket");
			return (Criteria) this;
		}

		public Criteria andEntrustCommissionBracketIsNull() {
			addCriterion("entrust_commission_bracket is null");
			return (Criteria) this;
		}

		public Criteria andEntrustCommissionBracketIsNotNull() {
			addCriterion("entrust_commission_bracket is not null");
			return (Criteria) this;
		}

		public Criteria andEntrustCommissionBracketEqualTo(String value) {
			addCriterion("entrust_commission_bracket =", value, "entrustCommissionBracket");
			return (Criteria) this;
		}

		public Criteria andEntrustCommissionBracketNotEqualTo(String value) {
			addCriterion("entrust_commission_bracket <>", value, "entrustCommissionBracket");
			return (Criteria) this;
		}

		public Criteria andEntrustCommissionBracketGreaterThan(String value) {
			addCriterion("entrust_commission_bracket >", value, "entrustCommissionBracket");
			return (Criteria) this;
		}

		public Criteria andEntrustCommissionBracketGreaterThanOrEqualTo(String value) {
			addCriterion("entrust_commission_bracket >=", value, "entrustCommissionBracket");
			return (Criteria) this;
		}

		public Criteria andEntrustCommissionBracketLessThan(String value) {
			addCriterion("entrust_commission_bracket <", value, "entrustCommissionBracket");
			return (Criteria) this;
		}

		public Criteria andEntrustCommissionBracketLessThanOrEqualTo(String value) {
			addCriterion("entrust_commission_bracket <=", value, "entrustCommissionBracket");
			return (Criteria) this;
		}

		public Criteria andEntrustCommissionBracketLike(String value) {
			addCriterion("entrust_commission_bracket like", value, "entrustCommissionBracket");
			return (Criteria) this;
		}

		public Criteria andEntrustCommissionBracketNotLike(String value) {
			addCriterion("entrust_commission_bracket not like", value, "entrustCommissionBracket");
			return (Criteria) this;
		}

		public Criteria andEntrustCommissionBracketIn(List<String> values) {
			addCriterion("entrust_commission_bracket in", values, "entrustCommissionBracket");
			return (Criteria) this;
		}

		public Criteria andEntrustCommissionBracketNotIn(List<String> values) {
			addCriterion("entrust_commission_bracket not in", values, "entrustCommissionBracket");
			return (Criteria) this;
		}

		public Criteria andEntrustCommissionBracketBetween(String value1, String value2) {
			addCriterion("entrust_commission_bracket between", value1, value2, "entrustCommissionBracket");
			return (Criteria) this;
		}

		public Criteria andEntrustCommissionBracketNotBetween(String value1, String value2) {
			addCriterion("entrust_commission_bracket not between", value1, value2, "entrustCommissionBracket");
			return (Criteria) this;
		}

		public Criteria andRatingBracketIsNull() {
			addCriterion("rating_bracket is null");
			return (Criteria) this;
		}

		public Criteria andRatingBracketIsNotNull() {
			addCriterion("rating_bracket is not null");
			return (Criteria) this;
		}

		public Criteria andRatingBracketEqualTo(String value) {
			addCriterion("rating_bracket =", value, "ratingBracket");
			return (Criteria) this;
		}

		public Criteria andRatingBracketNotEqualTo(String value) {
			addCriterion("rating_bracket <>", value, "ratingBracket");
			return (Criteria) this;
		}

		public Criteria andRatingBracketGreaterThan(String value) {
			addCriterion("rating_bracket >", value, "ratingBracket");
			return (Criteria) this;
		}

		public Criteria andRatingBracketGreaterThanOrEqualTo(String value) {
			addCriterion("rating_bracket >=", value, "ratingBracket");
			return (Criteria) this;
		}

		public Criteria andRatingBracketLessThan(String value) {
			addCriterion("rating_bracket <", value, "ratingBracket");
			return (Criteria) this;
		}

		public Criteria andRatingBracketLessThanOrEqualTo(String value) {
			addCriterion("rating_bracket <=", value, "ratingBracket");
			return (Criteria) this;
		}

		public Criteria andRatingBracketLike(String value) {
			addCriterion("rating_bracket like", value, "ratingBracket");
			return (Criteria) this;
		}

		public Criteria andRatingBracketNotLike(String value) {
			addCriterion("rating_bracket not like", value, "ratingBracket");
			return (Criteria) this;
		}

		public Criteria andRatingBracketIn(List<String> values) {
			addCriterion("rating_bracket in", values, "ratingBracket");
			return (Criteria) this;
		}

		public Criteria andRatingBracketNotIn(List<String> values) {
			addCriterion("rating_bracket not in", values, "ratingBracket");
			return (Criteria) this;
		}

		public Criteria andRatingBracketBetween(String value1, String value2) {
			addCriterion("rating_bracket between", value1, value2, "ratingBracket");
			return (Criteria) this;
		}

		public Criteria andRatingBracketNotBetween(String value1, String value2) {
			addCriterion("rating_bracket not between", value1, value2, "ratingBracket");
			return (Criteria) this;
		}

		public Criteria andIsLitigationIsNull() {
			addCriterion("is_litigation is null");
			return (Criteria) this;
		}

		public Criteria andIsLitigationIsNotNull() {
			addCriterion("is_litigation is not null");
			return (Criteria) this;
		}

		public Criteria andIsLitigationEqualTo(String value) {
			addCriterion("is_litigation =", value, "isLitigation");
			return (Criteria) this;
		}

		public Criteria andIsLitigationNotEqualTo(String value) {
			addCriterion("is_litigation <>", value, "isLitigation");
			return (Criteria) this;
		}

		public Criteria andIsLitigationGreaterThan(String value) {
			addCriterion("is_litigation >", value, "isLitigation");
			return (Criteria) this;
		}

		public Criteria andIsLitigationGreaterThanOrEqualTo(String value) {
			addCriterion("is_litigation >=", value, "isLitigation");
			return (Criteria) this;
		}

		public Criteria andIsLitigationLessThan(String value) {
			addCriterion("is_litigation <", value, "isLitigation");
			return (Criteria) this;
		}

		public Criteria andIsLitigationLessThanOrEqualTo(String value) {
			addCriterion("is_litigation <=", value, "isLitigation");
			return (Criteria) this;
		}

		public Criteria andIsLitigationLike(String value) {
			addCriterion("is_litigation like", value, "isLitigation");
			return (Criteria) this;
		}

		public Criteria andIsLitigationNotLike(String value) {
			addCriterion("is_litigation not like", value, "isLitigation");
			return (Criteria) this;
		}

		public Criteria andIsLitigationIn(List<String> values) {
			addCriterion("is_litigation in", values, "isLitigation");
			return (Criteria) this;
		}

		public Criteria andIsLitigationNotIn(List<String> values) {
			addCriterion("is_litigation not in", values, "isLitigation");
			return (Criteria) this;
		}

		public Criteria andIsLitigationBetween(String value1, String value2) {
			addCriterion("is_litigation between", value1, value2, "isLitigation");
			return (Criteria) this;
		}

		public Criteria andIsLitigationNotBetween(String value1, String value2) {
			addCriterion("is_litigation not between", value1, value2, "isLitigation");
			return (Criteria) this;
		}

		public Criteria andCustomerCurrentMonthRepaymentIsNull() {
			addCriterion("customer_current_month_repayment is null");
			return (Criteria) this;
		}

		public Criteria andCustomerCurrentMonthRepaymentIsNotNull() {
			addCriterion("customer_current_month_repayment is not null");
			return (Criteria) this;
		}

		public Criteria andCustomerCurrentMonthRepaymentEqualTo(BigDecimal value) {
			addCriterion("customer_current_month_repayment =", value, "customerCurrentMonthRepayment");
			return (Criteria) this;
		}

		public Criteria andCustomerCurrentMonthRepaymentNotEqualTo(BigDecimal value) {
			addCriterion("customer_current_month_repayment <>", value, "customerCurrentMonthRepayment");
			return (Criteria) this;
		}

		public Criteria andCustomerCurrentMonthRepaymentGreaterThan(BigDecimal value) {
			addCriterion("customer_current_month_repayment >", value, "customerCurrentMonthRepayment");
			return (Criteria) this;
		}

		public Criteria andCustomerCurrentMonthRepaymentGreaterThanOrEqualTo(BigDecimal value) {
			addCriterion("customer_current_month_repayment >=", value, "customerCurrentMonthRepayment");
			return (Criteria) this;
		}

		public Criteria andCustomerCurrentMonthRepaymentLessThan(BigDecimal value) {
			addCriterion("customer_current_month_repayment <", value, "customerCurrentMonthRepayment");
			return (Criteria) this;
		}

		public Criteria andCustomerCurrentMonthRepaymentLessThanOrEqualTo(BigDecimal value) {
			addCriterion("customer_current_month_repayment <=", value, "customerCurrentMonthRepayment");
			return (Criteria) this;
		}

		public Criteria andCustomerCurrentMonthRepaymentIn(List<BigDecimal> values) {
			addCriterion("customer_current_month_repayment in", values, "customerCurrentMonthRepayment");
			return (Criteria) this;
		}

		public Criteria andCustomerCurrentMonthRepaymentNotIn(List<BigDecimal> values) {
			addCriterion("customer_current_month_repayment not in", values, "customerCurrentMonthRepayment");
			return (Criteria) this;
		}

		public Criteria andCustomerCurrentMonthRepaymentBetween(BigDecimal value1, BigDecimal value2) {
			addCriterion("customer_current_month_repayment between", value1, value2, "customerCurrentMonthRepayment");
			return (Criteria) this;
		}

		public Criteria andCustomerCurrentMonthRepaymentNotBetween(BigDecimal value1, BigDecimal value2) {
			addCriterion("customer_current_month_repayment not between", value1, value2,
					"customerCurrentMonthRepayment");
			return (Criteria) this;
		}

		public Criteria andCustomerPreviousDayRepaymentIsNull() {
			addCriterion("customer_previous_day_repayment is null");
			return (Criteria) this;
		}

		public Criteria andCustomerPreviousDayRepaymentIsNotNull() {
			addCriterion("customer_previous_day_repayment is not null");
			return (Criteria) this;
		}

		public Criteria andCustomerPreviousDayRepaymentEqualTo(BigDecimal value) {
			addCriterion("customer_previous_day_repayment =", value, "customerPreviousDayRepayment");
			return (Criteria) this;
		}

		public Criteria andCustomerPreviousDayRepaymentNotEqualTo(BigDecimal value) {
			addCriterion("customer_previous_day_repayment <>", value, "customerPreviousDayRepayment");
			return (Criteria) this;
		}

		public Criteria andCustomerPreviousDayRepaymentGreaterThan(BigDecimal value) {
			addCriterion("customer_previous_day_repayment >", value, "customerPreviousDayRepayment");
			return (Criteria) this;
		}

		public Criteria andCustomerPreviousDayRepaymentGreaterThanOrEqualTo(BigDecimal value) {
			addCriterion("customer_previous_day_repayment >=", value, "customerPreviousDayRepayment");
			return (Criteria) this;
		}

		public Criteria andCustomerPreviousDayRepaymentLessThan(BigDecimal value) {
			addCriterion("customer_previous_day_repayment <", value, "customerPreviousDayRepayment");
			return (Criteria) this;
		}

		public Criteria andCustomerPreviousDayRepaymentLessThanOrEqualTo(BigDecimal value) {
			addCriterion("customer_previous_day_repayment <=", value, "customerPreviousDayRepayment");
			return (Criteria) this;
		}

		public Criteria andCustomerPreviousDayRepaymentIn(List<BigDecimal> values) {
			addCriterion("customer_previous_day_repayment in", values, "customerPreviousDayRepayment");
			return (Criteria) this;
		}

		public Criteria andCustomerPreviousDayRepaymentNotIn(List<BigDecimal> values) {
			addCriterion("customer_previous_day_repayment not in", values, "customerPreviousDayRepayment");
			return (Criteria) this;
		}

		public Criteria andCustomerPreviousDayRepaymentBetween(BigDecimal value1, BigDecimal value2) {
			addCriterion("customer_previous_day_repayment between", value1, value2, "customerPreviousDayRepayment");
			return (Criteria) this;
		}

		public Criteria andCustomerPreviousDayRepaymentNotBetween(BigDecimal value1, BigDecimal value2) {
			addCriterion("customer_previous_day_repayment not between", value1, value2, "customerPreviousDayRepayment");
			return (Criteria) this;
		}

		public Criteria andCaseGrabbingUserIsNull() {
			addCriterion("case_grabbing_user is null");
			return (Criteria) this;
		}

		public Criteria andCaseGrabbingUserIsNotNull() {
			addCriterion("case_grabbing_user is not null");
			return (Criteria) this;
		}

		public Criteria andCaseGrabbingUserEqualTo(String value) {
			addCriterion("case_grabbing_user =", value, "caseGrabbingUser");
			return (Criteria) this;
		}

		public Criteria andCaseGrabbingUserNotEqualTo(String value) {
			addCriterion("case_grabbing_user <>", value, "caseGrabbingUser");
			return (Criteria) this;
		}

		public Criteria andCaseGrabbingUserGreaterThan(String value) {
			addCriterion("case_grabbing_user >", value, "caseGrabbingUser");
			return (Criteria) this;
		}

		public Criteria andCaseGrabbingUserGreaterThanOrEqualTo(String value) {
			addCriterion("case_grabbing_user >=", value, "caseGrabbingUser");
			return (Criteria) this;
		}

		public Criteria andCaseGrabbingUserLessThan(String value) {
			addCriterion("case_grabbing_user <", value, "caseGrabbingUser");
			return (Criteria) this;
		}

		public Criteria andCaseGrabbingUserLessThanOrEqualTo(String value) {
			addCriterion("case_grabbing_user <=", value, "caseGrabbingUser");
			return (Criteria) this;
		}

		public Criteria andCaseGrabbingUserLike(String value) {
			addCriterion("case_grabbing_user like", value, "caseGrabbingUser");
			return (Criteria) this;
		}

		public Criteria andCaseGrabbingUserNotLike(String value) {
			addCriterion("case_grabbing_user not like", value, "caseGrabbingUser");
			return (Criteria) this;
		}

		public Criteria andCaseGrabbingUserIn(List<String> values) {
			addCriterion("case_grabbing_user in", values, "caseGrabbingUser");
			return (Criteria) this;
		}

		public Criteria andCaseGrabbingUserNotIn(List<String> values) {
			addCriterion("case_grabbing_user not in", values, "caseGrabbingUser");
			return (Criteria) this;
		}

		public Criteria andCaseGrabbingUserBetween(String value1, String value2) {
			addCriterion("case_grabbing_user between", value1, value2, "caseGrabbingUser");
			return (Criteria) this;
		}

		public Criteria andCaseGrabbingUserNotBetween(String value1, String value2) {
			addCriterion("case_grabbing_user not between", value1, value2, "caseGrabbingUser");
			return (Criteria) this;
		}

		public Criteria andSpecialProjectTypeIsNull() {
			addCriterion("special_project_type is null");
			return (Criteria) this;
		}

		public Criteria andSpecialProjectTypeIsNotNull() {
			addCriterion("special_project_type is not null");
			return (Criteria) this;
		}

		public Criteria andSpecialProjectTypeEqualTo(String value) {
			addCriterion("special_project_type =", value, "specialProjectType");
			return (Criteria) this;
		}

		public Criteria andSpecialProjectTypeNotEqualTo(String value) {
			addCriterion("special_project_type <>", value, "specialProjectType");
			return (Criteria) this;
		}

		public Criteria andSpecialProjectTypeGreaterThan(String value) {
			addCriterion("special_project_type >", value, "specialProjectType");
			return (Criteria) this;
		}

		public Criteria andSpecialProjectTypeGreaterThanOrEqualTo(String value) {
			addCriterion("special_project_type >=", value, "specialProjectType");
			return (Criteria) this;
		}

		public Criteria andSpecialProjectTypeLessThan(String value) {
			addCriterion("special_project_type <", value, "specialProjectType");
			return (Criteria) this;
		}

		public Criteria andSpecialProjectTypeLessThanOrEqualTo(String value) {
			addCriterion("special_project_type <=", value, "specialProjectType");
			return (Criteria) this;
		}

		public Criteria andSpecialProjectTypeLike(String value) {
			addCriterion("special_project_type like", value, "specialProjectType");
			return (Criteria) this;
		}

		public Criteria andSpecialProjectTypeNotLike(String value) {
			addCriterion("special_project_type not like", value, "specialProjectType");
			return (Criteria) this;
		}

		public Criteria andSpecialProjectTypeIn(List<String> values) {
			addCriterion("special_project_type in", values, "specialProjectType");
			return (Criteria) this;
		}

		public Criteria andSpecialProjectTypeNotIn(List<String> values) {
			addCriterion("special_project_type not in", values, "specialProjectType");
			return (Criteria) this;
		}

		public Criteria andSpecialProjectTypeBetween(String value1, String value2) {
			addCriterion("special_project_type between", value1, value2, "specialProjectType");
			return (Criteria) this;
		}

		public Criteria andSpecialProjectTypeNotBetween(String value1, String value2) {
			addCriterion("special_project_type not between", value1, value2, "specialProjectType");
			return (Criteria) this;
		}

		public Criteria andRetentionCountIsNull() {
			addCriterion("retention_count is null");
			return (Criteria) this;
		}

		public Criteria andRetentionCountIsNotNull() {
			addCriterion("retention_count is not null");
			return (Criteria) this;
		}

		public Criteria andRetentionCountEqualTo(Integer value) {
			addCriterion("retention_count =", value, "retentionCount");
			return (Criteria) this;
		}

		public Criteria andRetentionCountNotEqualTo(Integer value) {
			addCriterion("retention_count <>", value, "retentionCount");
			return (Criteria) this;
		}

		public Criteria andRetentionCountGreaterThan(Integer value) {
			addCriterion("retention_count >", value, "retentionCount");
			return (Criteria) this;
		}

		public Criteria andRetentionCountGreaterThanOrEqualTo(Integer value) {
			addCriterion("retention_count >=", value, "retentionCount");
			return (Criteria) this;
		}

		public Criteria andRetentionCountLessThan(Integer value) {
			addCriterion("retention_count <", value, "retentionCount");
			return (Criteria) this;
		}

		public Criteria andRetentionCountLessThanOrEqualTo(Integer value) {
			addCriterion("retention_count <=", value, "retentionCount");
			return (Criteria) this;
		}

		public Criteria andRetentionCountIn(List<Integer> values) {
			addCriterion("retention_count in", values, "retentionCount");
			return (Criteria) this;
		}

		public Criteria andRetentionCountNotIn(List<Integer> values) {
			addCriterion("retention_count not in", values, "retentionCount");
			return (Criteria) this;
		}

		public Criteria andRetentionCountBetween(Integer value1, Integer value2) {
			addCriterion("retention_count between", value1, value2, "retentionCount");
			return (Criteria) this;
		}

		public Criteria andRetentionCountNotBetween(Integer value1, Integer value2) {
			addCriterion("retention_count not between", value1, value2, "retentionCount");
			return (Criteria) this;
		}

		public Criteria andPersonalizedInstallmentStatusIsNull() {
			addCriterion("personalized_installment_status is null");
			return (Criteria) this;
		}

		public Criteria andPersonalizedInstallmentStatusIsNotNull() {
			addCriterion("personalized_installment_status is not null");
			return (Criteria) this;
		}

		public Criteria andPersonalizedInstallmentStatusEqualTo(String value) {
			addCriterion("personalized_installment_status =", value, "personalizedInstallmentStatus");
			return (Criteria) this;
		}

		public Criteria andPersonalizedInstallmentStatusNotEqualTo(String value) {
			addCriterion("personalized_installment_status <>", value, "personalizedInstallmentStatus");
			return (Criteria) this;
		}

		public Criteria andPersonalizedInstallmentStatusGreaterThan(String value) {
			addCriterion("personalized_installment_status >", value, "personalizedInstallmentStatus");
			return (Criteria) this;
		}

		public Criteria andPersonalizedInstallmentStatusGreaterThanOrEqualTo(String value) {
			addCriterion("personalized_installment_status >=", value, "personalizedInstallmentStatus");
			return (Criteria) this;
		}

		public Criteria andPersonalizedInstallmentStatusLessThan(String value) {
			addCriterion("personalized_installment_status <", value, "personalizedInstallmentStatus");
			return (Criteria) this;
		}

		public Criteria andPersonalizedInstallmentStatusLessThanOrEqualTo(String value) {
			addCriterion("personalized_installment_status <=", value, "personalizedInstallmentStatus");
			return (Criteria) this;
		}

		public Criteria andPersonalizedInstallmentStatusLike(String value) {
			addCriterion("personalized_installment_status like", value, "personalizedInstallmentStatus");
			return (Criteria) this;
		}

		public Criteria andPersonalizedInstallmentStatusNotLike(String value) {
			addCriterion("personalized_installment_status not like", value, "personalizedInstallmentStatus");
			return (Criteria) this;
		}

		public Criteria andPersonalizedInstallmentStatusIn(List<String> values) {
			addCriterion("personalized_installment_status in", values, "personalizedInstallmentStatus");
			return (Criteria) this;
		}

		public Criteria andPersonalizedInstallmentStatusNotIn(List<String> values) {
			addCriterion("personalized_installment_status not in", values, "personalizedInstallmentStatus");
			return (Criteria) this;
		}

		public Criteria andPersonalizedInstallmentStatusBetween(String value1, String value2) {
			addCriterion("personalized_installment_status between", value1, value2, "personalizedInstallmentStatus");
			return (Criteria) this;
		}

		public Criteria andPersonalizedInstallmentStatusNotBetween(String value1, String value2) {
			addCriterion("personalized_installment_status not between", value1, value2,
					"personalizedInstallmentStatus");
			return (Criteria) this;
		}

		public Criteria andPersonalizedInstallmentFulfillmentStatusIsNull() {
			addCriterion("personalized_installment_fulfillment_status is null");
			return (Criteria) this;
		}

		public Criteria andPersonalizedInstallmentFulfillmentStatusIsNotNull() {
			addCriterion("personalized_installment_fulfillment_status is not null");
			return (Criteria) this;
		}

		public Criteria andPersonalizedInstallmentFulfillmentStatusEqualTo(String value) {
			addCriterion("personalized_installment_fulfillment_status =", value,
					"personalizedInstallmentFulfillmentStatus");
			return (Criteria) this;
		}

		public Criteria andPersonalizedInstallmentFulfillmentStatusNotEqualTo(String value) {
			addCriterion("personalized_installment_fulfillment_status <>", value,
					"personalizedInstallmentFulfillmentStatus");
			return (Criteria) this;
		}

		public Criteria andPersonalizedInstallmentFulfillmentStatusGreaterThan(String value) {
			addCriterion("personalized_installment_fulfillment_status >", value,
					"personalizedInstallmentFulfillmentStatus");
			return (Criteria) this;
		}

		public Criteria andPersonalizedInstallmentFulfillmentStatusGreaterThanOrEqualTo(String value) {
			addCriterion("personalized_installment_fulfillment_status >=", value,
					"personalizedInstallmentFulfillmentStatus");
			return (Criteria) this;
		}

		public Criteria andPersonalizedInstallmentFulfillmentStatusLessThan(String value) {
			addCriterion("personalized_installment_fulfillment_status <", value,
					"personalizedInstallmentFulfillmentStatus");
			return (Criteria) this;
		}

		public Criteria andPersonalizedInstallmentFulfillmentStatusLessThanOrEqualTo(String value) {
			addCriterion("personalized_installment_fulfillment_status <=", value,
					"personalizedInstallmentFulfillmentStatus");
			return (Criteria) this;
		}

		public Criteria andPersonalizedInstallmentFulfillmentStatusLike(String value) {
			addCriterion("personalized_installment_fulfillment_status like", value,
					"personalizedInstallmentFulfillmentStatus");
			return (Criteria) this;
		}

		public Criteria andPersonalizedInstallmentFulfillmentStatusNotLike(String value) {
			addCriterion("personalized_installment_fulfillment_status not like", value,
					"personalizedInstallmentFulfillmentStatus");
			return (Criteria) this;
		}

		public Criteria andPersonalizedInstallmentFulfillmentStatusIn(List<String> values) {
			addCriterion("personalized_installment_fulfillment_status in", values,
					"personalizedInstallmentFulfillmentStatus");
			return (Criteria) this;
		}

		public Criteria andPersonalizedInstallmentFulfillmentStatusNotIn(List<String> values) {
			addCriterion("personalized_installment_fulfillment_status not in", values,
					"personalizedInstallmentFulfillmentStatus");
			return (Criteria) this;
		}

		public Criteria andPersonalizedInstallmentFulfillmentStatusBetween(String value1, String value2) {
			addCriterion("personalized_installment_fulfillment_status between", value1, value2,
					"personalizedInstallmentFulfillmentStatus");
			return (Criteria) this;
		}

		public Criteria andPersonalizedInstallmentFulfillmentStatusNotBetween(String value1, String value2) {
			addCriterion("personalized_installment_fulfillment_status not between", value1, value2,
					"personalizedInstallmentFulfillmentStatus");
			return (Criteria) this;
		}

		public Criteria andComplaintLabelIsNull() {
			addCriterion("complaint_label is null");
			return (Criteria) this;
		}

		public Criteria andComplaintLabelIsNotNull() {
			addCriterion("complaint_label is not null");
			return (Criteria) this;
		}

		public Criteria andComplaintLabelEqualTo(String value) {
			addCriterion("complaint_label =", value, "complaintLabel");
			return (Criteria) this;
		}

		public Criteria andComplaintLabelNotEqualTo(String value) {
			addCriterion("complaint_label <>", value, "complaintLabel");
			return (Criteria) this;
		}

		public Criteria andComplaintLabelGreaterThan(String value) {
			addCriterion("complaint_label >", value, "complaintLabel");
			return (Criteria) this;
		}

		public Criteria andComplaintLabelGreaterThanOrEqualTo(String value) {
			addCriterion("complaint_label >=", value, "complaintLabel");
			return (Criteria) this;
		}

		public Criteria andComplaintLabelLessThan(String value) {
			addCriterion("complaint_label <", value, "complaintLabel");
			return (Criteria) this;
		}

		public Criteria andComplaintLabelLessThanOrEqualTo(String value) {
			addCriterion("complaint_label <=", value, "complaintLabel");
			return (Criteria) this;
		}

		public Criteria andComplaintLabelLike(String value) {
			addCriterion("complaint_label like", value, "complaintLabel");
			return (Criteria) this;
		}

		public Criteria andComplaintLabelNotLike(String value) {
			addCriterion("complaint_label not like", value, "complaintLabel");
			return (Criteria) this;
		}

		public Criteria andComplaintLabelIn(List<String> values) {
			addCriterion("complaint_label in", values, "complaintLabel");
			return (Criteria) this;
		}

		public Criteria andComplaintLabelNotIn(List<String> values) {
			addCriterion("complaint_label not in", values, "complaintLabel");
			return (Criteria) this;
		}

		public Criteria andComplaintLabelBetween(String value1, String value2) {
			addCriterion("complaint_label between", value1, value2, "complaintLabel");
			return (Criteria) this;
		}

		public Criteria andComplaintLabelNotBetween(String value1, String value2) {
			addCriterion("complaint_label not between", value1, value2, "complaintLabel");
			return (Criteria) this;
		}

		public Criteria andPersonalizedFulfillmentLabelIsNull() {
			addCriterion("personalized_fulfillment_label is null");
			return (Criteria) this;
		}

		public Criteria andPersonalizedFulfillmentLabelIsNotNull() {
			addCriterion("personalized_fulfillment_label is not null");
			return (Criteria) this;
		}

		public Criteria andPersonalizedFulfillmentLabelEqualTo(String value) {
			addCriterion("personalized_fulfillment_label =", value, "personalizedFulfillmentLabel");
			return (Criteria) this;
		}

		public Criteria andPersonalizedFulfillmentLabelNotEqualTo(String value) {
			addCriterion("personalized_fulfillment_label <>", value, "personalizedFulfillmentLabel");
			return (Criteria) this;
		}

		public Criteria andPersonalizedFulfillmentLabelGreaterThan(String value) {
			addCriterion("personalized_fulfillment_label >", value, "personalizedFulfillmentLabel");
			return (Criteria) this;
		}

		public Criteria andPersonalizedFulfillmentLabelGreaterThanOrEqualTo(String value) {
			addCriterion("personalized_fulfillment_label >=", value, "personalizedFulfillmentLabel");
			return (Criteria) this;
		}

		public Criteria andPersonalizedFulfillmentLabelLessThan(String value) {
			addCriterion("personalized_fulfillment_label <", value, "personalizedFulfillmentLabel");
			return (Criteria) this;
		}

		public Criteria andPersonalizedFulfillmentLabelLessThanOrEqualTo(String value) {
			addCriterion("personalized_fulfillment_label <=", value, "personalizedFulfillmentLabel");
			return (Criteria) this;
		}

		public Criteria andPersonalizedFulfillmentLabelLike(String value) {
			addCriterion("personalized_fulfillment_label like", value, "personalizedFulfillmentLabel");
			return (Criteria) this;
		}

		public Criteria andPersonalizedFulfillmentLabelNotLike(String value) {
			addCriterion("personalized_fulfillment_label not like", value, "personalizedFulfillmentLabel");
			return (Criteria) this;
		}

		public Criteria andPersonalizedFulfillmentLabelIn(List<String> values) {
			addCriterion("personalized_fulfillment_label in", values, "personalizedFulfillmentLabel");
			return (Criteria) this;
		}

		public Criteria andPersonalizedFulfillmentLabelNotIn(List<String> values) {
			addCriterion("personalized_fulfillment_label not in", values, "personalizedFulfillmentLabel");
			return (Criteria) this;
		}

		public Criteria andPersonalizedFulfillmentLabelBetween(String value1, String value2) {
			addCriterion("personalized_fulfillment_label between", value1, value2, "personalizedFulfillmentLabel");
			return (Criteria) this;
		}

		public Criteria andPersonalizedFulfillmentLabelNotBetween(String value1, String value2) {
			addCriterion("personalized_fulfillment_label not between", value1, value2, "personalizedFulfillmentLabel");
			return (Criteria) this;
		}

		public Criteria andLitigationLabelIsNull() {
			addCriterion("litigation_label is null");
			return (Criteria) this;
		}

		public Criteria andLitigationLabelIsNotNull() {
			addCriterion("litigation_label is not null");
			return (Criteria) this;
		}

		public Criteria andLitigationLabelEqualTo(String value) {
			addCriterion("litigation_label =", value, "litigationLabel");
			return (Criteria) this;
		}

		public Criteria andLitigationLabelNotEqualTo(String value) {
			addCriterion("litigation_label <>", value, "litigationLabel");
			return (Criteria) this;
		}

		public Criteria andLitigationLabelGreaterThan(String value) {
			addCriterion("litigation_label >", value, "litigationLabel");
			return (Criteria) this;
		}

		public Criteria andLitigationLabelGreaterThanOrEqualTo(String value) {
			addCriterion("litigation_label >=", value, "litigationLabel");
			return (Criteria) this;
		}

		public Criteria andLitigationLabelLessThan(String value) {
			addCriterion("litigation_label <", value, "litigationLabel");
			return (Criteria) this;
		}

		public Criteria andLitigationLabelLessThanOrEqualTo(String value) {
			addCriterion("litigation_label <=", value, "litigationLabel");
			return (Criteria) this;
		}

		public Criteria andLitigationLabelLike(String value) {
			addCriterion("litigation_label like", value, "litigationLabel");
			return (Criteria) this;
		}

		public Criteria andLitigationLabelNotLike(String value) {
			addCriterion("litigation_label not like", value, "litigationLabel");
			return (Criteria) this;
		}

		public Criteria andLitigationLabelIn(List<String> values) {
			addCriterion("litigation_label in", values, "litigationLabel");
			return (Criteria) this;
		}

		public Criteria andLitigationLabelNotIn(List<String> values) {
			addCriterion("litigation_label not in", values, "litigationLabel");
			return (Criteria) this;
		}

		public Criteria andLitigationLabelBetween(String value1, String value2) {
			addCriterion("litigation_label between", value1, value2, "litigationLabel");
			return (Criteria) this;
		}

		public Criteria andLitigationLabelNotBetween(String value1, String value2) {
			addCriterion("litigation_label not between", value1, value2, "litigationLabel");
			return (Criteria) this;
		}

		public Criteria andSmartVoiceLabelIsNull() {
			addCriterion("smart_voice_label is null");
			return (Criteria) this;
		}

		public Criteria andSmartVoiceLabelIsNotNull() {
			addCriterion("smart_voice_label is not null");
			return (Criteria) this;
		}

		public Criteria andSmartVoiceLabelEqualTo(String value) {
			addCriterion("smart_voice_label =", value, "smartVoiceLabel");
			return (Criteria) this;
		}

		public Criteria andSmartVoiceLabelNotEqualTo(String value) {
			addCriterion("smart_voice_label <>", value, "smartVoiceLabel");
			return (Criteria) this;
		}

		public Criteria andSmartVoiceLabelGreaterThan(String value) {
			addCriterion("smart_voice_label >", value, "smartVoiceLabel");
			return (Criteria) this;
		}

		public Criteria andSmartVoiceLabelGreaterThanOrEqualTo(String value) {
			addCriterion("smart_voice_label >=", value, "smartVoiceLabel");
			return (Criteria) this;
		}

		public Criteria andSmartVoiceLabelLessThan(String value) {
			addCriterion("smart_voice_label <", value, "smartVoiceLabel");
			return (Criteria) this;
		}

		public Criteria andSmartVoiceLabelLessThanOrEqualTo(String value) {
			addCriterion("smart_voice_label <=", value, "smartVoiceLabel");
			return (Criteria) this;
		}

		public Criteria andSmartVoiceLabelLike(String value) {
			addCriterion("smart_voice_label like", value, "smartVoiceLabel");
			return (Criteria) this;
		}

		public Criteria andSmartVoiceLabelNotLike(String value) {
			addCriterion("smart_voice_label not like", value, "smartVoiceLabel");
			return (Criteria) this;
		}

		public Criteria andSmartVoiceLabelIn(List<String> values) {
			addCriterion("smart_voice_label in", values, "smartVoiceLabel");
			return (Criteria) this;
		}

		public Criteria andSmartVoiceLabelNotIn(List<String> values) {
			addCriterion("smart_voice_label not in", values, "smartVoiceLabel");
			return (Criteria) this;
		}

		public Criteria andSmartVoiceLabelBetween(String value1, String value2) {
			addCriterion("smart_voice_label between", value1, value2, "smartVoiceLabel");
			return (Criteria) this;
		}

		public Criteria andSmartVoiceLabelNotBetween(String value1, String value2) {
			addCriterion("smart_voice_label not between", value1, value2, "smartVoiceLabel");
			return (Criteria) this;
		}

		public Criteria andSpecialProjectLabelIsNull() {
			addCriterion("special_project_label is null");
			return (Criteria) this;
		}

		public Criteria andSpecialProjectLabelIsNotNull() {
			addCriterion("special_project_label is not null");
			return (Criteria) this;
		}

		public Criteria andSpecialProjectLabelEqualTo(String value) {
			addCriterion("special_project_label =", value, "specialProjectLabel");
			return (Criteria) this;
		}

		public Criteria andSpecialProjectLabelNotEqualTo(String value) {
			addCriterion("special_project_label <>", value, "specialProjectLabel");
			return (Criteria) this;
		}

		public Criteria andSpecialProjectLabelGreaterThan(String value) {
			addCriterion("special_project_label >", value, "specialProjectLabel");
			return (Criteria) this;
		}

		public Criteria andSpecialProjectLabelGreaterThanOrEqualTo(String value) {
			addCriterion("special_project_label >=", value, "specialProjectLabel");
			return (Criteria) this;
		}

		public Criteria andSpecialProjectLabelLessThan(String value) {
			addCriterion("special_project_label <", value, "specialProjectLabel");
			return (Criteria) this;
		}

		public Criteria andSpecialProjectLabelLessThanOrEqualTo(String value) {
			addCriterion("special_project_label <=", value, "specialProjectLabel");
			return (Criteria) this;
		}

		public Criteria andSpecialProjectLabelLike(String value) {
			addCriterion("special_project_label like", value, "specialProjectLabel");
			return (Criteria) this;
		}

		public Criteria andSpecialProjectLabelNotLike(String value) {
			addCriterion("special_project_label not like", value, "specialProjectLabel");
			return (Criteria) this;
		}

		public Criteria andSpecialProjectLabelIn(List<String> values) {
			addCriterion("special_project_label in", values, "specialProjectLabel");
			return (Criteria) this;
		}

		public Criteria andSpecialProjectLabelNotIn(List<String> values) {
			addCriterion("special_project_label not in", values, "specialProjectLabel");
			return (Criteria) this;
		}

		public Criteria andSpecialProjectLabelBetween(String value1, String value2) {
			addCriterion("special_project_label between", value1, value2, "specialProjectLabel");
			return (Criteria) this;
		}

		public Criteria andSpecialProjectLabelNotBetween(String value1, String value2) {
			addCriterion("special_project_label not between", value1, value2, "specialProjectLabel");
			return (Criteria) this;
		}

		public Criteria andIsLitigationForCommissionIsNull() {
			addCriterion("is_litigation_for_commission is null");
			return (Criteria) this;
		}

		public Criteria andIsLitigationForCommissionIsNotNull() {
			addCriterion("is_litigation_for_commission is not null");
			return (Criteria) this;
		}

		public Criteria andIsLitigationForCommissionEqualTo(String value) {
			addCriterion("is_litigation_for_commission =", value, "isLitigationForCommission");
			return (Criteria) this;
		}

		public Criteria andIsLitigationForCommissionNotEqualTo(String value) {
			addCriterion("is_litigation_for_commission <>", value, "isLitigationForCommission");
			return (Criteria) this;
		}

		public Criteria andIsLitigationForCommissionGreaterThan(String value) {
			addCriterion("is_litigation_for_commission >", value, "isLitigationForCommission");
			return (Criteria) this;
		}

		public Criteria andIsLitigationForCommissionGreaterThanOrEqualTo(String value) {
			addCriterion("is_litigation_for_commission >=", value, "isLitigationForCommission");
			return (Criteria) this;
		}

		public Criteria andIsLitigationForCommissionLessThan(String value) {
			addCriterion("is_litigation_for_commission <", value, "isLitigationForCommission");
			return (Criteria) this;
		}

		public Criteria andIsLitigationForCommissionLessThanOrEqualTo(String value) {
			addCriterion("is_litigation_for_commission <=", value, "isLitigationForCommission");
			return (Criteria) this;
		}

		public Criteria andIsLitigationForCommissionLike(String value) {
			addCriterion("is_litigation_for_commission like", value, "isLitigationForCommission");
			return (Criteria) this;
		}

		public Criteria andIsLitigationForCommissionNotLike(String value) {
			addCriterion("is_litigation_for_commission not like", value, "isLitigationForCommission");
			return (Criteria) this;
		}

		public Criteria andIsLitigationForCommissionIn(List<String> values) {
			addCriterion("is_litigation_for_commission in", values, "isLitigationForCommission");
			return (Criteria) this;
		}

		public Criteria andIsLitigationForCommissionNotIn(List<String> values) {
			addCriterion("is_litigation_for_commission not in", values, "isLitigationForCommission");
			return (Criteria) this;
		}

		public Criteria andIsLitigationForCommissionBetween(String value1, String value2) {
			addCriterion("is_litigation_for_commission between", value1, value2, "isLitigationForCommission");
			return (Criteria) this;
		}

		public Criteria andIsLitigationForCommissionNotBetween(String value1, String value2) {
			addCriterion("is_litigation_for_commission not between", value1, value2, "isLitigationForCommission");
			return (Criteria) this;
		}

		public Criteria andCaseAgencyâ€œIsNull() {
			addCriterion("case_agencyâ€Œ is null");
			return (Criteria) this;
		}

		public Criteria andCaseAgencyâ€œIsNotNull() {
			addCriterion("case_agencyâ€Œ is not null");
			return (Criteria) this;
		}

		public Criteria andCaseAgencyâ€œEqualTo(String value) {
			addCriterion("case_agencyâ€Œ =", value, "caseAgencyâ€œ");
			return (Criteria) this;
		}

		public Criteria andCaseAgencyâ€œNotEqualTo(String value) {
			addCriterion("case_agencyâ€Œ <>", value, "caseAgencyâ€œ");
			return (Criteria) this;
		}

		public Criteria andCaseAgencyâ€œGreaterThan(String value) {
			addCriterion("case_agencyâ€Œ >", value, "caseAgencyâ€œ");
			return (Criteria) this;
		}

		public Criteria andCaseAgencyâ€œGreaterThanOrEqualTo(String value) {
			addCriterion("case_agencyâ€Œ >=", value, "caseAgencyâ€œ");
			return (Criteria) this;
		}

		public Criteria andCaseAgencyâ€œLessThan(String value) {
			addCriterion("case_agencyâ€Œ <", value, "caseAgencyâ€œ");
			return (Criteria) this;
		}

		public Criteria andCaseAgencyâ€œLessThanOrEqualTo(String value) {
			addCriterion("case_agencyâ€Œ <=", value, "caseAgencyâ€œ");
			return (Criteria) this;
		}

		public Criteria andCaseAgencyâ€œLike(String value) {
			addCriterion("case_agencyâ€Œ like", value, "caseAgencyâ€œ");
			return (Criteria) this;
		}

		public Criteria andCaseAgencyâ€œNotLike(String value) {
			addCriterion("case_agencyâ€Œ not like", value, "caseAgencyâ€œ");
			return (Criteria) this;
		}

		public Criteria andCaseAgencyâ€œIn(List<String> values) {
			addCriterion("case_agencyâ€Œ in", values, "caseAgencyâ€œ");
			return (Criteria) this;
		}

		public Criteria andCaseAgencyâ€œNotIn(List<String> values) {
			addCriterion("case_agencyâ€Œ not in", values, "caseAgencyâ€œ");
			return (Criteria) this;
		}

		public Criteria andCaseAgencyâ€œBetween(String value1, String value2) {
			addCriterion("case_agencyâ€Œ between", value1, value2, "caseAgencyâ€œ");
			return (Criteria) this;
		}

		public Criteria andCaseAgencyâ€œNotBetween(String value1, String value2) {
			addCriterion("case_agencyâ€Œ not between", value1, value2, "caseAgencyâ€œ");
			return (Criteria) this;
		}

		public Criteria andFollowUpStatusIsNull() {
			addCriterion("follow_up_status is null");
			return (Criteria) this;
		}

		public Criteria andFollowUpStatusIsNotNull() {
			addCriterion("follow_up_status is not null");
			return (Criteria) this;
		}

		public Criteria andFollowUpStatusEqualTo(String value) {
			addCriterion("follow_up_status =", value, "followUpStatus");
			return (Criteria) this;
		}

		public Criteria andFollowUpStatusNotEqualTo(String value) {
			addCriterion("follow_up_status <>", value, "followUpStatus");
			return (Criteria) this;
		}

		public Criteria andFollowUpStatusGreaterThan(String value) {
			addCriterion("follow_up_status >", value, "followUpStatus");
			return (Criteria) this;
		}

		public Criteria andFollowUpStatusGreaterThanOrEqualTo(String value) {
			addCriterion("follow_up_status >=", value, "followUpStatus");
			return (Criteria) this;
		}

		public Criteria andFollowUpStatusLessThan(String value) {
			addCriterion("follow_up_status <", value, "followUpStatus");
			return (Criteria) this;
		}

		public Criteria andFollowUpStatusLessThanOrEqualTo(String value) {
			addCriterion("follow_up_status <=", value, "followUpStatus");
			return (Criteria) this;
		}

		public Criteria andFollowUpStatusLike(String value) {
			addCriterion("follow_up_status like", value, "followUpStatus");
			return (Criteria) this;
		}

		public Criteria andFollowUpStatusNotLike(String value) {
			addCriterion("follow_up_status not like", value, "followUpStatus");
			return (Criteria) this;
		}

		public Criteria andFollowUpStatusIn(List<String> values) {
			addCriterion("follow_up_status in", values, "followUpStatus");
			return (Criteria) this;
		}

		public Criteria andFollowUpStatusNotIn(List<String> values) {
			addCriterion("follow_up_status not in", values, "followUpStatus");
			return (Criteria) this;
		}

		public Criteria andFollowUpStatusBetween(String value1, String value2) {
			addCriterion("follow_up_status between", value1, value2, "followUpStatus");
			return (Criteria) this;
		}

		public Criteria andFollowUpStatusNotBetween(String value1, String value2) {
			addCriterion("follow_up_status not between", value1, value2, "followUpStatus");
			return (Criteria) this;
		}
	}

	/**
	 * This class was generated by MyBatis Generator. This class corresponds to the database table cc_user
	 * @mbg.generated  Wed Nov 20 11:23:04 HKT 2024
	 */
	public static class Criterion {
		private String condition;
		private Object value;
		private Object secondValue;
		private boolean noValue;
		private boolean singleValue;
		private boolean betweenValue;
		private boolean listValue;
		private String typeHandler;

		public String getCondition() {
			return condition;
		}

		public Object getValue() {
			return value;
		}

		public Object getSecondValue() {
			return secondValue;
		}

		public boolean isNoValue() {
			return noValue;
		}

		public boolean isSingleValue() {
			return singleValue;
		}

		public boolean isBetweenValue() {
			return betweenValue;
		}

		public boolean isListValue() {
			return listValue;
		}

		public String getTypeHandler() {
			return typeHandler;
		}

		protected Criterion(String condition) {
			super();
			this.condition = condition;
			this.typeHandler = null;
			this.noValue = true;
		}

		protected Criterion(String condition, Object value, String typeHandler) {
			super();
			this.condition = condition;
			this.value = value;
			this.typeHandler = typeHandler;
			if (value instanceof List<?>) {
				this.listValue = true;
			} else {
				this.singleValue = true;
			}
		}

		protected Criterion(String condition, Object value) {
			this(condition, value, null);
		}

		protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
			super();
			this.condition = condition;
			this.value = value;
			this.secondValue = secondValue;
			this.typeHandler = typeHandler;
			this.betweenValue = true;
		}

		protected Criterion(String condition, Object value, Object secondValue) {
			this(condition, value, secondValue, null);
		}
	}

	/**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table cc_user
     *
     * @mbg.generated do_not_delete_during_merge Tue Nov 19 16:53:11 HKT 2024
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }
}