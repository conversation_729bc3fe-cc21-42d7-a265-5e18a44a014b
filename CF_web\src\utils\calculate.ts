import BigNumber from "bignumber.js";

/**处理计算进度问题*/
//加
const $add = function (a: number, b: number) {
  return new BigNumber(a).plus(new BigNumber(b)).toNumber();
};

// 减
const $subtract = function (a: number, b: number) {
  return new BigNumber(a).minus(new BigNumber(b)).toNumber();
};

// 乘
const $multiply = function (a: number, b: number) {
  return new BigNumber(a).multipliedBy(new BigNumber(b)).toNumber();
};

// 除
const $divide = function (a: number, b: number) {
  return new BigNumber(a).dividedBy(new BigNumber(b)).toNumber();
};

export { $add, $subtract, $multiply, $divide };
