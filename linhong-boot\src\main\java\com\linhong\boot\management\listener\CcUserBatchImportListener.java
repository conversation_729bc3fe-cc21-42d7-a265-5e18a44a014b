package com.linhong.boot.management.listener;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

import com.alibaba.excel.context.AnalysisContext;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.esotericsoftware.minlog.Log;
import com.linhong.boot.common.base.BaseAnalysisEventListener;
import com.linhong.boot.common.enums.CaseEnum;
import com.linhong.boot.common.enums.QueryResultEnum;
import com.linhong.boot.common.util.ReflectiveCopierUtils;
import com.linhong.boot.management.model.dto.CcUserDTO;
import com.linhong.boot.management.model.entity.CcUser;
import com.linhong.boot.management.service.CasePoolService;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;


@Slf4j
public class CcUserBatchImportListener extends BaseAnalysisEventListener<CcUserDTO> {


    // 有效条数
    private int validCount;

    // 无效条数
    private int invalidCount;
    
    /*外包序号*/
    private String outsourceSerialNumber;

    private List<CcUser> cList = new ArrayList<>();;
    
    private static final int BATCH_SIZE = 1000;
    private final ExecutorService executorService;
    private Set<String> existingCids;
    List<String> IndexList = new ArrayList<>();
    ConcurrentLinkedQueue<CcUser> dataQueue = new ConcurrentLinkedQueue<>();
    
    public String getOutsourceSerialNumber() {
		return outsourceSerialNumber;
	}

	public void setOutsourceSerialNumber(String outsourceSerialNumber) {
		this.outsourceSerialNumber = outsourceSerialNumber;
//		cList = new ArrayList<>();
	}


	// 导入返回信息
    StringBuilder msg = new StringBuilder();

    private final CasePoolService casePoolService;

    public CcUserBatchImportListener() {
		this.executorService = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors());
		this.casePoolService=SpringUtil.getBean(CasePoolService.class);
    }

    @Override
    public void invoke(CcUserDTO ccUserDTO, AnalysisContext analysisContext){
        log.info("解析到一条数据:{}", JSONUtil.toJsonStr(ccUserDTO));
        try {
        	CcUser ccUser = new CcUser(); 
			ReflectiveCopierUtils.copy(ccUserDTO, ccUser);
			ccUser.setFollowUpStatus(CaseEnum.CASE_POOL.getValue().toString()); //初始化案件状态: 案池
			ccUser.setOutsourceSerialNumber(outsourceSerialNumber);//外包序号
			ccUser.setLostContactQueryResult(QueryResultEnum.WEI_CHU.getValue().toString()); //初始化 失联查询状态未出
			ccUser.setIsMarkChange("-1");// 是否开启换单 默认:否
			IndexList.add(ccUser.getCustomerIndexNumber());
			dataQueue.add(ccUser);
			validCount++;
		} catch (IllegalAccessException e) {
			e.printStackTrace();
			Log.error("导入异常:"+e.getMessage());
		}
    }

    /**
     * 所有数据解析完成会来调用
     */
    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
    	
    	LambdaQueryWrapper<CcUser> cq = new LambdaQueryWrapper<>();
    	cq.in(CcUser::getCustomerIndexNumber, IndexList);
    	List<CcUser> oldUser = casePoolService.getBaseMapper().selectList(cq);
    	existingCids = oldUser.stream().map(CcUser::getCustomerIndexNumber).collect(Collectors.toSet());
    	processData(dataQueue);
        log.info("所有数据解析完成！"+dataQueue.size());
    }

    private void processData(ConcurrentLinkedQueue<CcUser> dataQueue) {
        List<Future<?>> futures = new ArrayList<>();
        List<CcUser> updateList = new ArrayList<>(BATCH_SIZE);
        List<CcUser> insertList = new ArrayList<>(BATCH_SIZE);

        while (!dataQueue.isEmpty()) {
        	CcUser data = dataQueue.poll();
            if (existingCids.contains(data.getCustomerIndexNumber())) {
                updateList.add(data);
            } else {
                insertList.add(data);
            }

            // 当达到批次大小时，提交任务到线程池
            if (updateList.size() >= BATCH_SIZE || insertList.size() >= BATCH_SIZE) {
                if (!updateList.isEmpty()) {
                    futures.add(executorService.submit(() -> casePoolService.updateBatchByIdNumber(updateList)));
                    updateList.clear();
                }
                if (!insertList.isEmpty()) {
                    futures.add(executorService.submit(() -> casePoolService.saveBatch(insertList)));
                    insertList.clear();
                }
            }
        }
        // 处理剩余的数据
        if (!updateList.isEmpty()) {
            futures.add(executorService.submit(() -> casePoolService.updateBatchByIdNumber(updateList)));
        }
        if (!insertList.isEmpty()) {
            futures.add(executorService.submit(() -> casePoolService.saveBatch(insertList)));
        }

        // 等待所有任务完成
        for (Future<?> future : futures) {
            try {
                future.get();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        // 关闭线程池
        executorService.shutdown();
    }

    @Override
    public String getMsg() {
        // 总结信息
        return StrUtil.format("导入用户结束：成功{}条，失败{}条；{}", validCount, invalidCount, msg);
    }
}
