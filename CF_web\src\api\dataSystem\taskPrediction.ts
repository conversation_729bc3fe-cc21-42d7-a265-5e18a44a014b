import request from "@/utils/request";

const statementAPI = {
  /**
   * 任务预测列表
   * **/
  taskRepayTaskPage(queryParams?: any) {
    return request<any>({
      url: "/api/v1/taskRepay/taskPage",
      method: "get",
      params: queryParams,
    });
  },
  // 删除任务预测
  taskRepayDelete(queryParams?: any) {
    return request<any>({
      url: "/api/v1/taskRepay/delete",
      method: "post",
      data: queryParams,
      loading: true,
    });
  },
};
export default statementAPI;
