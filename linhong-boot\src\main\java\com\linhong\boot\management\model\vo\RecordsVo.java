package com.linhong.boot.management.model.vo;

import java.math.BigDecimal;
import java.util.Date;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 跟进记录表
 */
@Schema(description ="跟进记录表")
public class RecordsVo {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column follow_up_records.id
     *
     * @mbg.generated Tue Nov 26 07:12:46 HKT 2024
     */
	@Schema(description ="跟进记录表ID")
    private Long id;

    /**
     * 对应cc_user.id <br/>
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column follow_up_records.user_id
     *
     * @mbg.generated Tue Nov 26 07:12:46 HKT 2024
     */
	@Schema(description ="案件ID")
    private Long userId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column follow_up_records.called_number
     *
     * @mbg.generated Tue Nov 26 07:12:46 HKT 2024
     */
	@Schema(description ="被叫号码")
    private String calledNumber;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column follow_up_records.caller_number
     *
     * @mbg.generated Tue Nov 26 07:12:46 HKT 2024
     */
	@Schema(description ="主叫号码")
    private String callerNumber;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column follow_up_records.collection_result
     *
     * @mbg.generated Tue Nov 26 07:12:46 HKT 2024
     */
	@Schema(description ="催收结果(传中文):案池,新案, 继续跟进 ,重点跟进 , 谈方案 , "
			+ "线下分期 , 个性化分期 , 待减免, 要求退案 , 保留案件 , 投诉倾向")
    private String collectionResult;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column follow_up_records.missing_contact_result
     *issued','not_issued','not_checked','other'
     * @mbg.generated Tue Nov 26 07:12:46 HKT 2024
     */
	@Schema(description ="失联查询结果(传中文):已出、未出、未查、其他")
    private String missingContactResult;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column follow_up_records.repayment_amount
     *
     * @mbg.generated Tue Nov 26 07:12:46 HKT 2024
     */
	@Schema(description ="回款金额，保留两位小数")
    private BigDecimal repaymentAmount;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column follow_up_records.repayment_date
     *
     * @mbg.generated Tue Nov 26 07:12:46 HKT 2024
     */
	@Schema(description = "回款日期 date类型")
    private Date repaymentDate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column follow_up_records.is_marked_for_change
     *
     * @mbg.generated Tue Nov 26 07:12:46 HKT 2024
     */
	@Schema(description = "标记是否换单,0:否  1:是 , 默认0")
    private Boolean isMarkedForChange;

	@Schema(description = "任务预测")
	private String taskPrediction;
	
	@Schema(description = "催记内容")
	private String collectionNotes;
	
	@Schema(description = "操作员ID")
	private Long operationId;
	
	@Schema(description = "操作员名字")
	private String operationName;
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column follow_up_records.created_at
     *
     * @mbg.generated Tue Nov 26 07:12:46 HKT 2024
     */
    private Date createdAt;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column follow_up_records.updated_at
     *
     * @mbg.generated Tue Nov 26 07:12:46 HKT 2024
     */
    private Date updatedAt;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column follow_up_records.id
     *
     * @return the value of follow_up_records.id
     *
     * @mbg.generated Tue Nov 26 07:12:46 HKT 2024
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column follow_up_records.id
     *
     * @param id the value for follow_up_records.id
     *
     * @mbg.generated Tue Nov 26 07:12:46 HKT 2024
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column follow_up_records.user_id
     *
     * @return the value of follow_up_records.user_id
     *
     * @mbg.generated Tue Nov 26 07:12:46 HKT 2024
     */
    public Long getUserId() {
        return userId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column follow_up_records.user_id
     *
     * @param userId the value for follow_up_records.user_id
     *
     * @mbg.generated Tue Nov 26 07:12:46 HKT 2024
     */
    public void setUserId(Long userId) {
        this.userId = userId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column follow_up_records.called_number
     *
     * @return the value of follow_up_records.called_number
     *
     * @mbg.generated Tue Nov 26 07:12:46 HKT 2024
     */
    public String getCalledNumber() {
        return calledNumber;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column follow_up_records.called_number
     *
     * @param calledNumber the value for follow_up_records.called_number
     *
     * @mbg.generated Tue Nov 26 07:12:46 HKT 2024
     */
    public void setCalledNumber(String calledNumber) {
        this.calledNumber = calledNumber == null ? null : calledNumber.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column follow_up_records.caller_number
     *
     * @return the value of follow_up_records.caller_number
     *
     * @mbg.generated Tue Nov 26 07:12:46 HKT 2024
     */
    public String getCallerNumber() {
        return callerNumber;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column follow_up_records.caller_number
     *
     * @param callerNumber the value for follow_up_records.caller_number
     *
     * @mbg.generated Tue Nov 26 07:12:46 HKT 2024
     */
    public void setCallerNumber(String callerNumber) {
        this.callerNumber = callerNumber == null ? null : callerNumber.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column follow_up_records.collection_result
     *
     * @return the value of follow_up_records.collection_result
     *
     * @mbg.generated Tue Nov 26 07:12:46 HKT 2024
     */
    public String getCollectionResult() {
        return collectionResult;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column follow_up_records.collection_result
     *
     * @param collectionResult the value for follow_up_records.collection_result
     *
     * @mbg.generated Tue Nov 26 07:12:46 HKT 2024
     */
    public void setCollectionResult(String collectionResult) {
        this.collectionResult = collectionResult == null ? null : collectionResult.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column follow_up_records.missing_contact_result
     *
     * @return the value of follow_up_records.missing_contact_result
     *
     * @mbg.generated Tue Nov 26 07:12:46 HKT 2024
     */
    public String getMissingContactResult() {
        return missingContactResult;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column follow_up_records.missing_contact_result
     *
     * @param missingContactResult the value for follow_up_records.missing_contact_result
     *
     * @mbg.generated Tue Nov 26 07:12:46 HKT 2024
     */
    public void setMissingContactResult(String missingContactResult) {
        this.missingContactResult = missingContactResult == null ? null : missingContactResult.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column follow_up_records.repayment_amount
     *
     * @return the value of follow_up_records.repayment_amount
     *
     * @mbg.generated Tue Nov 26 07:12:46 HKT 2024
     */
    public BigDecimal getRepaymentAmount() {
        return repaymentAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column follow_up_records.repayment_amount
     *
     * @param repaymentAmount the value for follow_up_records.repayment_amount
     *
     * @mbg.generated Tue Nov 26 07:12:46 HKT 2024
     */
    public void setRepaymentAmount(BigDecimal repaymentAmount) {
        this.repaymentAmount = repaymentAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column follow_up_records.repayment_date
     *
     * @return the value of follow_up_records.repayment_date
     *
     * @mbg.generated Tue Nov 26 07:12:46 HKT 2024
     */
    public Date getRepaymentDate() {
        return repaymentDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column follow_up_records.repayment_date
     *
     * @param repaymentDate the value for follow_up_records.repayment_date
     *
     * @mbg.generated Tue Nov 26 07:12:46 HKT 2024
     */
    public void setRepaymentDate(Date repaymentDate) {
        this.repaymentDate = repaymentDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column follow_up_records.is_marked_for_change
     *
     * @return the value of follow_up_records.is_marked_for_change
     *
     * @mbg.generated Tue Nov 26 07:12:46 HKT 2024
     */
    public Boolean getIsMarkedForChange() {
        return isMarkedForChange;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column follow_up_records.is_marked_for_change
     *
     * @param isMarkedForChange the value for follow_up_records.is_marked_for_change
     *
     * @mbg.generated Tue Nov 26 07:12:46 HKT 2024
     */
    public void setIsMarkedForChange(Boolean isMarkedForChange) {
        this.isMarkedForChange = isMarkedForChange;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column follow_up_records.created_at
     *
     * @return the value of follow_up_records.created_at
     *
     * @mbg.generated Tue Nov 26 07:12:46 HKT 2024
     */
    public Date getCreatedAt() {
        return createdAt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column follow_up_records.created_at
     *
     * @param createdAt the value for follow_up_records.created_at
     *
     * @mbg.generated Tue Nov 26 07:12:46 HKT 2024
     */
    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column follow_up_records.updated_at
     *
     * @return the value of follow_up_records.updated_at
     *
     * @mbg.generated Tue Nov 26 07:12:46 HKT 2024
     */
    public Date getUpdatedAt() {
        return updatedAt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column follow_up_records.updated_at
     *
     * @param updatedAt the value for follow_up_records.updated_at
     *
     * @mbg.generated Tue Nov 26 07:12:46 HKT 2024
     */
    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

	public String getTaskPrediction() {
		return taskPrediction;
	}

	public void setTaskPrediction(String taskPrediction) {
		this.taskPrediction = taskPrediction;
	}

	public String getCollectionNotes() {
		return collectionNotes;
	}

	public void setCollectionNotes(String collectionNotes) {
		this.collectionNotes = collectionNotes;
	}

	public Long getOperationId() {
		return operationId;
	}

	public void setOperationId(Long operationId) {
		this.operationId = operationId;
	}

	public String getOperationName() {
		return operationName;
	}

	public void setOperationName(String operationName) {
		this.operationName = operationName;
	}
    
    
    
}