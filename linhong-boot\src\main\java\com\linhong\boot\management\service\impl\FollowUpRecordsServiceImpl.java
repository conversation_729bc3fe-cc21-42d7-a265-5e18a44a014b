package com.linhong.boot.management.service.impl;

import java.util.List;

import cn.hutool.core.util.StrUtil;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.linhong.boot.management.mapper.FollowUpRecordsMapper;
import com.linhong.boot.management.model.entity.FollowUpRecords;
import com.linhong.boot.management.model.query.RecordsQuery;
import com.linhong.boot.management.service.FollowUpRecordsService;

import lombok.RequiredArgsConstructor;

/**
 * 跟进功能业务
 */
@Service
@RequiredArgsConstructor
public class FollowUpRecordsServiceImpl extends ServiceImpl<FollowUpRecordsMapper, FollowUpRecords> implements FollowUpRecordsService {


    @Override
    public boolean saveRecord(FollowUpRecords recordsWithBLOBs) {
        int res = this.baseMapper.insertSelective(recordsWithBLOBs);
        return res > 0 ? true : false;
    }

    @Override
    public IPage<FollowUpRecords> getRecordsPage(RecordsQuery query) {
        int pageNum = query.getPageNum();
        int pageSize = query.getPageSize();
        Page<FollowUpRecords> page = new Page<>(pageNum, pageSize);
        QueryWrapper<FollowUpRecords> records = new QueryWrapper<>();
        records.eq("user_id", query.getUserId());
        if (StrUtil.isNotEmpty(query.getMissingContactResult())) {
            records.eq("missing_contact_result", query.getMissingContactResult());
        }
        records.orderByDesc("updated_at");
        IPage<FollowUpRecords> list = this.getBaseMapper().selectPage(page, records);
        List<FollowUpRecords> followUpRecords = list.getRecords();
        int totalRecords = followUpRecords.size();
        for (int i = 0; i < totalRecords; i++) {
            followUpRecords.get(i).setSort(totalRecords - i);
        }

        return list;
    }

    @Override
    public List<FollowUpRecords> TaskUnFollowDay() {
        return this.baseMapper.TaskUnFollowDay();
    }

}
