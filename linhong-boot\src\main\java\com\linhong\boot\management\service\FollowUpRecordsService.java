package com.linhong.boot.management.service;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.linhong.boot.management.model.entity.FollowUpRecords;
import com.linhong.boot.management.model.query.RecordsQuery;

/**
 * 跟进功能接口
 */
public interface FollowUpRecordsService extends IService<FollowUpRecords>{

	
	public boolean saveRecord(FollowUpRecords recordsWithBLOBs);
	
	
	/**
	 * 跟进分页查询
	 * @return
	 */
	public IPage<FollowUpRecords> getRecordsPage(RecordsQuery query);
	
	/**
	 * 未跟进天数
	 * @return
	 */
	public List<FollowUpRecords> TaskUnFollowDay();
	
}
