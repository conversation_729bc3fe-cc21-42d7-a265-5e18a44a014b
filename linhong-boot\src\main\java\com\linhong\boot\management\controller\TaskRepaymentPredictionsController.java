package com.linhong.boot.management.controller;

import java.util.List;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.linhong.boot.common.annotation.Log;
import com.linhong.boot.common.annotation.RepeatSubmit;
import com.linhong.boot.common.enums.LogModuleEnum;
import com.linhong.boot.common.result.PageResult;
import com.linhong.boot.common.result.Result;
import com.linhong.boot.core.security.util.SecurityUtils;
import com.linhong.boot.management.model.entity.CcUser;
import com.linhong.boot.management.model.entity.TaskRepaymentPredictions;
import com.linhong.boot.management.model.query.TaskRepaymentPredictionQuery;
import com.linhong.boot.management.service.CasePoolService;
import com.linhong.boot.management.service.TaskRepaymentPredictionService;
import com.linhong.boot.system.model.vo.UserProfileVO;
import com.linhong.boot.system.service.DeptService;
import com.linhong.boot.system.service.UserService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;

@Tag(name = "6.任务预测")
@RestController
@RequestMapping("/api/v1/taskRepay")
@RequiredArgsConstructor
public class TaskRepaymentPredictionsController {
	
	private final TaskRepaymentPredictionService taskRepaymentPredictionService;
	private final CasePoolService casePoolService;
	private final DeptService deptService;
	private final UserService userService;
	
	@Operation(summary = "任务预测列表")
	@GetMapping("/page")
	@Log(value = "查看任务预测列表", module = LogModuleEnum.TASKREPAY)
	public PageResult<TaskRepaymentPredictions> getPoolPage(TaskRepaymentPredictionQuery query) {
		IPage<TaskRepaymentPredictions> result = taskRepaymentPredictionService.getTaskRepaymentPage(query);
		return PageResult.success(result);
	}
	
	@Operation(summary = "任务预测列表")
	@GetMapping("/taskPage")
	@Log(value = "查看任务预测汇总列表", module = LogModuleEnum.TASKREPAY)
	public PageResult<TaskRepaymentPredictions> getPoolPageRel(TaskRepaymentPredictionQuery query) {
		IPage<TaskRepaymentPredictions> result = taskRepaymentPredictionService.getTaskPage(query);
		return PageResult.success(result);
	}
	
	@Operation(summary = "新增任务预测")
	@PostMapping("/save")
	@RepeatSubmit
	@Log(value = "新增任务预测", module = LogModuleEnum.TASKREPAY)
	@Transactional
	public Result<?> save(@RequestBody TaskRepaymentPredictions records) {
		CcUser c = casePoolService.getBaseMapper().selectById(records.getCcId());
		if(c==null) {
			return Result.failed("未查询到案件信息");
		}
		String customerName = c.getCustomerName();
		String customerIndexNum = c.getCustomerIndexNumber();
		/*如果该案件没有分配催收人,则默认操作人*/
		String userOrDept = c.getUserOrDept()==null||c.getUserOrDept().equals("")?SecurityUtils.getUserId().toString():c.getUserOrDept();
		UserProfileVO uvo = userService.getUserProfile(Long.valueOf(userOrDept));
		records.setCustomerIndexNumber(customerIndexNum);
		records.setCustomerName(customerName==null?"":customerName);
		records.setOperatorId(uvo.getId().toString());
		records.setUserOrDept(uvo.getNickname());
		records.setDeptName(uvo.getDeptName());
		records.setCcId(records.getCcId());
		records.setBatchNumber(c.getBatchNumber());
		boolean res =taskRepaymentPredictionService.saveTaskRepayment(records);
		return Result.judge(res);
	}
	
	@Operation(summary = "删除任务预测")
	@PostMapping("/delete")
	@RepeatSubmit
	@Log(value = "删除任务预测", module = LogModuleEnum.TASKREPAY)
	@Transactional
	public Result<?> delete(@RequestBody TaskRepaymentPredictions records) {
		if(records.getId()==null)
			return Result.failed("ID不可以为空");
		boolean res =taskRepaymentPredictionService.deletTaskRepayment(records);
		return Result.judge(res);
	}
	
	@Operation(summary = "修改任务预测,跟进id修改")
	@PostMapping("/update")
	@RepeatSubmit
	@Log(value = "修改任务预测", module = LogModuleEnum.TASKREPAY)
	public Result<?> update(@RequestBody TaskRepaymentPredictions records) {
		if(records.getId()==null)
			return Result.failed("ID不可以为空");
		boolean res =taskRepaymentPredictionService.updateTaskRepayment(records);
		return Result.judge(res);
	}

	@Operation(summary = "查询任务预测详情")
	@GetMapping("/detail")
	public Result<TaskRepaymentPredictions> detail(@RequestParam String id) {
		TaskRepaymentPredictions res = taskRepaymentPredictionService.getTaskRepaymentOne(id);
		return Result.success(res);
	}
	
	@Operation(summary = "查询任务预测列表")
	@GetMapping("/list")
	public Result<List<TaskRepaymentPredictions>> list(@RequestParam String id) {
		if(id == null)
			Result.failed("查询数据异常,ID为空");
		List<TaskRepaymentPredictions> contacts = taskRepaymentPredictionService.getTaskRepaymentList(id);
		return Result.success(contacts);
	}
	
}
