package com.linhong.boot.common.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.linhong.boot.system.model.entity.User;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 下拉选项对象
 *
 */
@Schema(description ="下拉选项对象")
@Data
@NoArgsConstructor
public class OptionOfDept<T> {

    public OptionOfDept(T value, String label) {
        this.value = value;
        this.label = label;
    }

    public OptionOfDept(T value, String label, List<OptionOfDept<T>> children) {
        this.value = value;
        this.label = label;
        this.children= children;
    }

    public OptionOfDept(T value, String label, String tag) {
        this.value = value;
        this.label = label;
        this.tag= tag;
    }


    @Schema(description="选项的值")
    private T value;

    @Schema(description="选项的标签")
    private String label;
    
    @Schema(description="部门用户")
    private List<User> users;

    @Schema(description = "标签类型")
    @JsonInclude(value = JsonInclude.Include.NON_EMPTY)
    private String tag;
    
    @Schema(description="子选项列表")
    @JsonInclude(value = JsonInclude.Include.NON_EMPTY)
    private List<OptionOfDept<T>> children;

}