package com.linhong.boot.management.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.linhong.boot.management.model.entity.CcUserWork;
import com.linhong.boot.management.model.entity.CcUserWorkExample;

@Mapper
public interface CcUserWorkMapper extends BaseMapper<CcUserWork>{
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cc_user_work
     *
     * @mbg.generated Mon Nov 25 22:00:16 HKT 2024
     */
    long countByExample(CcUserWorkExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cc_user_work
     *
     * @mbg.generated Mon Nov 25 22:00:16 HKT 2024
     */
    int deleteByExample(CcUserWorkExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cc_user_work
     *
     * @mbg.generated Mon Nov 25 22:00:16 HKT 2024
     */
    int insert(CcUserWork row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cc_user_work
     *
     * @mbg.generated Mon Nov 25 22:00:16 HKT 2024
     */
    int insertSelective(CcUserWork row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cc_user_work
     *
     * @mbg.generated Mon Nov 25 22:00:16 HKT 2024
     */
    List<CcUserWork> selectByExample(CcUserWorkExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cc_user_work
     *
     * @mbg.generated Mon Nov 25 22:00:16 HKT 2024
     */
    int updateByExampleSelective(@Param("row") CcUserWork row, @Param("example") CcUserWorkExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cc_user_work
     *
     * @mbg.generated Mon Nov 25 22:00:16 HKT 2024
     */
    int updateByExample(@Param("row") CcUserWork row, @Param("example") CcUserWorkExample example);
}