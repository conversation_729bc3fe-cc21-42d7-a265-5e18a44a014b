import { queryMode } from "@/enums/optionsConfig/pool";
// 筛选字段
const screenList: any = [
  {
    label: "持代码",
    type: "input",
    options: queryMode,
    fieldKey: "key6-key",
    field: "cardholderCode",
    placeholder: "请输入",
    rules: "",
    labelWidth: "60px",
  },
  {
    label: "客户索引号",
    type: "input",
    options: queryMode,
    fieldKey: "key13-key",
    field: "customerIndexNumber",
    placeholder: "请输入",
    rules: "",
  },
];

// 列表表头数据
const listFields = [
  { label: "持卡人代码", prop: "cardholderCode" },
  { label: "客户索引号", prop: "customerIndexNumber" },
  { label: "客户姓名", prop: "customerName" },
  { label: "案件当前归属", prop: "userOrDept" },
  { label: "跟进状态", prop: "followUpStatus" },
];

export { screenList, listFields };
