package com.linhong.boot.management.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.linhong.boot.management.mapper.RepaymentRecordsMapper;
import com.linhong.boot.management.model.entity.RepaymentRecords;
import com.linhong.boot.management.model.vo.CaseDistributionVO;
import com.linhong.boot.management.model.vo.RepaymentTrendVO;
import com.linhong.boot.management.service.DashboardService;
import lombok.RequiredArgsConstructor;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 仪表板服务实现类
 */
@Service
@RequiredArgsConstructor
public class DashboardServiceImpl implements DashboardService {

    private final RepaymentRecordsMapper repaymentRecordsMapper;
    private final JdbcTemplate jdbcTemplate;

    @Override
    public RepaymentTrendVO getRepaymentTrend(LocalDate startDate, LocalDate endDate, String period, String operatorId) {
        // 查询回款记录
        LambdaQueryWrapper<RepaymentRecords> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.between(RepaymentRecords::getRepaymentDate, startDate, endDate)
                    .eq(RepaymentRecords::getIsDelete, "0");
        
        if (operatorId != null && !operatorId.isEmpty()) {
            queryWrapper.eq(RepaymentRecords::getOperatorId, operatorId);
        }
        
        List<RepaymentRecords> records = repaymentRecordsMapper.selectList(queryWrapper);
        
        // 生成日期范围
        List<LocalDate> dateRange = generateDateRange(startDate, endDate);
        List<String> xAxis = dateRange.stream()
                .map(date -> date.format(DateTimeFormatter.ofPattern("M/dd")))
                .collect(Collectors.toList());
        
        List<String> dates = dateRange.stream()
                .map(date -> date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")))
                .collect(Collectors.toList());
        
        RepaymentTrendVO result = new RepaymentTrendVO();
        result.setXAxis(xAxis);
        result.setDates(dates);
        
        List<RepaymentTrendVO.SeriesData> seriesList = new ArrayList<>();
        
        if ("amount".equals(period)) {
            // 回款金额趋势
            RepaymentTrendVO.SeriesData amountSeries = createAmountSeries(records, dateRange);
            seriesList.add(amountSeries);
        } else if ("count".equals(period)) {
            // 回款户数趋势
            RepaymentTrendVO.SeriesData countSeries = createCountSeries(records, dateRange);
            seriesList.add(countSeries);
        } else if ("recent".equals(period)) {
            // 近7天数据，同时显示金额和户数
            RepaymentTrendVO.SeriesData amountSeries = createAmountSeries(records, dateRange);
            RepaymentTrendVO.SeriesData countSeries = createCountSeries(records, dateRange);
            amountSeries.setName("回款金额(万元)");
            countSeries.setName("回款户数");
            seriesList.add(amountSeries);
            seriesList.add(countSeries);
        }
        
        result.setSeries(seriesList);
        return result;
    }
    
    /**
     * 创建金额系列数据
     */
    private RepaymentTrendVO.SeriesData createAmountSeries(List<RepaymentRecords> records, List<LocalDate> dateRange) {
        Map<LocalDate, BigDecimal> dailyAmounts = records.stream()
                .collect(Collectors.groupingBy(
                        record -> record.getRepaymentDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate(),
                        Collectors.reducing(BigDecimal.ZERO,
                                RepaymentRecords::getRepaymentAmount,
                                BigDecimal::add)
                ));

        List<Number> data = dateRange.stream()
                .map(date -> {
                    BigDecimal amount = dailyAmounts.getOrDefault(date, BigDecimal.ZERO);
                    // 转换为万元
                    return amount.divide(new BigDecimal("10000"), 2, RoundingMode.HALF_UP);
                })
                .collect(Collectors.toList());

        RepaymentTrendVO.SeriesData series = new RepaymentTrendVO.SeriesData();
        series.setName("回款金额(万元)");
        series.setData(data);
        series.setType("line");
        series.setColor("#fbbf24");

        return series;
    }
    
    /**
     * 创建户数系列数据
     */
    private RepaymentTrendVO.SeriesData createCountSeries(List<RepaymentRecords> records, List<LocalDate> dateRange) {
        Map<LocalDate, Long> dailyCounts = records.stream()
                .collect(Collectors.groupingBy(
                        record -> record.getRepaymentDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate(),
                        Collectors.counting()
                ));

        List<Number> data = dateRange.stream()
                .map(date -> dailyCounts.getOrDefault(date, 0L))
                .collect(Collectors.toList());

        RepaymentTrendVO.SeriesData series = new RepaymentTrendVO.SeriesData();
        series.setName("回款户数");
        series.setData(data);
        series.setType("line");
        series.setColor("#4080ff");

        return series;
    }
    
    /**
     * 生成日期范围
     */
    private List<LocalDate> generateDateRange(LocalDate startDate, LocalDate endDate) {
        List<LocalDate> dates = new ArrayList<>();
        LocalDate current = startDate;
        while (!current.isAfter(endDate)) {
            dates.add(current);
            current = current.plusDays(1);
        }
        return dates;
    }

    @Override
    public List<CaseDistributionVO> getCaseDistribution(String type) {
        // 先获取作业清单中的总金额用于计算百分比（只统计work_status IS NULL的记录）
        String totalAmountSql = """
            SELECT COALESCE(SUM(cu.entrust_total_amount), 0) as total_amount
            FROM cc_user_work cuw
            INNER JOIN cc_user cu ON cuw.cc_id = cu.id
            WHERE cuw.work_status IS NULL
            """;

        BigDecimal totalAmount = jdbcTemplate.queryForObject(totalAmountSql, BigDecimal.class);
        if (totalAmount == null || totalAmount.compareTo(BigDecimal.ZERO) == 0) {
            return new ArrayList<>(); // 如果没有数据，返回空列表
        }

        String sql;
        if ("personal".equals(type)) {
            // 个人维度查询 - 只统计作业清单中的案件（work_status IS NULL）
            sql = """
                SELECT
                    COALESCE(su.nickname, '未知用户') as category,
                    COUNT(DISTINCT cuw.cc_id) as case_count,
                    COALESCE(SUM(cu.entrust_total_amount), 0) as total_amount
                FROM cc_user_work cuw
                INNER JOIN cc_user cu ON cuw.cc_id = cu.id
                INNER JOIN sys_user su ON cuw.user_id = su.id
                WHERE cuw.work_status IS NULL
                  AND su.is_deleted = 0 AND su.status = 1
                GROUP BY cuw.user_id, su.nickname
                HAVING total_amount > 0
                ORDER BY total_amount DESC
                """;
        } else {
            // 部门维度查询 - 只统计作业清单中的案件（work_status IS NULL）
            sql = """
                SELECT
                    COALESCE(sd.name, '未知部门') as category,
                    COUNT(DISTINCT cuw.cc_id) as case_count,
                    COALESCE(SUM(cu.entrust_total_amount), 0) as total_amount
                FROM cc_user_work cuw
                INNER JOIN cc_user cu ON cuw.cc_id = cu.id
                INNER JOIN sys_user su ON cuw.user_id = su.id
                INNER JOIN sys_dept sd ON su.dept_id = sd.id
                WHERE cuw.work_status IS NULL
                  AND su.is_deleted = 0 AND su.status = 1 AND sd.is_deleted = 0
                GROUP BY su.dept_id, sd.name
                HAVING total_amount > 0
                ORDER BY total_amount DESC
                """;
        }

        List<Map<String, Object>> results = jdbcTemplate.queryForList(sql);

        // 定义颜色数组
        String[] colors = {"#4080ff", "#23c343", "#ff9a2e", "#e74c3c", "#9b59b6", "#f39c12", "#1abc9c", "#34495e"};

        List<CaseDistributionVO> distributionList = new ArrayList<>();
        for (int i = 0; i < results.size(); i++) {
            Map<String, Object> row = results.get(i);
            CaseDistributionVO vo = new CaseDistributionVO();
            vo.setCategory((String) row.get("category"));
            vo.setCaseCount(((Number) row.get("case_count")).intValue());

            BigDecimal itemAmount = (BigDecimal) row.get("total_amount");
            vo.setTotalAmount(itemAmount);

            // 计算百分比
            double percentage = itemAmount.divide(totalAmount, 4, RoundingMode.HALF_UP)
                    .multiply(new BigDecimal("100"))
                    .doubleValue();
            vo.setPercentage(Math.round(percentage * 100.0) / 100.0); // 保留两位小数

            vo.setColor(colors[i % colors.length]);
            distributionList.add(vo);
        }

        return distributionList;
    }
}
