package com.linhong.boot.common.enums;

import com.linhong.boot.common.base.IBaseEnum;

import lombok.Getter;

/**
 * 失联状态枚举
 */
@Getter
public enum QueryResultEnum implements IBaseEnum<Integer> {

	/**
	 * 已出
	 */
	YI_CHU(0, "已出"),
	/**
	 * 未出
	 */
	WEI_CHU(1, "未出"),
	/**
	 * 未查
	 */
	WEI_CHA(2, "未查"),
	/**
	 * 其他
	 */
	OTHER(3, "其他");

    private final Integer value;


    private final String label;

    QueryResultEnum(Integer value, String label) {
        this.value = value;
        this.label = label;
    }
    


    
}
