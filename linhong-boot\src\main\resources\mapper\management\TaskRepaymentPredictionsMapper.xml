<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.linhong.boot.management.mapper.TaskRepaymentPredictionsMapper">
  <resultMap id="BaseResultMap" type="com.linhong.boot.management.model.entity.TaskRepaymentPredictions">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Dec 17 01:34:51 CST 2024.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="customer_name" jdbcType="VARCHAR" property="customerName" />
    <result column="customer_index_number" jdbcType="VARCHAR" property="customerIndexNumber" />
    <result column="operator_id" jdbcType="VARCHAR" property="operatorId" />
    <result column="operator_name" jdbcType="VARCHAR" property="operatorName" />
    <result column="dept_id" jdbcType="VARCHAR" property="deptId" />
    <result column="dept_name" jdbcType="VARCHAR" property="deptName" />
    <result column="next_month_repayment" jdbcType="BIGINT" property="nextMonthRepayment" />
    <result column="expected_repayment_date" jdbcType="DATE" property="expectedRepaymentDate" />
    <result column="batch_number" jdbcType="VARCHAR" property="batchNumber" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.linhong.boot.management.model.entity.TaskRepaymentPredictions">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Dec 17 01:34:51 CST 2024.
    -->
    <result column="remarks" jdbcType="LONGVARCHAR" property="remarks" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Dec 17 01:34:51 CST 2024.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Dec 17 01:34:51 CST 2024.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Dec 17 01:34:51 CST 2024.
    -->
    id, customer_name, customer_index, operator_id, operator_name, dept_id, dept_name, 
    next_month_repayment, expected_repayment_date
  </sql>
  <sql id="Blob_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Dec 17 01:34:51 CST 2024.
    -->
    remarks
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.linhong.boot.management.model.entity.TaskRepaymentPredictionsExample" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Dec 17 01:34:51 CST 2024.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from task_repayment_predictions
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.linhong.boot.management.model.entity.TaskRepaymentPredictionsExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Dec 17 01:34:51 CST 2024.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from task_repayment_predictions
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Dec 17 01:34:51 CST 2024.
    -->
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from task_repayment_predictions
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Dec 17 01:34:51 CST 2024.
    -->
    delete from task_repayment_predictions
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.linhong.boot.management.model.entity.TaskRepaymentPredictionsExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Dec 17 01:34:51 CST 2024.
    -->
    delete from task_repayment_predictions
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <!-- 
  <insert id="insert" parameterType="com.linhong.boot.management.model.entity.TaskRepaymentPredictions">
    insert into task_repayment_predictions (id, customer_name, customer_index, 
      operator_id, operator_name, dept_id, 
      dept_name, next_month_repayment, expected_repayment_date, 
      remarks,cc_id)
    values (#{id,jdbcType=INTEGER}, #{customerName,jdbcType=VARCHAR}, #{customerIndex,jdbcType=VARCHAR}, 
      #{operatorId,jdbcType=VARCHAR}, #{operatorName,jdbcType=VARCHAR}, #{deptId,jdbcType=VARCHAR}, 
      #{deptName,jdbcType=VARCHAR}, #{nextMonthRepayment,jdbcType=BIGINT}, #{expectedRepaymentDate,jdbcType=DATE}, 
      #{remarks,jdbcType=LONGVARCHAR},#{ccId,jdbcType=VARCHAR})
  </insert>
   -->
  <insert id="insertSelective" parameterType="com.linhong.boot.management.model.entity.TaskRepaymentPredictions">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Dec 17 01:34:51 CST 2024.
    -->
    insert into task_repayment_predictions
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="customerName != null">
        customer_name,
      </if>
      <if test="customerIndex != null">
        customer_index,
      </if>
      <if test="operatorId != null">
        operator_id,
      </if>
      <if test="operatorName != null">
        operator_name,
      </if>
      <if test="deptId != null">
        dept_id,
      </if>
      <if test="deptName != null">
        dept_name,
      </if>
      <if test="nextMonthRepayment != null">
        next_month_repayment,
      </if>
      <if test="expectedRepaymentDate != null">
        expected_repayment_date,
      </if>
      <if test="remarks != null">
        remarks,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="customerName != null">
        #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="customerIndex != null">
        #{customerIndex,jdbcType=VARCHAR},
      </if>
      <if test="operatorId != null">
        #{operatorId,jdbcType=VARCHAR},
      </if>
      <if test="operatorName != null">
        #{operatorName,jdbcType=VARCHAR},
      </if>
      <if test="deptId != null">
        #{deptId,jdbcType=VARCHAR},
      </if>
      <if test="deptName != null">
        #{deptName,jdbcType=VARCHAR},
      </if>
      <if test="nextMonthRepayment != null">
        #{nextMonthRepayment,jdbcType=BIGINT},
      </if>
      <if test="expectedRepaymentDate != null">
        #{expectedRepaymentDate,jdbcType=DATE},
      </if>
      <if test="remarks != null">
        #{remarks,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.linhong.boot.management.model.entity.TaskRepaymentPredictionsExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Dec 17 01:34:51 CST 2024.
    -->
    select count(*) from task_repayment_predictions
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Dec 17 01:34:51 CST 2024.
    -->
    update task_repayment_predictions
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=INTEGER},
      </if>
      <if test="row.customerName != null">
        customer_name = #{row.customerName,jdbcType=VARCHAR},
      </if>
      <if test="row.customerIndex != null">
        customer_index = #{row.customerIndex,jdbcType=VARCHAR},
      </if>
      <if test="row.operatorId != null">
        operator_id = #{row.operatorId,jdbcType=VARCHAR},
      </if>
      <if test="row.operatorName != null">
        operator_name = #{row.operatorName,jdbcType=VARCHAR},
      </if>
      <if test="row.deptId != null">
        dept_id = #{row.deptId,jdbcType=VARCHAR},
      </if>
      <if test="row.deptName != null">
        dept_name = #{row.deptName,jdbcType=VARCHAR},
      </if>
      <if test="row.nextMonthRepayment != null">
        next_month_repayment = #{row.nextMonthRepayment,jdbcType=BIGINT},
      </if>
      <if test="row.expectedRepaymentDate != null">
        expected_repayment_date = #{row.expectedRepaymentDate,jdbcType=DATE},
      </if>
      <if test="row.remarks != null">
        remarks = #{row.remarks,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Dec 17 01:34:51 CST 2024.
    -->
    update task_repayment_predictions
    set id = #{row.id,jdbcType=INTEGER},
      customer_name = #{row.customerName,jdbcType=VARCHAR},
      customer_index = #{row.customerIndex,jdbcType=VARCHAR},
      operator_id = #{row.operatorId,jdbcType=VARCHAR},
      operator_name = #{row.operatorName,jdbcType=VARCHAR},
      dept_id = #{row.deptId,jdbcType=VARCHAR},
      dept_name = #{row.deptName,jdbcType=VARCHAR},
      next_month_repayment = #{row.nextMonthRepayment,jdbcType=BIGINT},
      expected_repayment_date = #{row.expectedRepaymentDate,jdbcType=DATE},
      remarks = #{row.remarks,jdbcType=LONGVARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Dec 17 01:34:51 CST 2024.
    -->
    update task_repayment_predictions
    set id = #{row.id,jdbcType=INTEGER},
      customer_name = #{row.customerName,jdbcType=VARCHAR},
      customer_index = #{row.customerIndex,jdbcType=VARCHAR},
      operator_id = #{row.operatorId,jdbcType=VARCHAR},
      operator_name = #{row.operatorName,jdbcType=VARCHAR},
      dept_id = #{row.deptId,jdbcType=VARCHAR},
      dept_name = #{row.deptName,jdbcType=VARCHAR},
      next_month_repayment = #{row.nextMonthRepayment,jdbcType=BIGINT},
      expected_repayment_date = #{row.expectedRepaymentDate,jdbcType=DATE}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.linhong.boot.management.model.entity.TaskRepaymentPredictions">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Dec 17 01:34:51 CST 2024.
    -->
    update task_repayment_predictions
    <set>
      <if test="customerName != null">
        customer_name = #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="customerIndex != null">
        customer_index = #{customerIndex,jdbcType=VARCHAR},
      </if>
      <if test="operatorId != null">
        operator_id = #{operatorId,jdbcType=VARCHAR},
      </if>
      <if test="operatorName != null">
        operator_name = #{operatorName,jdbcType=VARCHAR},
      </if>
      <if test="deptId != null">
        dept_id = #{deptId,jdbcType=VARCHAR},
      </if>
      <if test="deptName != null">
        dept_name = #{deptName,jdbcType=VARCHAR},
      </if>
      <if test="nextMonthRepayment != null">
        next_month_repayment = #{nextMonthRepayment,jdbcType=BIGINT},
      </if>
      <if test="expectedRepaymentDate != null">
        expected_repayment_date = #{expectedRepaymentDate,jdbcType=DATE},
      </if>
      <if test="remarks != null">
        remarks = #{remarks,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.linhong.boot.management.model.entity.TaskRepaymentPredictions">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Dec 17 01:34:51 CST 2024.
    -->
    update task_repayment_predictions
    set customer_name = #{customerName,jdbcType=VARCHAR},
      customer_index = #{customerIndex,jdbcType=VARCHAR},
      operator_id = #{operatorId,jdbcType=VARCHAR},
      operator_name = #{operatorName,jdbcType=VARCHAR},
      dept_id = #{deptId,jdbcType=VARCHAR},
      dept_name = #{deptName,jdbcType=VARCHAR},
      next_month_repayment = #{nextMonthRepayment,jdbcType=BIGINT},
      expected_repayment_date = #{expectedRepaymentDate,jdbcType=DATE},
      remarks = #{remarks,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.linhong.boot.management.model.entity.TaskRepaymentPredictions">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Dec 17 01:34:51 CST 2024.
    -->
    update task_repayment_predictions
    set customer_name = #{customerName,jdbcType=VARCHAR},
      customer_index = #{customerIndex,jdbcType=VARCHAR},
      operator_id = #{operatorId,jdbcType=VARCHAR},
      operator_name = #{operatorName,jdbcType=VARCHAR},
      dept_id = #{deptId,jdbcType=VARCHAR},
      dept_name = #{deptName,jdbcType=VARCHAR},
      next_month_repayment = #{nextMonthRepayment,jdbcType=BIGINT},
      expected_repayment_date = #{expectedRepaymentDate,jdbcType=DATE}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>